version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:5.0
    container_name: alaa-courses-db
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password123}
      MONGO_INITDB_DATABASE: alaa-courses
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - alaa-courses-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API
  backend:
    build: .
    container_name: alaa-courses-backend
    restart: unless-stopped
    ports:
      - "${PORT:-5000}:5000"
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password123}@mongodb:27017/alaa-courses?authSource=admin
      JWT_SECRET: ${JWT_SECRET:-alaa_abdulhameed_courses_secret_key_2024}
      PORT: 5000
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - alaa-courses-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: alaa-courses-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:5000/api}
      REACT_APP_TITLE: علاء عبد الحميد - منصة الكورسات
      NODE_ENV: ${NODE_ENV:-production}
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - alaa-courses-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: alaa-courses-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - alaa-courses-network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data/mongodb}

networks:
  alaa-courses-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
