# 🏫 SKILLS WORLD ACADEMY
## نظام إدارة الأكاديمية الاحترافي مع التكامل المزدوج Firebase + Supabase

[![Firebase](https://img.shields.io/badge/Firebase-FFCA28?style=for-the-badge&logo=firebase&logoColor=black)](https://firebase.google.com/)
[![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white)](https://supabase.com/)
[![React](https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Material-UI](https://img.shields.io/badge/Material--UI-0081CB?style=for-the-badge&logo=material-ui&logoColor=white)](https://mui.com/)

### 🌟 نظرة عامة

**SKILLS WORLD ACADEMY** هو نظام إدارة أكاديمية متطور يجمع بين قوة Firebase و Supabase لتوفير تجربة تعليمية شاملة ومتطورة. النظام مصمم خصيصاً لإدارة الكورسات التعليمية والطلاب مع مزامنة فورية وأداء عالي.

### 🚀 المميزات الرئيسية

#### 🔥 Firebase Integration
- **المصادقة الآمنة**: نظام تسجيل دخول متطور
- **الاستضافة السريعة**: نشر سريع وموثوق
- **التخزين السحابي**: لحفظ الملفات والصور
- **الإشعارات الفورية**: تنبيهات في الوقت الفعلي

#### 🐘 Supabase Integration  
- **قاعدة بيانات PostgreSQL**: قوية ومرنة
- **المزامنة الفورية**: Real-time subscriptions
- **الاستعلامات المعقدة**: SQL كامل
- **الأمان المتقدم**: Row Level Security (RLS)

#### 📚 إدارة الكورسات
- إضافة وتحرير الكورسات
- رفع الفيديوهات والمواد التعليمية
- تصنيف الكورسات حسب المستوى
- إحصائيات مفصلة لكل كورس

#### 👨‍🎓 إدارة الطلاب
- تسجيل الطلاب الجدد
- توليد أكواد طلاب فريدة
- متابعة تقدم الطلاب
- إدارة التسجيلات في الكورسات

#### 📊 لوحة تحكم شاملة
- إحصائيات في الوقت الفعلي
- تقارير مفصلة
- مراقبة النشاط
- اختبار النظام المدمج

### 🛠️ التقنيات المستخدمة

```json
{
  "Frontend": {
    "Framework": "React.js 18",
    "UI Library": "Material-UI (MUI) 5",
    "State Management": "React Hooks",
    "Routing": "React Router 6",
    "Notifications": "React Hot Toast"
  },
  "Backend": {
    "Authentication": "Firebase Auth",
    "Database": "Supabase PostgreSQL",
    "Real-time": "Supabase Subscriptions",
    "Storage": "Firebase Storage"
  },
  "Deployment": {
    "Hosting": "Firebase Hosting",
    "Database": "Supabase Cloud",
    "CDN": "Firebase CDN"
  }
}
```

### 📋 متطلبات النظام

- **Node.js**: 16.0.0 أو أحدث
- **npm**: 8.0.0 أو أحدث
- **Firebase CLI**: أحدث إصدار
- **حساب Firebase**: مع مشروع مُعد
- **حساب Supabase**: مع قاعدة بيانات مُعدة

### 🚀 التثبيت والإعداد

#### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/skills-world-academy.git
cd skills-world-academy
```

#### 2. تثبيت التبعيات
```bash
cd frontend
npm install
```

#### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعبئة المتغيرات المطلوبة
```

#### 4. إعداد Supabase
```sql
-- تنفيذ في Supabase SQL Editor
-- 1. database/supabase-schema.sql
-- 2. database/supabase-functions.sql
```

#### 5. تشغيل المشروع محلياً
```bash
npm start
```

### 🌐 النشر

#### النشر التلقائي
```bash
# تشغيل سكريبت النشر الاحترافي
./deploy.sh
```

#### النشر اليدوي
```bash
# بناء المشروع
npm run build

# النشر على Firebase
firebase deploy --only hosting
```

### 🧪 الاختبار

النظام يتضمن مجموعة اختبارات شاملة:

```bash
# اختبار التكامل
npm test

# اختبار النظام من لوحة التحكم
# انتقل إلى: لوحة التحكم > اختبار النظام
```

### 📁 هيكل المشروع

```
skills-world-academy/
├── frontend/
│   ├── src/
│   │   ├── components/          # مكونات React
│   │   ├── services/           # خدمات التكامل
│   │   ├── firebase/           # إعدادات Firebase
│   │   ├── supabase/          # إعدادات Supabase
│   │   └── tests/             # ملفات الاختبار
│   ├── public/                # الملفات العامة
│   └── package.json           # تبعيات المشروع
├── database/
│   ├── supabase-schema.sql    # هيكل قاعدة البيانات
│   └── supabase-functions.sql # دوال SQL
├── deploy.sh                  # سكريبت النشر
├── firebase.json             # إعدادات Firebase
└── README.md                 # هذا الملف
```

### 🔧 الإعدادات المتقدمة

#### Firebase Configuration
```javascript
// src/firebase/config.js
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "marketwise-academy-qhizq.firebaseapp.com",
  projectId: "marketwise-academy-qhizq",
  // ...
};
```

#### Supabase Configuration
```javascript
// src/supabase/config.js
const supabaseUrl = 'https://your-project.supabase.co'
const supabaseKey = 'your-anon-key'
```

### 📊 الأداء والمراقبة

- **سرعة التحميل**: أقل من 3 ثواني
- **المزامنة الفورية**: أقل من 100ms
- **معدل التوفر**: 99.9%
- **الأمان**: RLS + Firebase Auth

### 🛡️ الأمان

- **Row Level Security (RLS)**: حماية على مستوى الصفوف
- **Firebase Authentication**: مصادقة آمنة
- **HTTPS**: تشفير كامل للبيانات
- **API Keys**: حماية مفاتيح API

### 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### 📞 الدعم والتواصل

**المطور:** علاء عبد الحميد  
**البريد الإلكتروني:** ALAA <EMAIL>  
**الهاتف:** **********  

**روابط المشروع:**
- 🌐 **الموقع المباشر**: https://marketwise-academy-qhizq.web.app
- 🔥 **Firebase Console**: https://console.firebase.google.com/project/marketwise-academy-qhizq
- 🐘 **Supabase Dashboard**: [سيتم إضافته بعد الإعداد]

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

### 🙏 شكر وتقدير

- **Firebase Team**: للمنصة الرائعة
- **Supabase Team**: لقاعدة البيانات المتطورة  
- **Material-UI Team**: لمكتبة UI الجميلة
- **React Team**: للإطار المتميز

---

<div align="center">

**صُنع بـ ❤️ في المملكة العربية السعودية**

[![Firebase](https://img.shields.io/badge/Powered%20by-Firebase-FFCA28?style=flat-square&logo=firebase)](https://firebase.google.com/)
[![Supabase](https://img.shields.io/badge/Database-Supabase-3ECF8E?style=flat-square&logo=supabase)](https://supabase.com/)

</div>
