// خدمة إدارة البيانات المحلية (localStorage)

/**
 * حفظ بيانات المستخدم في localStorage
 * @param {string} userId - معرف المستخدم
 * @param {Object} userData - بيانات المستخدم
 * @returns {boolean} true إذا تم الحفظ بنجاح
 */
export const saveUserData = (userId, userData) => {
  try {
    const timestamp = new Date().toISOString();
    const userDataWithTimestamp = {
      ...userData,
      updatedAt: timestamp,
      lastSavedAt: timestamp
    };

    // حفظ البيانات الأساسية
    localStorage.setItem('user', JSON.stringify(userDataWithTimestamp));
    
    // حفظ نسخة احتياطية مع معرف المستخدم
    localStorage.setItem(`user_${userId}`, JSON.stringify(userDataWithTimestamp));
    
    console.log('تم حفظ بيانات المستخدم محلياً:', userDataWithTimestamp);
    return true;
  } catch (error) {
    console.error('خطأ في حفظ بيانات المستخدم محلياً:', error);
    return false;
  }
};

/**
 * جلب بيانات المستخدم من localStorage
 * @param {string} userId - معرف المستخدم
 * @returns {Object|null} بيانات المستخدم أو null
 */
export const getUserData = (userId) => {
  try {
    // محاولة جلب البيانات الأساسية أولاً
    let userData = localStorage.getItem('user');
    
    if (!userData && userId) {
      // محاولة جلب النسخة الاحتياطية
      userData = localStorage.getItem(`user_${userId}`);
    }
    
    if (userData) {
      const parsedData = JSON.parse(userData);
      console.log('تم جلب بيانات المستخدم محلياً:', parsedData);
      return parsedData;
    }
    
    return null;
  } catch (error) {
    console.error('خطأ في جلب بيانات المستخدم محلياً:', error);
    return null;
  }
};

/**
 * تحديث بيانات المستخدم في localStorage
 * @param {string} userId - معرف المستخدم
 * @param {Object} updates - التحديثات
 * @returns {boolean} true إذا تم التحديث بنجاح
 */
export const updateUserData = (userId, updates) => {
  try {
    const currentData = getUserData(userId) || {};
    const updatedData = {
      ...currentData,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    return saveUserData(userId, updatedData);
  } catch (error) {
    console.error('خطأ في تحديث بيانات المستخدم محلياً:', error);
    return false;
  }
};

/**
 * حذف بيانات المستخدم من localStorage
 * @param {string} userId - معرف المستخدم
 * @returns {boolean} true إذا تم الحذف بنجاح
 */
export const clearUserData = (userId) => {
  try {
    localStorage.removeItem('user');
    if (userId) {
      localStorage.removeItem(`user_${userId}`);
    }
    console.log('تم حذف بيانات المستخدم محلياً');
    return true;
  } catch (error) {
    console.error('خطأ في حذف بيانات المستخدم محلياً:', error);
    return false;
  }
};

/**
 * حفظ إعدادات المستخدم
 * @param {string} userId - معرف المستخدم
 * @param {Object} settings - الإعدادات
 * @returns {boolean} true إذا تم الحفظ بنجاح
 */
export const saveUserSettings = (userId, settings) => {
  try {
    const settingsKey = `settings_${userId}`;
    const settingsWithTimestamp = {
      ...settings,
      updatedAt: new Date().toISOString()
    };
    
    localStorage.setItem(settingsKey, JSON.stringify(settingsWithTimestamp));
    console.log('تم حفظ إعدادات المستخدم محلياً:', settingsWithTimestamp);
    return true;
  } catch (error) {
    console.error('خطأ في حفظ إعدادات المستخدم محلياً:', error);
    return false;
  }
};

/**
 * جلب إعدادات المستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Object|null} الإعدادات أو null
 */
export const getUserSettings = (userId) => {
  try {
    const settingsKey = `settings_${userId}`;
    const settings = localStorage.getItem(settingsKey);
    
    if (settings) {
      return JSON.parse(settings);
    }
    
    return null;
  } catch (error) {
    console.error('خطأ في جلب إعدادات المستخدم محلياً:', error);
    return null;
  }
};

/**
 * حفظ تقدم المستخدم في الكورسات
 * @param {string} userId - معرف المستخدم
 * @param {string} courseId - معرف الكورس
 * @param {Object} progress - بيانات التقدم
 * @returns {boolean} true إذا تم الحفظ بنجاح
 */
export const saveUserProgress = (userId, courseId, progress) => {
  try {
    const progressKey = `progress_${userId}_${courseId}`;
    const progressWithTimestamp = {
      ...progress,
      userId,
      courseId,
      updatedAt: new Date().toISOString()
    };
    
    localStorage.setItem(progressKey, JSON.stringify(progressWithTimestamp));
    console.log('تم حفظ تقدم المستخدم محلياً:', progressWithTimestamp);
    return true;
  } catch (error) {
    console.error('خطأ في حفظ تقدم المستخدم محلياً:', error);
    return false;
  }
};

/**
 * جلب تقدم المستخدم في كورس معين
 * @param {string} userId - معرف المستخدم
 * @param {string} courseId - معرف الكورس
 * @returns {Object|null} بيانات التقدم أو null
 */
export const getUserProgress = (userId, courseId) => {
  try {
    const progressKey = `progress_${userId}_${courseId}`;
    const progress = localStorage.getItem(progressKey);
    
    if (progress) {
      return JSON.parse(progress);
    }
    
    return null;
  } catch (error) {
    console.error('خطأ في جلب تقدم المستخدم محلياً:', error);
    return null;
  }
};

/**
 * جلب جميع تقدم المستخدم في الكورسات
 * @param {string} userId - معرف المستخدم
 * @returns {Array} قائمة بتقدم المستخدم
 */
export const getAllUserProgress = (userId) => {
  try {
    const progressList = [];
    const prefix = `progress_${userId}_`;
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        const progress = localStorage.getItem(key);
        if (progress) {
          progressList.push(JSON.parse(progress));
        }
      }
    }
    
    return progressList;
  } catch (error) {
    console.error('خطأ في جلب جميع تقدم المستخدم محلياً:', error);
    return [];
  }
};

/**
 * تنظيف البيانات القديمة من localStorage
 * @param {number} daysOld - عدد الأيام للاحتفاظ بالبيانات
 * @returns {boolean} true إذا تم التنظيف بنجاح
 */
export const cleanupOldData = (daysOld = 30) => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    const keysToRemove = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('user_') || key.startsWith('progress_') || key.startsWith('settings_'))) {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            const parsedData = JSON.parse(data);
            const updatedAt = new Date(parsedData.updatedAt);
            
            if (updatedAt < cutoffDate) {
              keysToRemove.push(key);
            }
          } catch (parseError) {
            // إذا كانت البيانات تالفة، احذفها
            keysToRemove.push(key);
          }
        }
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    if (keysToRemove.length > 0) {
      console.log(`تم حذف ${keysToRemove.length} عنصر قديم من localStorage`);
    }
    
    return true;
  } catch (error) {
    console.error('خطأ في تنظيف البيانات القديمة:', error);
    return false;
  }
};

// تصدير جميع الدوال
export default {
  saveUserData,
  getUserData,
  updateUserData,
  clearUserData,
  saveUserSettings,
  getUserSettings,
  saveUserProgress,
  getUserProgress,
  getAllUserProgress,
  cleanupOldData
};
