import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  <PERSON>rror as <PERSON>rrorIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';
import { 
  testDirectConnection, 
  diagnoseProblem, 
  quickFix,
  testAllTables 
} from '../utils/supabaseConnectionFix';

const SupabaseConnectionTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState(null);
  const [testResults, setTestResults] = useState(null);
  const [autoTestDone, setAutoTestDone] = useState(false);

  // اختبار تلقائي عند تحميل المكون
  useEffect(() => {
    if (!autoTestDone) {
      runAutoTest();
    }
  }, [autoTestDone]);

  const runAutoTest = async () => {
    setIsLoading(true);
    try {
      const result = await testDirectConnection();
      setConnectionStatus(result);
      setAutoTestDone(true);
    } catch (error) {
      setConnectionStatus({
        success: false,
        error: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runFullDiagnosis = async () => {
    setIsLoading(true);
    setTestResults(null);
    
    try {
      const diagnosis = await diagnoseProblem();
      setTestResults(diagnosis);
      
      // تحديث حالة الاتصال
      const overallSuccess = diagnosis.tests.every(test => test.success);
      setConnectionStatus({
        success: overallSuccess,
        message: overallSuccess ? 'جميع الاختبارات نجحت' : 'بعض الاختبارات فشلت'
      });
      
    } catch (error) {
      setTestResults({
        error: error.message,
        tests: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runQuickFix = async () => {
    setIsLoading(true);
    
    try {
      const fixResult = await quickFix();
      
      if (fixResult.fixed) {
        setConnectionStatus({
          success: true,
          message: 'تم إصلاح المشكلة بنجاح!'
        });
      } else {
        setConnectionStatus({
          success: false,
          message: 'فشل في إصلاح المشكلة',
          error: fixResult.finalTest?.error
        });
      }
      
      setTestResults(fixResult.diagnosis);
      
    } catch (error) {
      setConnectionStatus({
        success: false,
        error: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (success) => {
    return success ? 'success' : 'error';
  };

  const getStatusIcon = (success) => {
    return success ? <CheckIcon color="success" /> : <ErrorIcon color="error" />;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ mb: 3, fontWeight: 'bold' }}>
        🔗 اختبار اتصال Supabase
      </Typography>

      {/* حالة الاتصال */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              حالة الاتصال
            </Typography>
            {isLoading && <CircularProgress size={24} />}
          </Box>

          {connectionStatus && (
            <Alert 
              severity={connectionStatus.success ? 'success' : 'error'}
              icon={getStatusIcon(connectionStatus.success)}
              sx={{ mb: 2 }}
            >
              <Typography variant="body1">
                {connectionStatus.success ? '✅ متصل بنجاح' : '❌ فشل الاتصال'}
              </Typography>
              {connectionStatus.message && (
                <Typography variant="body2">
                  {connectionStatus.message}
                </Typography>
              )}
              {connectionStatus.error && (
                <Typography variant="body2" color="error">
                  خطأ: {connectionStatus.error}
                </Typography>
              )}
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={runAutoTest}
              disabled={isLoading}
            >
              اختبار سريع
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<PlayIcon />}
              onClick={runFullDiagnosis}
              disabled={isLoading}
            >
              تشخيص شامل
            </Button>
            
            <Button
              variant="contained"
              color="warning"
              startIcon={<RefreshIcon />}
              onClick={runQuickFix}
              disabled={isLoading}
            >
              إصلاح سريع
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* نتائج الاختبار */}
      {testResults && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              📊 نتائج التشخيص
            </Typography>

            {testResults.summary && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  <strong>الملخص:</strong>
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip 
                    label={`إجمالي: ${testResults.summary.total}`} 
                    variant="outlined" 
                  />
                  <Chip 
                    label={`نجح: ${testResults.summary.successful}`} 
                    color="success" 
                  />
                  <Chip 
                    label={`فشل: ${testResults.summary.failed}`} 
                    color="error" 
                  />
                  <Chip 
                    label={`معدل النجاح: ${testResults.summary.successRate}`} 
                    color={testResults.summary.successful === testResults.summary.total ? 'success' : 'warning'}
                  />
                </Box>
              </Box>
            )}

            {testResults.tests && testResults.tests.length > 0 && (
              <List>
                {testResults.tests.map((test, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      {getStatusIcon(test.success)}
                    </ListItemIcon>
                    <ListItemText
                      primary={test.name}
                      secondary={
                        test.error ? (
                          <Typography variant="body2" color="error">
                            خطأ: {test.error}
                          </Typography>
                        ) : (
                          <Typography variant="body2" color="success.main">
                            نجح الاختبار
                          </Typography>
                        )
                      }
                    />
                    <Chip
                      label={test.success ? 'نجح' : 'فشل'}
                      color={getStatusColor(test.success)}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
            )}

            {testResults.error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                خطأ في التشخيص: {testResults.error}
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* معلومات إضافية */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            ℹ️ معلومات الاتصال
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>URL:</strong> https://auwpeiicfwcysoexoogf.supabase.co
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>المشروع:</strong> skills-world-academy
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>المنطقة:</strong> US East 1
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SupabaseConnectionTest;
