import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  TextField,
  Button,
  Alert,
  CircularProgress,
  InputAdornment,
  Link,
  Divider,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  Phone,
  Send,
  ArrowBack,
  Security,
  CheckCircle,
  Timer
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

const ForgotPassword = () => {
  const navigate = useNavigate();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(0); // 0: إدخال الرقم, 1: تأكيد الإرسال, 2: مكتمل
  const [countdown, setCountdown] = useState(0);
  const [lastSentTime, setLastSentTime] = useState(null);

  // التحقق من صحة رقم الهاتف السعودي
  const validateSaudiPhone = (phone) => {
    // إزالة المسافات والرموز
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    // التحقق من الأنماط المقبولة للأرقام السعودية
    const saudiPatterns = [
      /^05\d{8}$/, // 05xxxxxxxx
      /^\+9665\d{8}$/, // +9665xxxxxxxx
      /^9665\d{8}$/, // 9665xxxxxxxx
      /^00966\d{9}$/ // 00966xxxxxxxxx
    ];
    
    return saudiPatterns.some(pattern => pattern.test(cleanPhone));
  };

  // تنسيق رقم الهاتف للعرض
  const formatPhoneNumber = (phone) => {
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    if (cleanPhone.startsWith('05') && cleanPhone.length === 10) {
      return cleanPhone.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
    }
    return phone;
  };

  // تحويل رقم الهاتف للصيغة الدولية
  const normalizePhoneNumber = (phone) => {
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    if (cleanPhone.startsWith('05')) {
      return '+966' + cleanPhone.substring(1);
    } else if (cleanPhone.startsWith('+966')) {
      return cleanPhone;
    } else if (cleanPhone.startsWith('966')) {
      return '+' + cleanPhone;
    } else if (cleanPhone.startsWith('00966')) {
      return '+' + cleanPhone.substring(2);
    }
    
    return cleanPhone;
  };

  // العد التنازلي لمنع الإرسال المتكرر
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // التحقق من آخر وقت إرسال
  useEffect(() => {
    const lastSent = localStorage.getItem('lastPasswordResetSent');
    if (lastSent) {
      const timeDiff = Date.now() - parseInt(lastSent);
      const remainingTime = 60 - Math.floor(timeDiff / 1000);
      if (remainingTime > 0) {
        setCountdown(remainingTime);
        setLastSentTime(new Date(parseInt(lastSent)));
      }
    }
  }, []);

  // إرسال طلب استرداد كلمة المرور
  const handleSendPassword = async () => {
    // التحقق من صحة رقم الهاتف
    if (!validateSaudiPhone(phoneNumber)) {
      toast.error('يرجى إدخال رقم هاتف سعودي صحيح');
      return;
    }

    // التحقق من العد التنازلي
    if (countdown > 0) {
      toast.error(`يرجى الانتظار ${countdown} ثانية قبل المحاولة مرة أخرى`);
      return;
    }

    setLoading(true);
    
    try {
      const normalizedPhone = normalizePhoneNumber(phoneNumber);
      
      // محاكاة إرسال كلمة المرور (نسخة مبسطة للاختبار)
      console.log('📱 محاكاة إرسال SMS إلى:', normalizedPhone);
      console.log('🔑 كلمة المرور: Admin123!');

      // التحقق من أن الرقم مطابق لرقم المدير
      const adminPhone = '+966506747770';

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 2000));

      let result;
      if (normalizedPhone === adminPhone) {
        result = {
          success: true,
          message: 'تم إرسال كلمة المرور بنجاح',
          mock: true
        };
      } else {
        result = {
          success: false,
          error: 'PHONE_NOT_FOUND',
          message: 'رقم الهاتف غير مطابق لرقم هاتف المدير المسجل'
        };
      }

      if (result.success) {
        // نجح الإرسال
        setStep(1);
        setCountdown(60);
        setLastSentTime(new Date());
        localStorage.setItem('lastPasswordResetSent', Date.now().toString());

        if (result.mock) {
          toast.success('تم إرسال كلمة المرور بنجاح (وضع الاختبار)');
          console.log('🔐 كلمة المرور: Admin123!');
          console.log('📱 الرقم: ' + normalizedPhone);
        } else {
          toast.success('تم إرسال كلمة المرور إلى رقم هاتفك');
        }

        // الانتقال للخطوة النهائية بعد 3 ثوان
        setTimeout(() => {
          setStep(2);
        }, 3000);
        
      } else {
        // فشل الإرسال
        if (result.error === 'PHONE_NOT_FOUND') {
          toast.error('رقم الهاتف غير مطابق لرقم هاتف المدير المسجل (0506747770)');
        } else if (result.error === 'RATE_LIMITED') {
          toast.error('تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة لاحقاً');
          setCountdown(result.retryAfter || 60);
        } else {
          toast.error(result.message || 'حدث خطأ في إرسال الرسالة');
        }
      }
    } catch (error) {
      console.error('خطأ في إرسال طلب استرداد كلمة المرور:', error);
      toast.error('حدث خطأ في الاتصال. يرجى المحاولة لاحقاً');
    } finally {
      setLoading(false);
    }
  };

  // العودة لصفحة تسجيل الدخول
  const handleBackToLogin = () => {
    navigate('/admin-login');
  };

  // إعادة المحاولة
  const handleRetry = () => {
    setStep(0);
    setPhoneNumber('');
  };

  const steps = ['إدخال رقم الهاتف', 'إرسال الرسالة', 'مكتمل'];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
        direction: 'rtl'
      }}
    >
      <Card
        sx={{
          maxWidth: 500,
          width: '100%',
          borderRadius: 3,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }}
      >
        {/* رأس البطاقة */}
        <Box
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            color: 'white',
            p: 3,
            textAlign: 'center'
          }}
        >
          <Security sx={{ fontSize: 48, mb: 1 }} />
          <Typography variant="h5" fontWeight="bold">
            استرداد كلمة المرور
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
            SKILLS WORLD ACADEMY
          </Typography>
        </Box>

        <CardContent sx={{ p: 4 }}>
          {/* مؤشر التقدم */}
          <Stepper activeStep={step} sx={{ mb: 4 }}>
            {steps.map((label, index) => (
              <Step key={index}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* الخطوة 1: إدخال رقم الهاتف */}
          {step === 0 && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  أدخل رقم هاتف المدير المسجل في النظام لاستلام كلمة المرور
                </Typography>
              </Alert>

              <TextField
                fullWidth
                label="رقم الهاتف"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="05xxxxxxxx"
                variant="outlined"
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone color="primary" />
                    </InputAdornment>
                  ),
                }}
                helperText="مثال: 0506747770 أو +966506747770"
                error={phoneNumber && !validateSaudiPhone(phoneNumber)}
              />

              {countdown > 0 && (
                <Alert severity="warning" sx={{ mb: 3 }}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Timer />
                    <Typography variant="body2">
                      يرجى الانتظار {countdown} ثانية قبل المحاولة مرة أخرى
                    </Typography>
                  </Box>
                </Alert>
              )}

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleSendPassword}
                disabled={loading || !validateSaudiPhone(phoneNumber) || countdown > 0}
                startIcon={loading ? <CircularProgress size={20} /> : <Send />}
                sx={{
                  py: 1.5,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '1.1rem',
                  mb: 2
                }}
              >
                {loading ? 'جاري الإرسال...' : 'إرسال كلمة المرور'}
              </Button>
            </Box>
          )}

          {/* الخطوة 2: تأكيد الإرسال */}
          {step === 1 && (
            <Box textAlign="center">
              <CircularProgress size={60} sx={{ mb: 3, color: 'success.main' }} />
              <Typography variant="h6" gutterBottom>
                جاري إرسال الرسالة...
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                يتم إرسال كلمة المرور إلى رقم {formatPhoneNumber(phoneNumber)}
              </Typography>
              <Alert severity="info">
                <Typography variant="body2">
                  قد تستغرق الرسالة بضع دقائق للوصول
                </Typography>
              </Alert>
            </Box>
          )}

          {/* الخطوة 3: مكتمل */}
          {step === 2 && (
            <Box textAlign="center">
              <CheckCircle sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom color="success.main">
                تم إرسال كلمة المرور بنجاح!
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                تحقق من رسائل SMS على رقم {formatPhoneNumber(phoneNumber)}
              </Typography>
              
              <Alert severity="success" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  استخدم كلمة المرور المرسلة لتسجيل الدخول
                </Typography>
              </Alert>

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleBackToLogin}
                sx={{
                  py: 1.5,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '1.1rem',
                  mb: 2
                }}
              >
                العودة لتسجيل الدخول
              </Button>

              <Button
                fullWidth
                variant="outlined"
                onClick={handleRetry}
                sx={{ textTransform: 'none' }}
              >
                إرسال مرة أخرى
              </Button>
            </Box>
          )}

          {/* روابط إضافية */}
          {step === 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              
              <Box textAlign="center">
                <Link
                  component="button"
                  variant="body2"
                  onClick={handleBackToLogin}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 1,
                    textDecoration: 'none',
                    color: 'primary.main',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }}
                >
                  <ArrowBack fontSize="small" />
                  العودة لتسجيل الدخول
                </Link>
              </Box>

              <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="caption" color="text.secondary" display="block" textAlign="center">
                  للمساعدة، تواصل مع الدعم الفني
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" textAlign="center">
                  البريد الإلكتروني: ALAA <EMAIL>
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" textAlign="center">
                  الهاتف: 0506747770
                </Typography>
              </Box>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default ForgotPassword;
