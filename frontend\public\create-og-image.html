<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>إنشاء صورة Open Graph</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        .og-image {
            width: 1200px;
            height: 630px;
            position: relative;
            background: linear-gradient(135deg, #0000FF 0%, #4169E1 50%, #6366F1 100%);
            border-radius: 0;
            overflow: hidden;
            margin: 0 auto;
            display: block;
        }
        
        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('91ad9430-b4ac-42c6-8268-86e74ea939e1.jpg') center/cover;
            opacity: 0.1;
            filter: blur(1px);
        }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 80px 60px;
            color: white;
        }
        
        .logo-circle {
            width: 150px;
            height: 150px;
            background: url('91ad9430-b4ac-42c6-8268-86e74ea939e1.jpg') center/cover;
            border-radius: 50%;
            border: 8px solid rgba(255, 215, 0, 0.9);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            margin-bottom: 40px;
        }
        
        .main-title {
            font-size: 72px;
            font-weight: 900;
            margin-bottom: 25px;
            text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.6);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }
        
        .subtitle {
            font-size: 36px;
            font-weight: 600;
            margin-bottom: 30px;
            text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.6);
            opacity: 0.95;
        }
        
        .description {
            font-size: 28px;
            font-weight: 400;
            margin-bottom: 50px;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.6);
            opacity: 0.9;
            max-width: 900px;
            line-height: 1.3;
        }
        
        .features-container {
            display: flex;
            justify-content: center;
            gap: 50px;
            margin-bottom: 40px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 24px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.15);
            padding: 18px 30px;
            border-radius: 30px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .feature-emoji {
            font-size: 28px;
        }
        
        .website-url {
            font-size: 22px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.2);
            padding: 15px 35px;
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .decorative-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .deco-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 215, 0, 0.08);
        }
        
        .deco1 {
            width: 300px;
            height: 300px;
            top: -150px;
            right: -150px;
        }
        
        .deco2 {
            width: 200px;
            height: 200px;
            bottom: -100px;
            left: -100px;
        }
        
        .deco3 {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80px;
        }
        
        .controls {
            margin-top: 30px;
            text-align: center;
        }
        
        .btn {
            padding: 15px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-download {
            background: #FFD700;
            color: #0000FF;
        }
        
        .btn-download:hover {
            background: #FFA500;
            transform: translateY(-2px);
        }
        
        .info-text {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="og-image" id="ogImage">
        <div class="bg-overlay"></div>
        <div class="decorative-bg">
            <div class="deco-circle deco1"></div>
            <div class="deco-circle deco2"></div>
            <div class="deco-circle deco3"></div>
        </div>
        <div class="content">
            <div class="logo-circle"></div>
            <h1 class="main-title">SKILLS WORLD ACADEMY</h1>
            <h2 class="subtitle">منصة التعلم والتطوير المهني العالمية</h2>
            <p class="description">تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية والمدربين المحترفين</p>
            <div class="features-container">
                <div class="feature-item">
                    <span class="feature-emoji">📚</span>
                    <span>كورسات متنوعة</span>
                </div>
                <div class="feature-item">
                    <span class="feature-emoji">🎓</span>
                    <span>شهادات معتمدة</span>
                </div>
                <div class="feature-item">
                    <span class="feature-emoji">👨‍🏫</span>
                    <span>مدربين خبراء</span>
                </div>
            </div>
            <div class="website-url">marketwise-academy-qhizq.web.app</div>
        </div>
    </div>
    
    <div class="controls">
        <button class="btn btn-download" onclick="downloadOGImage()">تحميل صورة المشاركة (1200x630)</button>
    </div>
    
    <div class="info-text">
        <p>هذه الصورة مصممة خصيصاً للمشاركة على وسائل التواصل الاجتماعي</p>
        <p>الأبعاد: 1200x630 بكسل (مطابقة لمعايير Facebook, Twitter, LinkedIn)</p>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        async function downloadOGImage() {
            const element = document.getElementById('ogImage');
            
            try {
                const canvas = await html2canvas(element, {
                    width: 1200,
                    height: 630,
                    scale: 1,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null,
                    logging: false
                });
                
                // تحويل إلى صورة وتحميلها
                const link = document.createElement('a');
                link.download = 'social-share.jpg';
                link.href = canvas.toDataURL('image/jpeg', 0.9);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                console.log('تم إنشاء الصورة بنجاح!');
            } catch (error) {
                console.error('خطأ في إنشاء الصورة:', error);
                alert('حدث خطأ في إنشاء الصورة. تأكد من تحميل جميع الموارد.');
            }
        }
        
        // تحميل تلقائي بعد تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('الصفحة جاهزة لإنشاء الصورة');
                // يمكن تفعيل التحميل التلقائي هنا إذا رغبت
                // downloadOGImage();
            }, 2000);
        });
    </script>
</body>
</html>
