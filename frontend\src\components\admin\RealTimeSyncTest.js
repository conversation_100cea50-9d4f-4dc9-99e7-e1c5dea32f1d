import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  CircularProgress,
  Paper
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Sync,
  School,
  People,
  Notifications,
  Speed,
  Timeline
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد خدمات التزامن
import { realTimeCoursesSync, realTimeEnrollmentsSync, realTimeNotificationsSync } from '../../services/realTimeSyncService';
import { hybridCourses } from '../../services/hybridDatabaseService';

const RealTimeSyncTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [syncStatus, setSyncStatus] = useState({
    courses: 'idle',
    enrollments: 'idle',
    notifications: 'idle'
  });
  const [realTimeData, setRealTimeData] = useState({
    courses: [],
    enrollments: [],
    notifications: []
  });

  // مراقبة البيانات في الوقت الفعلي
  useEffect(() => {
    console.log('🔄 بدء مراقبة البيانات للاختبار...');

    // مراقبة الكورسات
    const unsubscribeCourses = realTimeCoursesSync.watchCourses((courses) => {
      setRealTimeData(prev => ({ ...prev, courses }));
      setSyncStatus(prev => ({ ...prev, courses: 'active' }));
      console.log('✅ تحديث الكورسات في الاختبار:', courses.length);
    });

    // مراقبة التسجيلات
    const unsubscribeEnrollments = realTimeEnrollmentsSync.watchEnrollments((enrollments) => {
      setRealTimeData(prev => ({ ...prev, enrollments }));
      setSyncStatus(prev => ({ ...prev, enrollments: 'active' }));
      console.log('✅ تحديث التسجيلات في الاختبار:', enrollments.length);
    });

    return () => {
      console.log('🛑 إيقاف مراقبة البيانات للاختبار');
      unsubscribeCourses();
      unsubscribeEnrollments();
    };
  }, []);

  // اختبار إضافة كورس جديد
  const testAddCourse = async () => {
    try {
      addTestResult('بدء اختبار إضافة كورس جديد...', 'info');
      
      const testCourse = {
        title: `كورس اختبار ${new Date().getTime()}`,
        description: 'هذا كورس اختبار للتأكد من التزامن الفوري',
        instructor: 'علاء عبد الحميد',
        level: 'مبتدئ',
        duration: '2 ساعات',
        price: 0
      };

      const result = await hybridCourses.addCourse(testCourse);
      
      if (result.success) {
        addTestResult('✅ تم إضافة الكورس بنجاح - يجب أن يظهر فوراً في قائمة الكورسات', 'success');
        
        // انتظار قليل للتأكد من التحديث
        setTimeout(() => {
          const courseExists = realTimeData.courses.some(c => c.id === result.id);
          if (courseExists) {
            addTestResult('✅ تأكيد: الكورس ظهر في القائمة فوراً', 'success');
          } else {
            addTestResult('❌ خطأ: الكورس لم يظهر في القائمة', 'error');
          }
        }, 2000);
      }
    } catch (error) {
      addTestResult(`❌ فشل في إضافة الكورس: ${error.message}`, 'error');
    }
  };

  // اختبار تحديث كورس
  const testUpdateCourse = async () => {
    try {
      if (realTimeData.courses.length === 0) {
        addTestResult('❌ لا توجد كورسات للاختبار', 'error');
        return;
      }

      addTestResult('بدء اختبار تحديث كورس...', 'info');
      
      const firstCourse = realTimeData.courses[0];
      const updateData = {
        title: `${firstCourse.title} - محدث ${new Date().getTime()}`,
        description: 'تم تحديث هذا الكورس للاختبار'
      };

      const result = await hybridCourses.updateCourse(firstCourse.id, updateData);
      
      if (result.success) {
        addTestResult('✅ تم تحديث الكورس بنجاح - يجب أن يظهر التحديث فوراً', 'success');
        
        // انتظار قليل للتأكد من التحديث
        setTimeout(() => {
          const updatedCourse = realTimeData.courses.find(c => c.id === firstCourse.id);
          if (updatedCourse && updatedCourse.title.includes('محدث')) {
            addTestResult('✅ تأكيد: التحديث ظهر فوراً', 'success');
          } else {
            addTestResult('❌ خطأ: التحديث لم يظهر', 'error');
          }
        }, 2000);
      }
    } catch (error) {
      addTestResult(`❌ فشل في تحديث الكورس: ${error.message}`, 'error');
    }
  };

  // اختبار شامل للتزامن
  const runFullSyncTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      addTestResult('🚀 بدء الاختبار الشامل للتزامن الفوري...', 'info');
      
      // اختبار 1: إضافة كورس
      await testAddCourse();
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // اختبار 2: تحديث كورس
      await testUpdateCourse();
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // اختبار 3: فحص حالة التزامن
      addTestResult('فحص حالة التزامن...', 'info');
      
      const syncHealthy = Object.values(syncStatus).every(status => status === 'active');
      if (syncHealthy) {
        addTestResult('✅ جميع خدمات التزامن تعمل بشكل صحيح', 'success');
      } else {
        addTestResult('⚠️ بعض خدمات التزامن لا تعمل بشكل مثالي', 'warning');
      }
      
      addTestResult('🎉 انتهى الاختبار الشامل', 'success');
      
    } catch (error) {
      addTestResult(`❌ خطأ في الاختبار الشامل: ${error.message}`, 'error');
    } finally {
      setIsRunning(false);
    }
  };

  // إضافة نتيجة اختبار
  const addTestResult = (message, type) => {
    const result = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toLocaleTimeString('ar-SA')
    };
    
    setTestResults(prev => [...prev, result]);
    
    // إظهار toast للنتائج المهمة
    if (type === 'success') {
      toast.success(message);
    } else if (type === 'error') {
      toast.error(message);
    } else if (type === 'warning') {
      toast(message, { icon: '⚠️' });
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      default: return 'default';
    }
  };

  // الحصول على أيقونة النتيجة
  const getResultIcon = (type) => {
    switch (type) {
      case 'success': return <CheckCircle color="success" />;
      case 'error': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      default: return <Sync color="info" />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Timeline color="primary" />
        اختبار التزامن الفوري
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        هذه الصفحة تختبر التزامن الفوري بين لوحة الإدارة وصفحة الطلاب
      </Typography>

      {/* حالة التزامن */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <School sx={{ fontSize: 40, mb: 1 }} color="primary" />
              <Typography variant="h6">الكورسات</Typography>
              <Chip 
                label={syncStatus.courses === 'active' ? 'متصل' : 'غير متصل'} 
                color={getStatusColor(syncStatus.courses)}
                size="small"
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {realTimeData.courses.length} كورس
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <People sx={{ fontSize: 40, mb: 1 }} color="primary" />
              <Typography variant="h6">التسجيلات</Typography>
              <Chip 
                label={syncStatus.enrollments === 'active' ? 'متصل' : 'غير متصل'} 
                color={getStatusColor(syncStatus.enrollments)}
                size="small"
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {realTimeData.enrollments.length} تسجيل
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Speed sx={{ fontSize: 40, mb: 1 }} color="primary" />
              <Typography variant="h6">الأداء</Typography>
              <Chip 
                label="ممتاز" 
                color="success"
                size="small"
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                تزامن فوري
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* أزرار الاختبار */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item>
          <Button
            variant="contained"
            onClick={runFullSyncTest}
            disabled={isRunning}
            startIcon={isRunning ? <CircularProgress size={20} /> : <Sync />}
          >
            {isRunning ? 'جاري الاختبار...' : 'اختبار شامل'}
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            onClick={testAddCourse}
            disabled={isRunning}
            startIcon={<School />}
          >
            اختبار إضافة كورس
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            onClick={testUpdateCourse}
            disabled={isRunning}
            startIcon={<Sync />}
          >
            اختبار تحديث كورس
          </Button>
        </Grid>
      </Grid>

      {/* نتائج الاختبار */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            نتائج الاختبار
          </Typography>
          
          {testResults.length === 0 ? (
            <Alert severity="info">
              لم يتم تشغيل أي اختبار بعد. اضغط على "اختبار شامل" للبدء.
            </Alert>
          ) : (
            <Paper sx={{ maxHeight: 400, overflow: 'auto' }}>
              <List>
                {testResults.map((result, index) => (
                  <React.Fragment key={result.id}>
                    <ListItem>
                      <ListItemIcon>
                        {getResultIcon(result.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={result.message}
                        secondary={result.timestamp}
                      />
                    </ListItem>
                    {index < testResults.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default RealTimeSyncTest;
