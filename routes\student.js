const express = require('express');
const Course = require('../models/Course');
const Progress = require('../models/Progress');
const Certificate = require('../models/Certificate');
const Category = require('../models/Category');
const { studentAuth } = require('../middleware/auth');

const router = express.Router();

// جميع المسارات تتطلب صلاحيات الطالب
router.use(studentAuth);

// الحصول على الكورسات المتاحة
router.get('/courses', async (req, res) => {
  try {
    const { category, search, page = 1, limit = 12 } = req.query;
    const skip = (page - 1) * limit;

    let query = { isPublished: true };
    
    if (category) {
      query.category = category;
    }

    if (search) {
      query.$text = { $search: search };
    }

    const courses = await Course.find(query)
      .populate('category', 'name color')
      .select('title description shortDescription thumbnail instructor level totalDuration enrollmentCount rating')
      .sort({ isFeatured: -1, createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Course.countDocuments(query);

    // إضافة معلومات التسجيل والتقدم للطالب
    const coursesWithProgress = await Promise.all(courses.map(async (course) => {
      const progress = await Progress.findOne({
        student: req.user.id,
        course: course._id
      });

      return {
        ...course.toObject(),
        isEnrolled: !!progress,
        progress: progress ? progress.overallProgress : 0,
        isCompleted: progress ? progress.isCompleted : false
      };
    }));

    res.json({
      courses: coursesWithProgress,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('خطأ في الحصول على الكورسات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على تفاصيل كورس محدد
router.get('/courses/:id', async (req, res) => {
  try {
    const course = await Course.findOne({ _id: req.params.id, isPublished: true })
      .populate('category', 'name color');

    if (!course) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    // التحقق من التسجيل والتقدم
    const progress = await Progress.findOne({
      student: req.user.id,
      course: course._id
    });

    const courseData = {
      ...course.toObject(),
      isEnrolled: !!progress,
      progress: progress ? progress.overallProgress : 0,
      isCompleted: progress ? progress.isCompleted : false,
      currentLesson: progress ? progress.currentLesson : null,
      lessonsProgress: progress ? progress.lessonsProgress : []
    };

    res.json({ course: courseData });
  } catch (error) {
    console.error('خطأ في الحصول على تفاصيل الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// التسجيل في كورس
router.post('/courses/:id/enroll', async (req, res) => {
  try {
    const course = await Course.findOne({ _id: req.params.id, isPublished: true });

    if (!course) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    // التحقق من التسجيل المسبق
    const existingProgress = await Progress.findOne({
      student: req.user.id,
      course: course._id
    });

    if (existingProgress) {
      return res.status(400).json({ message: 'أنت مسجل في هذا الكورس بالفعل' });
    }

    // إنشاء سجل التقدم
    const progress = new Progress({
      student: req.user.id,
      course: course._id,
      lessonsProgress: course.lessons.map(lesson => ({
        lessonId: lesson._id,
        watchedDuration: 0,
        isCompleted: false
      }))
    });

    await progress.save();

    // تحديث عدد المسجلين في الكورس
    course.enrollmentCount += 1;
    await course.save();

    res.json({
      message: 'تم التسجيل في الكورس بنجاح',
      progress: {
        overallProgress: progress.overallProgress,
        isCompleted: progress.isCompleted
      }
    });
  } catch (error) {
    console.error('خطأ في التسجيل في الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث تقدم الطالب في درس
router.put('/progress/:courseId/:lessonId', async (req, res) => {
  try {
    const { courseId, lessonId } = req.params;
    const { watchedDuration, isCompleted } = req.body;

    const progress = await Progress.findOne({
      student: req.user.id,
      course: courseId
    });

    if (!progress) {
      return res.status(404).json({ message: 'لم يتم العثور على سجل التقدم' });
    }

    // العثور على الدرس في سجل التقدم
    const lessonProgress = progress.lessonsProgress.find(
      lp => lp.lessonId.toString() === lessonId
    );

    if (!lessonProgress) {
      return res.status(404).json({ message: 'الدرس غير موجود' });
    }

    // تحديث التقدم
    lessonProgress.watchedDuration = Math.max(lessonProgress.watchedDuration, watchedDuration);
    lessonProgress.isCompleted = isCompleted || lessonProgress.isCompleted;
    lessonProgress.lastWatchedAt = new Date();

    // تحديث الدرس الحالي
    progress.currentLesson = lessonId;
    progress.lastAccessedAt = new Date();

    await progress.save();

    res.json({
      message: 'تم تحديث التقدم بنجاح',
      progress: {
        overallProgress: progress.overallProgress,
        isCompleted: progress.isCompleted,
        lessonProgress: lessonProgress
      }
    });
  } catch (error) {
    console.error('خطأ في تحديث التقدم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الكورسات المسجل فيها الطالب
router.get('/my-courses', async (req, res) => {
  try {
    const progresses = await Progress.find({ student: req.user.id })
      .populate({
        path: 'course',
        populate: {
          path: 'category',
          select: 'name color'
        }
      })
      .sort({ lastAccessedAt: -1 });

    const courses = progresses.map(progress => ({
      ...progress.course.toObject(),
      progress: progress.overallProgress,
      isCompleted: progress.isCompleted,
      lastAccessedAt: progress.lastAccessedAt,
      currentLesson: progress.currentLesson,
      totalWatchTime: progress.totalWatchTime
    }));

    res.json({ courses });
  } catch (error) {
    console.error('خطأ في الحصول على كورساتي:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الشهادات
router.get('/certificates', async (req, res) => {
  try {
    const certificates = await Certificate.find({ student: req.user.id })
      .populate('course', 'title thumbnail')
      .sort({ issueDate: -1 });

    res.json({ certificates });
  } catch (error) {
    console.error('خطأ في الحصول على الشهادات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الأقسام
router.get('/categories', async (req, res) => {
  try {
    const categories = await Category.find({ isActive: true })
      .sort({ order: 1, name: 1 });

    res.json({ categories });
  } catch (error) {
    console.error('خطأ في الحصول على الأقسام:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
