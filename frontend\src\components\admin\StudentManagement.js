import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Avatar,
  Tooltip,
  useMediaQuery,
  useTheme,
  Fab,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Hidden
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  People,
  School,
  Save,
  Cancel,
  Refresh,
  PersonAdd,
  Code,
  CheckCircle,
  Cancel as CancelIcon,
  ExpandMore,
  ExpandLess,
  ViewList,
  ViewModule,
  FilterList,
  Search,
  MoreVert
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد خدمة التكامل المختلطة
import { hybridStudents } from '../../services/hybridDatabaseService';

const StudentManagement = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [viewMode, setViewMode] = useState(isMobile ? 'list' : 'table');
  const [expandedCard, setExpandedCard] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState('all');

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    studentCode: '',
    isActive: true,
    enrolledCourses: 0,
    completedCourses: 0,
    totalWatchTime: 0
  });

  // جلب الطلاب من قاعدة البيانات مع التحديث الفوري
  useEffect(() => {
    console.log('🔄 بدء مراقبة الطلاب...');

    const unsubscribe = hybridStudents.watchStudents((studentsData) => {
      setStudents(studentsData);
      console.log('✅ تم تحديث الطلاب:', studentsData.length);
    });

    return () => {
      console.log('🛑 إيقاف مراقبة الطلاب');
      unsubscribe();
    };
  }, []);

  // توليد كود طالب عشوائي
  const generateStudentCode = () => {
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    // التحقق من عدم تكرار الكود
    const existingCodes = students.map(s => s.studentCode);
    if (existingCodes.includes(code)) {
      return generateStudentCode(); // إعادة المحاولة إذا كان الكود موجود
    }
    return code;
  };

  // إضافة طالب جديد
  const handleAddStudent = async () => {
    if (!formData.name.trim()) {
      toast.error('يرجى إدخال اسم الطالب');
      return;
    }

    // توليد كود طالب إذا لم يتم إدخاله
    const studentCode = formData.studentCode.trim() || generateStudentCode();
    
    // التحقق من عدم تكرار الكود
    const existingCodes = students.map(s => s.studentCode);
    if (existingCodes.includes(studentCode)) {
      toast.error('كود الطالب موجود مسبقاً');
      return;
    }

    setLoading(true);
    try {
      const result = await hybridStudents.addStudent({
        ...formData,
        student_code: studentCode
      });

      if (result.success) {
        console.log('✅ تم إضافة الطالب:', result.id);
        toast.success(`تم إضافة الطالب بنجاح - كود الطالب: ${result.studentCode}`);
        handleCloseDialog();
      }
    } catch (error) {
      console.error('❌ خطأ في إضافة الطالب:', error);
      toast.error('فشل في إضافة الطالب');
    } finally {
      setLoading(false);
    }
  };

  // تحديث طالب موجود
  const handleUpdateStudent = async () => {
    if (!editingStudent || !formData.name.trim()) {
      toast.error('يرجى إدخال اسم الطالب');
      return;
    }

    // التحقق من عدم تكرار كود الطالب
    const existingCodes = students
      .filter(s => s.id !== editingStudent.id)
      .map(s => s.studentCode);
    
    if (existingCodes.includes(formData.studentCode)) {
      toast.error('كود الطالب موجود مسبقاً');
      return;
    }

    setLoading(true);
    try {
      const result = await hybridStudents.updateStudent(editingStudent.id, formData);

      if (result.success) {
        console.log('✅ تم تحديث الطالب:', editingStudent.id);
        toast.success('تم تحديث بيانات الطالب بنجاح');
        handleCloseDialog();
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث الطالب:', error);
      toast.error('فشل في تحديث الطالب');
    } finally {
      setLoading(false);
    }
  };

  // حذف طالب
  const handleDeleteStudent = async (studentId, studentName) => {
    if (!window.confirm(`هل أنت متأكد من حذف الطالب "${studentName}"؟`)) {
      return;
    }

    setLoading(true);
    try {
      const result = await hybridStudents.deleteStudent(studentId);

      if (result.success) {
        console.log('✅ تم حذف الطالب:', studentId);
        toast.success('تم حذف الطالب بنجاح');
      }
    } catch (error) {
      console.error('❌ خطأ في حذف الطالب:', error);
      toast.error('فشل في حذف الطالب');
    } finally {
      setLoading(false);
    }
  };

  // تبديل حالة الطالب (مفعل/غير مفعل)
  const handleToggleStudentStatus = async (studentId, currentStatus) => {
    try {
      const result = await hybridStudents.updateStudent(studentId, {
        is_active: !currentStatus
      });

      if (result.success) {
        toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الطالب`);
      }
    } catch (error) {
      console.error('❌ خطأ في تغيير حالة الطالب:', error);
      toast.error('فشل في تغيير حالة الطالب');
    }
  };

  // فتح نافذة إضافة طالب جديد
  const handleOpenAddDialog = () => {
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      studentCode: '',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0
    });
    setOpenDialog(true);
  };

  // فتح نافذة تعديل طالب
  const handleOpenEditDialog = (student) => {
    setEditingStudent(student);
    setFormData({
      name: student.name || '',
      email: student.email || '',
      phone: student.phone || '',
      studentCode: student.studentCode || '',
      isActive: student.isActive !== undefined ? student.isActive : true,
      enrolledCourses: student.enrolledCourses || 0,
      completedCourses: student.completedCourses || 0,
      totalWatchTime: student.totalWatchTime || 0
    });
    setOpenDialog(true);
  };

  // إغلاق النافذة
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      studentCode: '',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0
    });
  };

  // تحديث بيانات النموذج
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تنسيق التاريخ
  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    if (date.toDate) date = date.toDate();
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // الحصول على الأحرف الأولى من الاسم للأفاتار
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // تصفية الطلاب حسب البحث والفلتر
  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentCode?.includes(searchTerm);

    const matchesFilter = filterActive === 'all' ||
                         (filterActive === 'active' && student.isActive) ||
                         (filterActive === 'inactive' && !student.isActive);

    return matchesSearch && matchesFilter;
  });

  // تبديل وضع العرض
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'table' ? 'list' : 'table');
  };

  // تبديل توسيع البطاقة
  const toggleCardExpansion = (studentId) => {
    setExpandedCard(prev => prev === studentId ? null : studentId);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box
        display="flex"
        flexDirection={isMobile ? 'column' : 'row'}
        justifyContent="space-between"
        alignItems={isMobile ? 'stretch' : 'center'}
        mb={3}
        gap={2}
      >
        <Typography
          variant={isMobile ? "h5" : "h4"}
          component="h1"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            textAlign: isMobile ? 'center' : 'left'
          }}
        >
          <People color="primary" />
          إدارة الطلاب
        </Typography>

        {/* شريط البحث والأدوات */}
        <Box
          display="flex"
          flexDirection={isMobile ? 'column' : 'row'}
          gap={1}
          alignItems="center"
          width={isMobile ? '100%' : 'auto'}
        >
          <TextField
            size="small"
            placeholder="البحث في الطلاب..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{
              minWidth: isMobile ? '100%' : 200,
              mb: isMobile ? 1 : 0
            }}
          />

          <Button
            size="small"
            variant="outlined"
            onClick={() => setFilterActive(filterActive === 'all' ? 'active' : filterActive === 'active' ? 'inactive' : 'all')}
            startIcon={<FilterList />}
            sx={{ minWidth: isMobile ? '100%' : 'auto' }}
          >
            {filterActive === 'all' ? 'الكل' : filterActive === 'active' ? 'المفعلين' : 'غير المفعلين'}
          </Button>

          {!isMobile && (
            <IconButton
              onClick={toggleViewMode}
              color="primary"
              title={viewMode === 'table' ? 'عرض القائمة' : 'عرض الجدول'}
            >
              {viewMode === 'table' ? <ViewList /> : <ViewModule />}
            </IconButton>
          )}
        </Box>
      </Box>

      {/* زر الإضافة العائم للأجهزة المحمولة */}
      {isMobile && (
        <Fab
          color="primary"
          aria-label="إضافة طالب"
          onClick={handleOpenAddDialog}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <PersonAdd />
        </Fab>
      )}

      {/* زر الإضافة للأجهزة الكبيرة */}
      {!isMobile && (
        <Box display="flex" justifyContent="flex-end" mb={2}>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={handleOpenAddDialog}
            size={isTablet ? "medium" : "large"}
            sx={{ borderRadius: 2 }}
          >
            إضافة طالب جديد
          </Button>
        </Box>
      )}

      {/* إحصائيات سريعة */}
      <Grid container spacing={isMobile ? 2 : 3} mb={3}>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            textAlign: 'center',
            p: isMobile ? 1.5 : 2,
            minHeight: isMobile ? 80 : 100
          }}>
            <Typography
              variant={isMobile ? "h6" : "h4"}
              color="primary"
              sx={{ fontSize: isMobile ? '1.2rem' : undefined }}
            >
              {filteredStudents.length}
            </Typography>
            <Typography
              variant={isMobile ? "caption" : "body2"}
              color="text.secondary"
              sx={{ fontSize: isMobile ? '0.7rem' : undefined }}
            >
              {searchTerm || filterActive !== 'all' ? 'نتائج البحث' : 'إجمالي الطلاب'}
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            textAlign: 'center',
            p: isMobile ? 1.5 : 2,
            minHeight: isMobile ? 80 : 100
          }}>
            <Typography
              variant={isMobile ? "h6" : "h4"}
              color="success.main"
              sx={{ fontSize: isMobile ? '1.2rem' : undefined }}
            >
              {students.filter(s => s.isActive).length}
            </Typography>
            <Typography
              variant={isMobile ? "caption" : "body2"}
              color="text.secondary"
              sx={{ fontSize: isMobile ? '0.7rem' : undefined }}
            >
              الطلاب المفعلين
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            textAlign: 'center',
            p: isMobile ? 1.5 : 2,
            minHeight: isMobile ? 80 : 100
          }}>
            <Typography
              variant={isMobile ? "h6" : "h4"}
              color="info.main"
              sx={{ fontSize: isMobile ? '1.2rem' : undefined }}
            >
              {students.reduce((total, student) => total + (student.enrolledCourses || 0), 0)}
            </Typography>
            <Typography
              variant={isMobile ? "caption" : "body2"}
              color="text.secondary"
              sx={{ fontSize: isMobile ? '0.7rem' : undefined }}
            >
              إجمالي التسجيلات
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            textAlign: 'center',
            p: isMobile ? 1.5 : 2,
            minHeight: isMobile ? 80 : 100
          }}>
            <Typography
              variant={isMobile ? "h6" : "h4"}
              color="warning.main"
              sx={{ fontSize: isMobile ? '1.2rem' : undefined }}
            >
              {students.reduce((total, student) => total + (student.completedCourses || 0), 0)}
            </Typography>
            <Typography
              variant={isMobile ? "caption" : "body2"}
              color="text.secondary"
              sx={{ fontSize: isMobile ? '0.7rem' : undefined }}
            >
              الكورسات المكتملة
            </Typography>
          </Card>
        </Grid>
      </Grid>

      {/* قائمة/جدول الطلاب */}
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              قائمة الطلاب ({filteredStudents.length})
            </Typography>
            {!isMobile && (
              <IconButton
                onClick={toggleViewMode}
                color="primary"
                title={viewMode === 'table' ? 'عرض البطاقات' : 'عرض الجدول'}
              >
                {viewMode === 'table' ? <ViewList /> : <ViewModule />}
              </IconButton>
            )}
          </Box>

          {loading && (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          )}

          {!loading && students.length === 0 && (
            <Alert severity="info" sx={{ mt: 2 }}>
              لا يوجد طلاب حالياً. اضغط على "إضافة طالب جديد" لإنشاء أول طالب.
            </Alert>
          )}

          {!loading && filteredStudents.length === 0 && students.length > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              لا توجد نتائج تطابق البحث أو الفلتر المحدد.
            </Alert>
          )}

          {/* عرض البطاقات للأجهزة المحمولة أو عند اختيار وضع القائمة */}
          {!loading && filteredStudents.length > 0 && (isMobile || viewMode === 'list') && (
            <Box sx={{ mt: 2 }}>
              {filteredStudents.map((student) => (
                <Card
                  key={student.id}
                  sx={{
                    mb: 2,
                    border: '1px solid',
                    borderColor: 'divider',
                    '&:hover': {
                      boxShadow: 2
                    }
                  }}
                >
                  <CardContent sx={{ pb: 1 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                      <Box display="flex" alignItems="center" gap={2} flex={1}>
                        <Avatar sx={{ bgcolor: 'primary.main', width: 40, height: 40 }}>
                          {getInitials(student.name)}
                        </Avatar>
                        <Box flex={1}>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {student.name}
                          </Typography>
                          <Box display="flex" gap={1} flexWrap="wrap" mt={0.5}>
                            <Chip
                              icon={<Code />}
                              label={student.studentCode}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            <Chip
                              label={student.isActive ? 'مفعل' : 'غير مفعل'}
                              size="small"
                              color={student.isActive ? 'success' : 'default'}
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1}>
                        <IconButton
                          size="small"
                          onClick={() => toggleCardExpansion(student.id)}
                        >
                          {expandedCard === student.id ? <ExpandLess /> : <ExpandMore />}
                        </IconButton>
                        <IconButton size="small">
                          <MoreVert />
                        </IconButton>
                      </Box>
                    </Box>

                    <Collapse in={expandedCard === student.id}>
                      <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <Typography variant="caption" color="text.secondary">
                              البريد الإلكتروني
                            </Typography>
                            <Typography variant="body2">
                              {student.email || 'غير محدد'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Typography variant="caption" color="text.secondary">
                              الهاتف
                            </Typography>
                            <Typography variant="body2">
                              {student.phone || 'غير محدد'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Typography variant="caption" color="text.secondary">
                              الكورسات المسجلة
                            </Typography>
                            <Typography variant="body2" color="info.main" fontWeight="bold">
                              {student.enrolledCourses || 0}
                            </Typography>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Typography variant="caption" color="text.secondary">
                              الكورسات المكتملة
                            </Typography>
                            <Typography variant="body2" color="success.main" fontWeight="bold">
                              {student.completedCourses || 0}
                            </Typography>
                          </Grid>
                          <Grid item xs={12}>
                            <Typography variant="caption" color="text.secondary">
                              تاريخ التسجيل
                            </Typography>
                            <Typography variant="body2">
                              {formatDate(student.createdAt)}
                            </Typography>
                          </Grid>
                        </Grid>

                        <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={student.isActive}
                                onChange={() => handleToggleStudentStatus(student.id, student.isActive)}
                                size="small"
                              />
                            }
                            label={student.isActive ? 'مفعل' : 'غير مفعل'}
                          />
                          <Box display="flex" gap={1}>
                            <Button
                              size="small"
                              startIcon={<Edit />}
                              onClick={() => handleOpenEditDialog(student)}
                              variant="outlined"
                            >
                              تعديل
                            </Button>
                            <Button
                              size="small"
                              startIcon={<Delete />}
                              onClick={() => handleDeleteStudent(student.id, student.name)}
                              color="error"
                              variant="outlined"
                            >
                              حذف
                            </Button>
                          </Box>
                        </Box>
                      </Box>
                    </Collapse>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}

          {/* عرض الجدول للأجهزة الكبيرة */}
          {!loading && filteredStudents.length > 0 && !isMobile && viewMode === 'table' && (
            <TableContainer
              component={Paper}
              sx={{
                mt: 2,
                overflowX: 'auto',
                '& .MuiTable-root': {
                  minWidth: isTablet ? 800 : 1000
                }
              }}
            >
              <Table size={isTablet ? "small" : "medium"}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>الطالب</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>كود الطالب</TableCell>
                    {!isTablet && <TableCell sx={{ fontWeight: 'bold' }}>البريد الإلكتروني</TableCell>}
                    {!isTablet && <TableCell sx={{ fontWeight: 'bold' }}>الهاتف</TableCell>}
                    <TableCell sx={{ fontWeight: 'bold' }}>الكورسات</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>المكتمل</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
                    {!isTablet && <TableCell sx={{ fontWeight: 'bold' }}>تاريخ التسجيل</TableCell>}
                    <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredStudents.map((student) => (
                    <TableRow key={student.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={isTablet ? 1 : 2}>
                          <Avatar
                            sx={{
                              bgcolor: 'primary.main',
                              width: isTablet ? 32 : 40,
                              height: isTablet ? 32 : 40,
                              fontSize: isTablet ? '0.8rem' : '1rem'
                            }}
                          >
                            {getInitials(student.name)}
                          </Avatar>
                          <Box>
                            <Typography
                              variant={isTablet ? "body2" : "subtitle2"}
                              fontWeight="bold"
                              sx={{
                                maxWidth: isTablet ? 120 : 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {student.name}
                            </Typography>
                            {!isTablet && (
                              <Typography variant="caption" color="text.secondary">
                                ID: {student.id.substring(0, 8)}...
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={<Code />}
                          label={student.studentCode}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      {!isTablet && (
                        <TableCell>
                          <Typography
                            variant="body2"
                            sx={{
                              maxWidth: 150,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {student.email || 'غير محدد'}
                          </Typography>
                        </TableCell>
                      )}
                      {!isTablet && (
                        <TableCell>{student.phone || 'غير محدد'}</TableCell>
                      )}
                      <TableCell>
                        <Chip
                          icon={<School />}
                          label={student.enrolledCourses || 0}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={<CheckCircle />}
                          label={student.completedCourses || 0}
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        {isTablet ? (
                          <Switch
                            checked={student.isActive}
                            onChange={() => handleToggleStudentStatus(student.id, student.isActive)}
                            size="small"
                          />
                        ) : (
                          <FormControlLabel
                            control={
                              <Switch
                                checked={student.isActive}
                                onChange={() => handleToggleStudentStatus(student.id, student.isActive)}
                                size="small"
                              />
                            }
                            label={student.isActive ? 'مفعل' : 'غير مفعل'}
                          />
                        )}
                      </TableCell>
                      {!isTablet && (
                        <TableCell>
                          <Typography variant="caption">
                            {formatDate(student.createdAt)}
                          </Typography>
                        </TableCell>
                      )}
                      <TableCell>
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="تعديل">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenEditDialog(student)}
                              color="primary"
                            >
                              <Edit />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="حذف">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteStudent(student.id, student.name)}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة/تعديل الطالب */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth={isMobile ? "sm" : "md"}
        fullWidth
        fullScreen={isMobile}
        aria-labelledby="student-dialog-title"
        aria-describedby="student-dialog-description"
        sx={{
          '& .MuiDialog-paper': {
            margin: isMobile ? 0 : 2,
            maxHeight: isMobile ? '100vh' : '90vh'
          }
        }}
      >
        <DialogTitle
          id="student-dialog-title"
          sx={{
            pb: isMobile ? 1 : 2,
            fontSize: isMobile ? '1.1rem' : '1.25rem'
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center">
            {editingStudent ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد'}
            {isMobile && (
              <IconButton onClick={handleCloseDialog} size="small">
                <Cancel />
              </IconButton>
            )}
          </Box>
        </DialogTitle>
        <DialogContent
          id="student-dialog-description"
          sx={{
            px: isMobile ? 2 : 3,
            py: isMobile ? 1 : 2
          }}
        >
          <Grid container spacing={isMobile ? 2 : 3} sx={{ mt: 0.5 }}>
            <Grid item xs={12} sm={isMobile ? 12 : 6}>
              <TextField
                fullWidth
                label="اسم الطالب *"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                variant="outlined"
                size={isMobile ? "medium" : "medium"}
              />
            </Grid>
            <Grid item xs={12} sm={isMobile ? 12 : 6}>
              <TextField
                fullWidth
                label="كود الطالب"
                value={formData.studentCode}
                onChange={(e) => handleFormChange('studentCode', e.target.value)}
                variant="outlined"
                placeholder="سيتم توليده تلقائياً إذا ترك فارغاً"
                helperText="6 أرقام فريدة للطالب"
                size={isMobile ? "medium" : "medium"}
              />
            </Grid>
            <Grid item xs={12} sm={isMobile ? 12 : 6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                variant="outlined"
                size={isMobile ? "medium" : "medium"}
              />
            </Grid>
            <Grid item xs={12} sm={isMobile ? 12 : 6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                variant="outlined"
                placeholder="05xxxxxxxx"
                size={isMobile ? "medium" : "medium"}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="حساب مفعل"
                sx={{
                  '& .MuiFormControlLabel-label': {
                    fontSize: isMobile ? '0.9rem' : '1rem'
                  }
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            px: isMobile ? 2 : 3,
            py: isMobile ? 2 : 1.5,
            flexDirection: isMobile ? 'column' : 'row',
            gap: isMobile ? 1 : 0
          }}
        >
          {!isMobile && (
            <Button onClick={handleCloseDialog} startIcon={<Cancel />}>
              إلغاء
            </Button>
          )}
          <Button
            onClick={editingStudent ? handleUpdateStudent : handleAddStudent}
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
            fullWidth={isMobile}
            size={isMobile ? "large" : "medium"}
          >
            {loading ? <CircularProgress size={20} /> : (editingStudent ? 'تحديث' : 'إضافة')}
          </Button>
          {isMobile && (
            <Button
              onClick={handleCloseDialog}
              fullWidth
              size="large"
              variant="outlined"
            >
              إلغاء
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagement;
