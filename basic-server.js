const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

app.use(cors());
app.use(express.json());

// بيانات تجريبية
const admin = {
  email: '<EMAIL>',
  password: 'Admin123!',
  name: 'علاء عبد الحميد'
};

const students = [
  {
    _id: 'student1',
    studentCode: '123456',
    name: 'أحم<PERSON> محمد',
    isActive: true,
    enrolledCourses: ['course1']
  }
];

const courses = [
  {
    _id: 'course1',
    title: 'أساسيات التسويق الرقمي',
    description: 'تعلم أساسيات التسويق الرقمي من الصفر',
    instructor: 'علاء عبد الحميد',
    isActive: true,
    videos: [
      { id: 1, title: 'مقدمة في التسويق الرقمي' },
      { id: 2, title: 'استراتيجيات التسويق' },
      { id: 3, title: 'وسائل التواصل الاجتماعي' }
    ]
  },
  {
    _id: 'course2',
    title: 'إدارة وسائل التواصل الاجتماعي',
    description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي',
    instructor: 'علاء عبد الحميد',
    isActive: true,
    videos: [
      { id: 1, title: 'مقدمة في وسائل التواصل' },
      { id: 2, title: 'استراتيجية المحتوى' }
    ]
  }
];

// Routes
app.get('/api/test', (req, res) => {
  res.json({ message: 'الخادم يعمل!' });
});

// تسجيل دخول المدير
app.post('/api/admin/login', (req, res) => {
  const { email, password } = req.body;
  console.log('🔐 طلب تسجيل دخول مدير:', { email });

  if (email === admin.email && password === admin.password) {
    res.json({
      token: 'fake-admin-token',
      user: {
        _id: 'admin1',
        name: admin.name,
        email: admin.email,
        role: 'admin'
      }
    });
  } else {
    res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
  }
});

// تسجيل دخول الطالب
app.post('/api/student/login', (req, res) => {
  const { code } = req.body;
  console.log('🎓 طلب تسجيل دخول طالب:', { code });

  const student = students.find(s => s.studentCode === code);
  if (student) {
    res.json({
      token: 'fake-student-token',
      user: {
        _id: student._id,
        name: student.name,
        studentCode: student.studentCode,
        role: 'student'
      }
    });
  } else {
    res.status(401).json({ message: 'كود الطالب غير صحيح' });
  }
});

// جلب الكورسات (للمدير)
app.get('/api/admin/courses', (req, res) => {
  console.log('📚 طلب جلب الكورسات من المدير');
  res.json(courses);
});

// جلب الطلاب (للمدير)
app.get('/api/admin/students', (req, res) => {
  console.log('👥 طلب جلب الطلاب من المدير');
  res.json(students);
});

// إحصائيات لوحة التحكم
app.get('/api/admin/dashboard-stats', (req, res) => {
  console.log('📊 طلب إحصائيات لوحة التحكم');
  
  const totalVideos = courses.reduce((total, course) => total + course.videos.length, 0);
  
  res.json({
    totalStudents: students.length,
    activeStudents: students.filter(s => s.isActive).length,
    totalCourses: courses.length,
    activeCourses: courses.filter(c => c.isActive).length,
    totalVideos,
    totalCertificates: 1
  });
});

// جلب كورسات الطالب
app.get('/api/student/courses', (req, res) => {
  console.log('📚 طلب جلب كورسات الطالب');
  
  const studentCourses = courses.filter(course => 
    course._id === 'course1' // الطالب التجريبي مسجل في course1 فقط
  ).map(course => ({
    ...course,
    progress: { completedVideos: 2, totalVideos: course.videos.length, progress: 67 }
  }));

  res.json(studentCourses);
});

app.listen(PORT, () => {
  console.log(`🚀 الخادم البسيط يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الموقع متاح على: http://localhost:${PORT}`);
  console.log(`📱 API متاح على: http://localhost:${PORT}/api/test`);
  console.log(`👨‍💼 بيانات المدير: <EMAIL> / Admin123!`);
  console.log(`👨‍🎓 كود طالب تجريبي: 123456`);
});
