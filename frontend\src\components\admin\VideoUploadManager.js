/**
 * مدير رفع الفيديوهات المتقدم - SKILLS WORLD ACADEMY
 * Advanced Video Upload Manager
 */

import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  CloudUpload,
  VideoLibrary,
  Cancel,
  CheckCircle,
  Error,
  PlayArrow,
  Delete,
  Edit,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import fileUploadService, { SUPPORTED_FILE_TYPES } from '../../services/fileUploadService';

const VideoUploadManager = ({ 
  open, 
  onClose, 
  courseId, 
  onVideoUploaded,
  existingVideos = [] 
}) => {
  const [uploadQueue, setUploadQueue] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [videoMetadata, setVideoMetadata] = useState({});
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);

  // معلومات الفيديو الافتراضية
  const [defaultVideoInfo, setDefaultVideoInfo] = useState({
    title: '',
    description: '',
    duration: '',
    isPublished: false,
    isFree: false,
    order: existingVideos.length + 1
  });

  /**
   * معالج إسقاط الملفات
   */
  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // معالجة الملفات المرفوضة
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach(error => {
        if (error.code === 'file-too-large') {
          toast.error(`الملف ${file.name} كبير جداً. الحد الأقصى 500MB`);
        } else if (error.code === 'file-invalid-type') {
          toast.error(`نوع الملف ${file.name} غير مدعوم`);
        }
      });
    });

    // إضافة الملفات المقبولة إلى قائمة الانتظار
    const newFiles = acceptedFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      status: 'pending', // pending, uploading, completed, error
      progress: 0,
      metadata: {
        title: file.name.replace(/\.[^/.]+$/, ""), // إزالة الامتداد
        description: '',
        duration: '',
        isPublished: false,
        isFree: false,
        order: existingVideos.length + uploadQueue.length + 1
      }
    }));

    setUploadQueue(prev => [...prev, ...newFiles]);
  }, [existingVideos.length, uploadQueue.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': SUPPORTED_FILE_TYPES.VIDEO.extensions
    },
    maxSize: SUPPORTED_FILE_TYPES.VIDEO.maxSize,
    multiple: true
  });

  /**
   * تحديث معلومات الفيديو
   */
  const updateVideoMetadata = (fileId, field, value) => {
    setUploadQueue(prev => 
      prev.map(item => 
        item.id === fileId 
          ? { ...item, metadata: { ...item.metadata, [field]: value } }
          : item
      )
    );
  };

  /**
   * إزالة فيديو من القائمة
   */
  const removeFromQueue = (fileId) => {
    setUploadQueue(prev => prev.filter(item => item.id !== fileId));
    
    // إلغاء الرفع إذا كان جارياً
    const item = uploadQueue.find(item => item.id === fileId);
    if (item && item.status === 'uploading') {
      fileUploadService.cancelUpload(item.uploadPath);
    }
  };

  /**
   * رفع فيديو واحد
   */
  const uploadSingleVideo = async (queueItem) => {
    try {
      // تحديث حالة الرفع
      setUploadQueue(prev => 
        prev.map(item => 
          item.id === queueItem.id 
            ? { ...item, status: 'uploading' }
            : item
        )
      );

      // رفع الفيديو
      const result = await fileUploadService.uploadCourseVideo(
        queueItem.file,
        courseId,
        (progress) => {
          setUploadProgress(prev => ({
            ...prev,
            [queueItem.id]: progress
          }));
        }
      );

      // تحديث حالة النجاح
      setUploadQueue(prev => 
        prev.map(item => 
          item.id === queueItem.id 
            ? { 
                ...item, 
                status: 'completed',
                uploadResult: result,
                uploadPath: result.path
              }
            : item
        )
      );

      // إشعار المكون الأب
      if (onVideoUploaded) {
        onVideoUploaded({
          ...queueItem.metadata,
          videoUrl: result.url,
          backupUrl: result.backupUrl,
          fileName: result.fileName,
          fileSize: result.size,
          uploadPath: result.path
        });
      }

      toast.success(`تم رفع الفيديو: ${queueItem.metadata.title}`);

    } catch (error) {
      console.error('خطأ في رفع الفيديو:', error);
      
      setUploadQueue(prev => 
        prev.map(item => 
          item.id === queueItem.id 
            ? { ...item, status: 'error', error: error.message }
            : item
        )
      );

      toast.error(`فشل في رفع الفيديو: ${error.message}`);
    }
  };

  /**
   * رفع جميع الفيديوهات
   */
  const uploadAllVideos = async () => {
    setIsUploading(true);
    
    const pendingVideos = uploadQueue.filter(item => item.status === 'pending');
    
    if (pendingVideos.length === 0) {
      toast.error('لا توجد فيديوهات للرفع');
      setIsUploading(false);
      return;
    }

    // التحقق من وجود عناوين للفيديوهات
    const videosWithoutTitles = pendingVideos.filter(item => !item.metadata.title.trim());
    if (videosWithoutTitles.length > 0) {
      toast.error('يرجى إدخال عناوين لجميع الفيديوهات');
      setIsUploading(false);
      return;
    }

    try {
      // رفع الفيديوهات بشكل متتالي لتجنب الحمل الزائد
      for (const video of pendingVideos) {
        await uploadSingleVideo(video);
      }
      
      toast.success('تم رفع جميع الفيديوهات بنجاح!');
    } catch (error) {
      console.error('خطأ في رفع الفيديوهات:', error);
    } finally {
      setIsUploading(false);
    }
  };

  /**
   * تنسيق حجم الملف
   */
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * الحصول على لون الحالة
   */
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'default';
      case 'uploading': return 'primary';
      case 'completed': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  /**
   * الحصول على أيقونة الحالة
   */
  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <VideoLibrary />;
      case 'uploading': return <CloudUpload />;
      case 'completed': return <CheckCircle />;
      case 'error': return <Error />;
      default: return <VideoLibrary />;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <VideoLibrary />
        مدير رفع الفيديوهات
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* منطقة إسقاط الملفات */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box
              {...getRootProps()}
              sx={{
                border: '2px dashed',
                borderColor: isDragActive ? 'primary.main' : 'grey.300',
                borderRadius: 2,
                p: 4,
                textAlign: 'center',
                cursor: 'pointer',
                bgcolor: isDragActive ? 'action.hover' : 'transparent',
                transition: 'all 0.3s ease'
              }}
            >
              <input {...getInputProps()} />
              <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {isDragActive ? 'أفلت الملفات هنا...' : 'اسحب وأفلت الفيديوهات هنا'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                أو انقر لاختيار الملفات
              </Typography>
              <Typography variant="caption" color="text.secondary">
                الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM
                <br />
                الحد الأقصى: 500MB لكل ملف
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* قائمة الفيديوهات */}
        {uploadQueue.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                قائمة الفيديوهات ({uploadQueue.length})
              </Typography>
              
              <List>
                {uploadQueue.map((item, index) => (
                  <React.Fragment key={item.id}>
                    <ListItem sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
                      {/* معلومات الملف الأساسية */}
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                          {getStatusIcon(item.status)}
                          <Box sx={{ ml: 2, flex: 1 }}>
                            <Typography variant="subtitle1">
                              {item.file.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatFileSize(item.file.size)}
                            </Typography>
                          </Box>
                        </Box>
                        
                        <Chip 
                          label={item.status === 'pending' ? 'في الانتظار' : 
                                item.status === 'uploading' ? 'جاري الرفع' :
                                item.status === 'completed' ? 'مكتمل' : 'خطأ'}
                          color={getStatusColor(item.status)}
                          size="small"
                          sx={{ mr: 1 }}
                        />
                        
                        <IconButton 
                          onClick={() => removeFromQueue(item.id)}
                          disabled={item.status === 'uploading'}
                          size="small"
                        >
                          <Delete />
                        </IconButton>
                      </Box>

                      {/* شريط التقدم */}
                      {item.status === 'uploading' && (
                        <Box sx={{ width: '100%', mb: 2 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={uploadProgress[item.id] || 0}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {Math.round(uploadProgress[item.id] || 0)}%
                          </Typography>
                        </Box>
                      )}

                      {/* معلومات الفيديو */}
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="عنوان الفيديو *"
                            value={item.metadata.title}
                            onChange={(e) => updateVideoMetadata(item.id, 'title', e.target.value)}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <TextField
                            fullWidth
                            label="مدة الفيديو"
                            value={item.metadata.duration}
                            onChange={(e) => updateVideoMetadata(item.id, 'duration', e.target.value)}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            placeholder="15:30"
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <TextField
                            fullWidth
                            type="number"
                            label="ترتيب الفيديو"
                            value={item.metadata.order}
                            onChange={(e) => updateVideoMetadata(item.id, 'order', parseInt(e.target.value))}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            multiline
                            rows={2}
                            label="وصف الفيديو"
                            value={item.metadata.description}
                            onChange={(e) => updateVideoMetadata(item.id, 'description', e.target.value)}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={item.metadata.isPublished}
                                  onChange={(e) => updateVideoMetadata(item.id, 'isPublished', e.target.checked)}
                                  disabled={item.status === 'uploading' || item.status === 'completed'}
                                />
                              }
                              label="نشر الفيديو"
                            />
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={item.metadata.isFree}
                                  onChange={(e) => updateVideoMetadata(item.id, 'isFree', e.target.checked)}
                                  disabled={item.status === 'uploading' || item.status === 'completed'}
                                />
                              }
                              label="فيديو مجاني"
                            />
                          </Box>
                        </Grid>
                      </Grid>

                      {/* رسالة الخطأ */}
                      {item.status === 'error' && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                          {item.error}
                        </Alert>
                      )}
                    </ListItem>
                    
                    {index < uploadQueue.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 2 }}>
        <Button onClick={onClose} disabled={isUploading}>
          إلغاء
        </Button>
        <Button
          variant="contained"
          onClick={uploadAllVideos}
          disabled={uploadQueue.length === 0 || isUploading}
          startIcon={<CloudUpload />}
        >
          {isUploading ? 'جاري الرفع...' : 'رفع جميع الفيديوهات'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VideoUploadManager;
