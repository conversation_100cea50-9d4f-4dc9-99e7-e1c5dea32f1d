import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  LinearProgress,
  Divider,

} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  Email,
  Phone,
  CalendarToday,
  School,
  Person
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import { studentManagementService } from '../../firebase/adminServices';

const StudentManagementSimple = () => {
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [dialogType, setDialogType] = useState('add');

  const [studentForm, setStudentForm] = useState({
    name: '',
    email: '',
    phone: '',
    studentCode: '',
    isActive: true,
    notes: ''
  });

  useEffect(() => {
    fetchStudents();
  }, []);

  const fetchStudents = async () => {
    try {
      const studentsData = await studentManagementService.getAllStudents();
      setStudents(studentsData);
    } catch (error) {
      console.error('خطأ في جلب الطلاب:', error);
      toast.error('فشل في جلب الطلاب');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (type, student = null) => {
    setDialogType(type);
    setSelectedStudent(student);
    if (student && type === 'edit') {
      setStudentForm({
        name: student.name,
        email: student.email || '',
        phone: student.phone || '',
        studentCode: student.studentCode,
        isActive: student.isActive,
        notes: student.notes || ''
      });
    } else {
      setStudentForm({
        name: '',
        email: '',
        phone: '',
        studentCode: '',
        isActive: true,
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedStudent(null);
  };

  const handleSaveStudent = async () => {
    try {
      if (dialogType === 'add') {
        await studentManagementService.createStudent(studentForm);
        const updatedStudents = await studentManagementService.getAllStudents();
        setStudents(updatedStudents);
        toast.success('تم إضافة الطالب بنجاح');
      } else if (dialogType === 'edit') {
        await studentManagementService.updateStudent(selectedStudent.id, studentForm);
        const updatedStudents = await studentManagementService.getAllStudents();
        setStudents(updatedStudents);
        toast.success('تم تحديث الطالب بنجاح');
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الطالب:', error);
      toast.error('فشل في حفظ الطالب');
    }
  };

  const handleDeleteStudent = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      try {
        await studentManagementService.deleteStudent(studentId);
        setStudents(students.filter(s => s.id !== studentId));
        toast.success('تم حذف الطالب بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        toast.error('فشل في حذف الطالب');
      }
    }
  };

  const StudentCard = ({ student }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ width: 56, height: 56, mr: 2, bgcolor: 'primary.main' }}>
            <Person />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" noWrap>
              {student.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              كود الطالب: {student.studentCode}
            </Typography>
            <Chip
              label={student.isActive ? 'نشط' : 'غير نشط'}
              color={student.isActive ? 'success' : 'default'}
              size="small"
              sx={{ mt: 0.5 }}
            />
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Email fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2" noWrap>
                {student.email || 'غير محدد'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Phone fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2">
                {student.phone || 'غير محدد'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <CalendarToday fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2">
                انضم: {new Date(student.joinDate).toLocaleDateString('ar-SA')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <School fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2">
                الكورسات: {student.enrolledCourses?.length || 0}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {student.notes && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              ملاحظات: {student.notes}
            </Typography>
          </Box>
        )}
      </CardContent>

      <Box sx={{ p: 2, pt: 0 }}>
        <Grid container spacing={1}>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Visibility />}
              onClick={() => handleOpenDialog('view', student)}
            >
              عرض
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Edit />}
              onClick={() => handleOpenDialog('edit', student)}
            >
              تعديل
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              color="error"
              startIcon={<Delete />}
              onClick={() => handleDeleteStudent(student._id)}
            >
              حذف
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>جاري تحميل الطلاب...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">إدارة الطلاب</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog('add')}
        >
          إضافة طالب جديد
        </Button>
      </Box>

      <Grid container spacing={3}>
        {students.map((student) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={student._id}>
            <StudentCard student={student} />
          </Grid>
        ))}
      </Grid>

      {students.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            لا توجد طلاب
          </Typography>
          <Typography variant="body2" color="text.secondary">
            ابدأ بإضافة طالب جديد
          </Typography>
        </Box>
      )}

      {/* Dialog for Add/Edit/View Student */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'add' && 'إضافة طالب جديد'}
          {dialogType === 'edit' && 'تعديل الطالب'}
          {dialogType === 'view' && 'تفاصيل الطالب'}
        </DialogTitle>
        
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم الطالب"
                value={studentForm.name}
                onChange={(e) => setStudentForm({...studentForm, name: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="كود الطالب"
                value={studentForm.studentCode}
                onChange={(e) => setStudentForm({...studentForm, studentCode: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={studentForm.email}
                onChange={(e) => setStudentForm({...studentForm, email: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={studentForm.phone}
                onChange={(e) => setStudentForm({...studentForm, phone: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="ملاحظات"
                value={studentForm.notes}
                onChange={(e) => setStudentForm({...studentForm, notes: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          {dialogType !== 'view' && (
            <Button variant="contained" onClick={handleSaveStudent}>
              {dialogType === 'add' ? 'إضافة' : 'حفظ'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagementSimple;
