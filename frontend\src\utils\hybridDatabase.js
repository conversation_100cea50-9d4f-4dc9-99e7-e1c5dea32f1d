// نظام قاعدة البيانات الهجين - Firestore مع ميزات SQL
// Hybrid Database System - Firestore with SQL-like features

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * إعداد قاعدة البيانات الهجين التلقائي
 */
export const setupHybridDatabase = async () => {
  console.log('🚀 بدء إعداد قاعدة البيانات الهجين...');
  console.log('📊 استخدام Firestore مع ميزات SQL متقدمة');
  
  try {
    // الخطوة 1: مسح البيانات القديمة
    console.log('🗑️ مسح البيانات القديمة...');
    await clearOldData();

    // الخطوة 2: إنشاء هيكل قاعدة البيانات الجديد
    console.log('🏗️ إنشاء هيكل قاعدة البيانات...');
    await createDatabaseStructure();

    // الخطوة 3: إدراج البيانات الأساسية
    console.log('📊 إدراج البيانات الأساسية...');
    await insertInitialData();

    // الخطوة 4: إنشاء الفهارس والعلاقات
    console.log('🔗 إنشاء الفهارس والعلاقات...');
    await createIndexesAndRelations();

    // الخطوة 5: التحقق من البيانات
    console.log('✅ التحقق من البيانات...');
    const verification = await verifyHybridSetup();

    if (verification.success) {
      console.log('🎉 تم إعداد قاعدة البيانات الهجين بنجاح!');
      console.log('');
      console.log('📊 إحصائيات قاعدة البيانات:');
      console.log(`👨‍💼 المدراء: ${verification.stats.admins}`);
      console.log(`👨‍🎓 الطلاب: ${verification.stats.students}`);
      console.log(`📚 الكورسات: ${verification.stats.courses}`);
      console.log(`🎥 الفيديوهات: ${verification.stats.videos}`);
      console.log(`❓ الأسئلة الشائعة: ${verification.stats.faqs}`);
      console.log(`📜 الشهادات: ${verification.stats.certificates}`);
      console.log(`📝 التسجيلات: ${verification.stats.enrollments}`);
      
      console.log('');
      console.log('🌐 المشروع المنشور:');
      console.log('الموقع: https://marketwise-academy-qhizq.web.app');
      
      console.log('');
      console.log('🔑 بيانات تسجيل الدخول:');
      console.log('👨‍💼 المدير:');
      console.log('   البريد: <EMAIL>');
      console.log('   كلمة المرور: Admin123!');
      
      console.log('');
      console.log('👨‍🎓 أكواد الطلاب للاختبار:');
      console.log('   أحمد محمد علي: 123456');
      console.log('   فاطمة أحمد حسن: 654321');
      console.log('   محمد عبد الله: 111111');
      console.log('   سارة أحمد: 222222');
      console.log('   يوسف محمد: 333333');
      console.log('   نور الهدى: 444444');
      console.log('   خالد أحمد: 555555');

      console.log('');
      console.log('🧪 اختبار النظام:');
      console.log('testHybridLogin("123456") - اختبار تسجيل دخول طالب');
      console.log('checkHybridData() - فحص البيانات');
      console.log('getHybridStats() - إحصائيات قاعدة البيانات');

      return {
        success: true,
        message: 'تم إعداد قاعدة البيانات الهجين بنجاح',
        stats: verification.stats,
        type: 'hybrid-firestore'
      };
    } else {
      throw new Error('فشل في التحقق من الإعداد');
    }

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات الهجين:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};

/**
 * مسح البيانات القديمة
 */
const clearOldData = async () => {
  const collections = ['users', 'courses', 'enrollments', 'certificates', 'faqs', 'settings', 'activities'];
  
  for (const collectionName of collections) {
    try {
      const snapshot = await getDocs(collection(db, collectionName));
      const batch = writeBatch(db);
      
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      if (snapshot.docs.length > 0) {
        await batch.commit();
        console.log(`✅ تم مسح مجموعة ${collectionName} - ${snapshot.docs.length} عنصر`);
      }
    } catch (error) {
      console.log(`⚠️ تحذير: ${collectionName} - ${error.message}`);
    }
  }
};

/**
 * إنشاء هيكل قاعدة البيانات
 */
const createDatabaseStructure = async () => {
  // إنشاء مجموعة الإعدادات مع البيانات الأساسية
  const settingsData = {
    academy_name: 'SKILLS WORLD ACADEMY',
    admin_name: 'علاء عبد الحميد',
    admin_email: 'ALAA <EMAIL>',
    admin_phone: '0506747770',
    academy_version: '2.0.0',
    database_type: 'hybrid-firestore',
    setup_date: serverTimestamp(),
    project_id: 'marketwise-academy-qhizq',
    website_url: 'https://marketwise-academy-qhizq.web.app'
  };

  await addDoc(collection(db, 'settings'), {
    id: 'main-settings',
    ...settingsData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  console.log('✅ تم إنشاء هيكل قاعدة البيانات');
};

/**
 * إدراج البيانات الأساسية
 */
const insertInitialData = async () => {
  // إنشاء المدير
  const adminData = {
    id: 'admin-001',
    name: 'علاء عبد الحميد',
    email: '<EMAIL>',
    phone: '0506747770',
    role: 'admin',
    isActive: true,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  };

  await addDoc(collection(db, 'users'), adminData);
  console.log('👨‍💼 تم إنشاء المدير');

  // إنشاء الطلاب
  const students = [
    { id: 'student-001', name: 'أحمد محمد علي', email: '<EMAIL>', phone: '0501234567', studentCode: '123456' },
    { id: 'student-002', name: 'فاطمة أحمد حسن', email: '<EMAIL>', phone: '0507654321', studentCode: '654321' },
    { id: 'student-003', name: 'محمد عبد الله', email: '<EMAIL>', phone: '0509876543', studentCode: '111111' },
    { id: 'student-004', name: 'سارة أحمد', email: '<EMAIL>', phone: '0502468135', studentCode: '222222' },
    { id: 'student-005', name: 'يوسف محمد', email: '<EMAIL>', phone: '0508642097', studentCode: '333333' },
    { id: 'student-006', name: 'نور الهدى', email: '<EMAIL>', phone: '0501357924', studentCode: '444444' },
    { id: 'student-007', name: 'خالد أحمد', email: '<EMAIL>', phone: '0509753186', studentCode: '555555' }
  ];

  const batch = writeBatch(db);
  students.forEach(student => {
    const docRef = doc(collection(db, 'users'));
    batch.set(docRef, {
      ...student,
      role: 'student',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  });

  await batch.commit();
  console.log(`👨‍🎓 تم إنشاء ${students.length} طالب`);

  // إنشاء الكورسات
  const courses = [
    {
      id: 'course-001',
      title: 'مقدمة في التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين',
      instructor: 'علاء عبد الحميد',
      duration: '4 ساعات',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 4,
      videos: [
        { id: 1, title: 'مقدمة في التسويق الرقمي', duration: '15:00', order: 1, isFree: true },
        { id: 2, title: 'استراتيجيات التسويق الرقمي', duration: '25:00', order: 2, isFree: false },
        { id: 3, title: 'أدوات التسويق الرقمي', duration: '20:00', order: 3, isFree: false },
        { id: 4, title: 'قياس النتائج والتحليل', duration: '18:00', order: 4, isFree: false }
      ]
    },
    {
      id: 'course-002',
      title: 'إدارة وسائل التواصل الاجتماعي',
      description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية',
      instructor: 'علاء عبد الحميد',
      duration: '6 ساعات',
      level: 'متوسط',
      price: 0,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 5,
      videos: [
        { id: 1, title: 'مقدمة في وسائل التواصل', duration: '12:00', order: 1, isFree: true },
        { id: 2, title: 'استراتيجية المحتوى', duration: '22:00', order: 2, isFree: false },
        { id: 3, title: 'التفاعل مع الجمهور', duration: '18:00', order: 3, isFree: false },
        { id: 4, title: 'الإعلانات المدفوعة', duration: '24:00', order: 4, isFree: false },
        { id: 5, title: 'تحليل الأداء', duration: '16:00', order: 5, isFree: false }
      ]
    },
    {
      id: 'course-003',
      title: 'التجارة الإلكترونية للمبتدئين',
      description: 'دليل شامل لبدء متجرك الإلكتروني من الصفر',
      instructor: 'علاء عبد الحميد',
      duration: '8 ساعات',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 6,
      videos: [
        { id: 1, title: 'مقدمة في التجارة الإلكترونية', duration: '10:00', order: 1, isFree: true },
        { id: 2, title: 'اختيار المنتجات المربحة', duration: '20:00', order: 2, isFree: false },
        { id: 3, title: 'إنشاء المتجر الإلكتروني', duration: '28:00', order: 3, isFree: false },
        { id: 4, title: 'استراتيجيات التسويق للمتجر', duration: '25:00', order: 4, isFree: false },
        { id: 5, title: 'خدمة العملاء والشحن', duration: '18:00', order: 5, isFree: false },
        { id: 6, title: 'تحليل المبيعات والأرباح', duration: '15:00', order: 6, isFree: false }
      ]
    },
    {
      id: 'course-004',
      title: 'تحليل البيانات التسويقية',
      description: 'تعلم كيفية جمع وتحليل البيانات التسويقية لاتخاذ قرارات مدروسة',
      instructor: 'علاء عبد الحميد',
      duration: '5 ساعات',
      level: 'متقدم',
      price: 0,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 4,
      videos: [
        { id: 1, title: 'مقدمة في تحليل البيانات', duration: '14:00', order: 1, isFree: true },
        { id: 2, title: 'أدوات جمع البيانات', duration: '22:00', order: 2, isFree: false },
        { id: 3, title: 'تحليل سلوك العملاء', duration: '26:00', order: 3, isFree: false },
        { id: 4, title: 'إنشاء التقارير والرؤى', duration: '20:00', order: 4, isFree: false }
      ]
    }
  ];

  const coursesBatch = writeBatch(db);
  courses.forEach(course => {
    const docRef = doc(collection(db, 'courses'));
    coursesBatch.set(docRef, {
      ...course,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  });

  await coursesBatch.commit();
  console.log(`📚 تم إنشاء ${courses.length} كورس`);

  // إنشاء الأسئلة الشائعة
  const faqs = [
    { question: 'كيف يمكنني التسجيل في الكورسات؟', answer: 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير', category: 'التسجيل', order: 1 },
    { question: 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟', answer: 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات', category: 'المشاهدة', order: 2 },
    { question: 'كيف أحصل على شهادة إتمام الكورس؟', answer: 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس', category: 'الشهادات', order: 3 },
    { question: 'ماذا أفعل إذا نسيت كود الطالب؟', answer: 'تواصل مع المدير للحصول على كود الطالب الخاص بك', category: 'الدعم', order: 4 },
    { question: 'هل يمكنني تحميل الفيديوهات؟', answer: 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط', category: 'المشاهدة', order: 5 },
    { question: 'كيف يمكنني تتبع تقدمي في الكورس؟', answer: 'يمكنك رؤية تقدمك في لوحة التحكم الخاصة بك', category: 'التقدم', order: 6 },
    { question: 'هل هناك مدة محددة لإنهاء الكورس؟', answer: 'لا، يمكنك إنهاء الكورس في الوقت الذي يناسبك', category: 'المشاهدة', order: 7 },
    { question: 'كيف يمكنني التواصل مع المدرب؟', answer: 'يمكنك استخدام نظام الدردشة المدمج في المنصة', category: 'الدعم', order: 8 }
  ];

  const faqsBatch = writeBatch(db);
  faqs.forEach(faq => {
    const docRef = doc(collection(db, 'faqs'));
    faqsBatch.set(docRef, {
      ...faq,
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  });

  await faqsBatch.commit();
  console.log(`❓ تم إنشاء ${faqs.length} سؤال شائع`);
};

/**
 * إنشاء الفهارس والعلاقات
 */
const createIndexesAndRelations = async () => {
  // إنشاء بعض التسجيلات التجريبية
  const enrollments = [
    { studentId: 'student-001', courseId: 'course-001', progress: 75 },
    { studentId: 'student-002', courseId: 'course-001', progress: 50 },
    { studentId: 'student-003', courseId: 'course-002', progress: 25 },
    { studentId: 'student-001', courseId: 'course-002', progress: 100 },
    { studentId: 'student-004', courseId: 'course-003', progress: 60 }
  ];

  const enrollmentsBatch = writeBatch(db);
  enrollments.forEach(enrollment => {
    const docRef = doc(collection(db, 'enrollments'));
    enrollmentsBatch.set(docRef, {
      ...enrollment,
      enrolledAt: serverTimestamp(),
      isCompleted: enrollment.progress === 100,
      completedAt: enrollment.progress === 100 ? serverTimestamp() : null
    });
  });

  await enrollmentsBatch.commit();
  console.log(`📝 تم إنشاء ${enrollments.length} تسجيل`);

  // إنشاء شهادات للطلاب المكملين
  const certificates = [
    { studentId: 'student-001', courseId: 'course-002', certificateNumber: 'CERT-2024-001' }
  ];

  const certsBatch = writeBatch(db);
  certificates.forEach(cert => {
    const docRef = doc(collection(db, 'certificates'));
    certsBatch.set(docRef, {
      ...cert,
      issuedAt: serverTimestamp(),
      isValid: true
    });
  });

  await certsBatch.commit();
  console.log(`📜 تم إنشاء ${certificates.length} شهادة`);
};

/**
 * التحقق من الإعداد الهجين
 */
const verifyHybridSetup = async () => {
  try {
    const stats = {
      admins: 0,
      students: 0,
      courses: 0,
      videos: 0,
      faqs: 0,
      certificates: 0,
      enrollments: 0
    };

    // عد المدراء
    const adminsQuery = query(collection(db, 'users'), where('role', '==', 'admin'));
    const adminsSnapshot = await getDocs(adminsQuery);
    stats.admins = adminsSnapshot.size;

    // عد الطلاب
    const studentsQuery = query(collection(db, 'users'), where('role', '==', 'student'));
    const studentsSnapshot = await getDocs(studentsQuery);
    stats.students = studentsSnapshot.size;

    // عد الكورسات
    const coursesSnapshot = await getDocs(collection(db, 'courses'));
    stats.courses = coursesSnapshot.size;

    // حساب إجمالي الفيديوهات
    coursesSnapshot.docs.forEach(doc => {
      const courseData = doc.data();
      stats.videos += courseData.totalVideos || 0;
    });

    // عد الأسئلة الشائعة
    const faqsSnapshot = await getDocs(collection(db, 'faqs'));
    stats.faqs = faqsSnapshot.size;

    // عد الشهادات
    const certificatesSnapshot = await getDocs(collection(db, 'certificates'));
    stats.certificates = certificatesSnapshot.size;

    // عد التسجيلات
    const enrollmentsSnapshot = await getDocs(collection(db, 'enrollments'));
    stats.enrollments = enrollmentsSnapshot.size;

    return {
      success: true,
      stats
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * اختبار تسجيل دخول هجين
 */
export const testHybridLogin = async (studentCode) => {
  try {
    console.log(`🔍 اختبار تسجيل دخول الطالب: ${studentCode}`);
    
    const studentsQuery = query(
      collection(db, 'users'),
      where('studentCode', '==', studentCode),
      where('role', '==', 'student'),
      limit(1)
    );
    
    const snapshot = await getDocs(studentsQuery);
    
    if (snapshot.empty) {
      console.log('❌ لم يتم العثور على طالب بهذا الكود');
      return { success: false, message: 'كود التسجيل غير صحيح' };
    }
    
    const studentDoc = snapshot.docs[0];
    const studentData = studentDoc.data();
    
    if (!studentData.isActive) {
      console.log('❌ حساب الطالب غير مفعل');
      return { success: false, message: 'حساب الطالب غير مفعل' };
    }
    
    // تحديث آخر تسجيل دخول
    await updateDoc(studentDoc.ref, {
      lastLogin: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    console.log('✅ نجح تسجيل الدخول!');
    console.log('بيانات الطالب:', {
      name: studentData.name,
      studentCode: studentData.studentCode,
      email: studentData.email
    });
    
    return {
      success: true,
      user: {
        id: studentDoc.id,
        ...studentData
      }
    };
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تسجيل الدخول:', error);
    return { success: false, message: error.message };
  }
};

/**
 * فحص بيانات هجين
 */
export const checkHybridData = async () => {
  try {
    console.log('📊 فحص بيانات قاعدة البيانات الهجين...');
    
    const verification = await verifyHybridSetup();
    
    if (verification.success) {
      console.log('✅ بيانات قاعدة البيانات:');
      console.log(verification.stats);
      return verification;
    } else {
      console.log('❌ فشل في جلب البيانات');
      return { success: false };
    }
  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
    return { success: false, message: error.message };
  }
};

/**
 * إحصائيات هجين
 */
export const getHybridStats = async () => {
  return await checkHybridData();
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.setupHybridDatabase = setupHybridDatabase;
window.testHybridLogin = testHybridLogin;
window.checkHybridData = checkHybridData;
window.getHybridStats = getHybridStats;

export default {
  setupHybridDatabase,
  testHybridLogin,
  checkHybridData,
  getHybridStats
};
