const functions = require('firebase-functions');
const admin = require('firebase-admin');
const cors = require('cors')({ origin: true });

// استيراد خدمات SMS والأمان
const SMSService = require('./services/smsService');
const MockSMSService = require('./services/mockSMSService');
const SecurityService = require('./services/securityService');

// إعدادات المدير المسجل
const ADMIN_PHONE = '0506747770'; // رقم هاتف المدير
const ADMIN_PASSWORD = 'Admin123!'; // كلمة مرور المدير
const RATE_LIMIT_WINDOW = 60 * 1000; // 60 ثانية
const MAX_ATTEMPTS_PER_WINDOW = 3; // 3 محاولات كحد أقصى

// تخزين محاولات الإرسال لمنع الإرسال المتكرر
const attemptTracker = new Map();

/**
 * دالة استرداد كلمة المرور
 */
exports.forgotPassword = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      // التحقق من طريقة الطلب
      if (req.method !== 'POST') {
        return res.status(405).json({
          success: false,
          error: 'METHOD_NOT_ALLOWED',
          message: 'يُسمح فقط بطلبات POST'
        });
      }

      const { phoneNumber, timestamp } = req.body;

      // التحقق من وجود البيانات المطلوبة
      if (!phoneNumber) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PHONE',
          message: 'رقم الهاتف مطلوب'
        });
      }

      // تنظيف وتنسيق رقم الهاتف
      const cleanPhone = normalizePhoneNumber(phoneNumber);
      const adminPhone = normalizePhoneNumber(ADMIN_PHONE);

      console.log(`📱 طلب استرداد كلمة المرور للرقم: ${cleanPhone}`);
      console.log(`🔍 رقم المدير المسجل: ${adminPhone}`);

      // التحقق من أن الرقم مطابق لرقم المدير
      if (cleanPhone !== adminPhone) {
        console.log('❌ رقم الهاتف غير مطابق لرقم المدير');
        
        // تسجيل محاولة غير صحيحة
        await SecurityService.logSecurityEvent('INVALID_PHONE_ATTEMPT', {
          attemptedPhone: cleanPhone,
          adminPhone: adminPhone,
          identifier: cleanPhone
        }, req.ip || req.connection.remoteAddress, req.get('User-Agent'));

        return res.status(400).json({
          success: false,
          error: 'PHONE_NOT_FOUND',
          message: 'رقم الهاتف غير مطابق لرقم هاتف المدير المسجل'
        });
      }

      // التحقق من الحد الأقصى للمحاولات
      const rateLimitResult = checkRateLimit(cleanPhone);
      if (!rateLimitResult.allowed) {
        console.log(`⏰ تم تجاوز الحد الأقصى للمحاولات للرقم: ${cleanPhone}`);
        
        return res.status(429).json({
          success: false,
          error: 'RATE_LIMITED',
          message: `تم تجاوز الحد الأقصى من المحاولات. يرجى المحاولة بعد ${Math.ceil(rateLimitResult.retryAfter / 1000)} ثانية`,
          retryAfter: Math.ceil(rateLimitResult.retryAfter / 1000)
        });
      }

      // تحديد خدمة SMS المستخدمة
      const smsService = SMSService.isAvailable() ? SMSService : MockSMSService;
      
      if (smsService === MockSMSService) {
        console.log('🧪 استخدام خدمة SMS وهمية للاختبار');
        MockSMSService.showMockWarning();
      }

      // إرسال كلمة المرور
      console.log('📤 بدء إرسال كلمة المرور...');
      const smsResult = await smsService.sendPasswordReset(cleanPhone, ADMIN_PASSWORD);

      if (smsResult.success) {
        console.log('✅ تم إرسال كلمة المرور بنجاح');
        
        // تسجيل العملية الناجحة
        await SecurityService.logSecurityEvent('PASSWORD_RESET_SENT', {
          phone: cleanPhone,
          messageId: smsResult.messageId,
          mock: smsResult.mock || false,
          identifier: cleanPhone
        }, req.ip || req.connection.remoteAddress, req.get('User-Agent'));

        // تحديث عداد المحاولات
        updateAttemptTracker(cleanPhone);

        return res.status(200).json({
          success: true,
          message: 'تم إرسال كلمة المرور إلى رقم هاتفك بنجاح',
          messageId: smsResult.messageId,
          mock: smsResult.mock || false
        });

      } else {
        console.error('❌ فشل في إرسال كلمة المرور:', smsResult.error);
        
        // تسجيل الفشل
        await SecurityService.logSecurityEvent('PASSWORD_RESET_FAILED', {
          phone: cleanPhone,
          error: smsResult.error,
          code: smsResult.code,
          identifier: cleanPhone
        }, req.ip || req.connection.remoteAddress, req.get('User-Agent'));

        return res.status(500).json({
          success: false,
          error: 'SMS_SEND_FAILED',
          message: 'فشل في إرسال الرسالة. يرجى المحاولة لاحقاً'
        });
      }

    } catch (error) {
      console.error('❌ خطأ في دالة استرداد كلمة المرور:', error);
      
      // تسجيل الخطأ
      await SecurityService.logSecurityEvent('PASSWORD_RESET_ERROR', {
        error: error.message,
        stack: error.stack
      }, req.ip || req.connection.remoteAddress, req.get('User-Agent'));

      return res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'حدث خطأ داخلي. يرجى المحاولة لاحقاً'
      });
    }
  });
});

/**
 * تنسيق رقم الهاتف للصيغة الدولية
 * @param {string} phoneNumber - رقم الهاتف
 * @returns {string} رقم الهاتف المنسق
 */
function normalizePhoneNumber(phoneNumber) {
  // إزالة المسافات والرموز
  let cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
  
  // تحويل للصيغة الدولية
  if (cleanPhone.startsWith('05')) {
    return '+966' + cleanPhone.substring(1);
  } else if (cleanPhone.startsWith('+966')) {
    return cleanPhone;
  } else if (cleanPhone.startsWith('966')) {
    return '+' + cleanPhone;
  } else if (cleanPhone.startsWith('00966')) {
    return '+' + cleanPhone.substring(2);
  }
  
  return cleanPhone;
}

/**
 * التحقق من الحد الأقصى للمحاولات
 * @param {string} phoneNumber - رقم الهاتف
 * @returns {Object} نتيجة التحقق
 */
function checkRateLimit(phoneNumber) {
  const now = Date.now();
  const key = phoneNumber;
  
  if (!attemptTracker.has(key)) {
    return { allowed: true };
  }
  
  const attempts = attemptTracker.get(key);
  
  // إزالة المحاولات القديمة
  const recentAttempts = attempts.filter(time => now - time < RATE_LIMIT_WINDOW);
  
  if (recentAttempts.length >= MAX_ATTEMPTS_PER_WINDOW) {
    const oldestAttempt = Math.min(...recentAttempts);
    const retryAfter = RATE_LIMIT_WINDOW - (now - oldestAttempt);
    
    return {
      allowed: false,
      retryAfter: retryAfter
    };
  }
  
  return { allowed: true };
}

/**
 * تحديث عداد المحاولات
 * @param {string} phoneNumber - رقم الهاتف
 */
function updateAttemptTracker(phoneNumber) {
  const now = Date.now();
  const key = phoneNumber;
  
  if (!attemptTracker.has(key)) {
    attemptTracker.set(key, []);
  }
  
  const attempts = attemptTracker.get(key);
  attempts.push(now);
  
  // الاحتفاظ بالمحاولات الحديثة فقط
  const recentAttempts = attempts.filter(time => now - time < RATE_LIMIT_WINDOW);
  attemptTracker.set(key, recentAttempts);
}



/**
 * دالة اختبار خدمة SMS
 */
exports.testSMS = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      if (req.method !== 'POST') {
        return res.status(405).json({
          success: false,
          message: 'يُسمح فقط بطلبات POST'
        });
      }

      const { phoneNumber } = req.body;

      if (!phoneNumber) {
        return res.status(400).json({
          success: false,
          message: 'رقم الهاتف مطلوب'
        });
      }

      // تحديد خدمة SMS المستخدمة
      const smsService = SMSService.isAvailable() ? SMSService : MockSMSService;
      
      console.log(`🧪 اختبار خدمة SMS للرقم: ${phoneNumber}`);
      
      const result = await smsService.sendTestMessage(phoneNumber);

      return res.status(200).json({
        success: result.success,
        message: result.success ? 'تم إرسال رسالة الاختبار بنجاح' : 'فشل في إرسال رسالة الاختبار',
        result: result,
        service: smsService === MockSMSService ? 'Mock SMS' : 'Twilio SMS'
      });

    } catch (error) {
      console.error('❌ خطأ في اختبار SMS:', error);
      
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ في اختبار الخدمة',
        error: error.message
      });
    }
  });
});

/**
 * دالة الحصول على إحصائيات الأمان
 */
exports.getSecurityStats = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      if (req.method !== 'GET') {
        return res.status(405).json({
          success: false,
          message: 'يُسمح فقط بطلبات GET'
        });
      }

      // استخدام خدمة الأمان للحصول على الإحصائيات
      const days = parseInt(req.query.days) || 7;
      const stats = await SecurityService.getSecurityStats(days);

      // جلب السجلات الحديثة
      const db = admin.firestore();
      const recentLogsSnapshot = await db.collection('security_logs')
        .orderBy('timestamp', 'desc')
        .limit(20)
        .get();

      const recentLogs = recentLogsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return res.status(200).json({
        success: true,
        stats,
        recentLogs,
        period: `آخر ${days} أيام`
      });

    } catch (error) {
      console.error('❌ خطأ في جلب إحصائيات الأمان:', error);

      return res.status(500).json({
        success: false,
        message: 'حدث خطأ في جلب الإحصائيات',
        error: error.message
      });
    }
  });
});

/**
 * دالة تنظيف السجلات القديمة (تعمل يومياً)
 */
exports.cleanupSecurityLogs = functions.pubsub.schedule('0 2 * * *').onRun(async (context) => {
  try {
    console.log('🧹 بدء تنظيف السجلات الأمنية القديمة...');

    const deletedCount = await SecurityService.cleanupOldLogs(90); // الاحتفاظ بـ 90 يوم

    console.log(`✅ تم تنظيف ${deletedCount} سجل أمني قديم`);

    return null;
  } catch (error) {
    console.error('❌ خطأ في تنظيف السجلات:', error);
    throw error;
  }
});
