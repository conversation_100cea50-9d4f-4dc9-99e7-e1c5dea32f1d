import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,

  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,

  Rating,
  LinearProgress
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  People,
  VideoLibrary,
  AccessTime,
  Category
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import { courseManagementService } from '../../firebase/adminServices';

const CourseManagementAdvanced = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('add'); // add, edit, view
  const [, setTabValue] = useState(0);
  const [courseForm, setCourseForm] = useState({
    title: '',
    description: '',
    price: '',
    originalPrice: '',
    level: 'مبتدئ',
    category: 'التسويق الرقمي',
    language: 'العربية',
    duration: '',
    requirements: [],
    whatYouWillLearn: [],
    tags: []
  });

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const coursesData = await courseManagementService.getAllCourses();
      setCourses(coursesData);
    } catch (error) {
      console.error('خطأ في جلب الكورسات:', error);
      toast.error('فشل في جلب الكورسات');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (type, course = null) => {
    setDialogType(type);
    setSelectedCourse(course);
    if (course && type === 'edit') {
      setCourseForm({
        title: course.title,
        description: course.description,
        price: course.price,
        originalPrice: course.originalPrice || '',
        level: course.level,
        category: course.category,
        language: course.language || 'العربية',
        duration: course.duration,
        requirements: course.requirements || [],
        whatYouWillLearn: course.whatYouWillLearn || [],
        tags: course.tags || []
      });
    } else {
      setCourseForm({
        title: '',
        description: '',
        price: '',
        originalPrice: '',
        level: 'مبتدئ',
        category: 'التسويق الرقمي',
        language: 'العربية',
        duration: '',
        requirements: [],
        whatYouWillLearn: [],
        tags: []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCourse(null);
    setTabValue(0);
  };

  const handleSaveCourse = async () => {
    try {
      if (dialogType === 'add') {
        await courseManagementService.createCourse(courseForm);
        const updatedCourses = await courseManagementService.getAllCourses();
        setCourses(updatedCourses);
        toast.success('تم إضافة الكورس بنجاح');
      } else if (dialogType === 'edit') {
        await courseManagementService.updateCourse(selectedCourse.id, courseForm);
        const updatedCourses = await courseManagementService.getAllCourses();
        setCourses(updatedCourses);
        toast.success('تم تحديث الكورس بنجاح');
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الكورس:', error);
      toast.error('فشل في حفظ الكورس');
    }
  };

  const handleDeleteCourse = async (courseId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الكورس؟')) {
      try {
        await axios.delete(`/admin/courses/${courseId}`);
        setCourses(courses.filter(c => c._id !== courseId));
        toast.success('تم حذف الكورس بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الكورس:', error);
        toast.error('فشل في حذف الكورس');
      }
    }
  };

  const CourseCard = ({ course }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ position: 'relative' }}>
        <Box
          sx={{
            height: 200,
            backgroundImage: `url(${course.thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundColor: '#f5f5f5'
          }}
        />
        <Chip
          label={course.isActive ? 'نشط' : 'غير نشط'}
          color={course.isActive ? 'success' : 'default'}
          size="small"
          sx={{ position: 'absolute', top: 8, right: 8 }}
        />
        <Chip
          label={`${course.discount}% خصم`}
          color="error"
          size="small"
          sx={{ position: 'absolute', top: 8, left: 8 }}
        />
      </Box>
      
      <CardContent sx={{ flexGrow: 1, p: 2 }}>
        <Typography variant="h6" gutterBottom noWrap>
          {course.title}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, height: 40, overflow: 'hidden' }}>
          {course.description}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Rating value={course.rating} readOnly size="small" />
          <Typography variant="body2" sx={{ ml: 1 }}>
            ({course.reviewsCount})
          </Typography>
        </Box>

        <Grid container spacing={1} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <People fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {course.enrolledStudents?.length || 0}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <VideoLibrary fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {course.totalVideos}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AccessTime fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {course.duration}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Category fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }} noWrap>
                {course.level}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" color="primary">
            {course.price} ريال
          </Typography>
          {course.originalPrice && (
            <Typography 
              variant="body2" 
              sx={{ ml: 1, textDecoration: 'line-through', color: 'text.secondary' }}
            >
              {course.originalPrice} ريال
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          {course.tags?.slice(0, 3).map((tag, index) => (
            <Chip key={index} label={tag} size="small" variant="outlined" />
          ))}
        </Box>
      </CardContent>

      <Box sx={{ p: 2, pt: 0 }}>
        <Grid container spacing={1}>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Visibility />}
              onClick={() => handleOpenDialog('view', course)}
            >
              عرض
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Edit />}
              onClick={() => handleOpenDialog('edit', course)}
            >
              تعديل
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              color="error"
              startIcon={<Delete />}
              onClick={() => handleDeleteCourse(course._id)}
            >
              حذف
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>جاري تحميل الكورسات...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">إدارة الكورسات</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog('add')}
        >
          إضافة كورس جديد
        </Button>
      </Box>

      <Grid container spacing={3}>
        {courses.map((course) => (
          <Grid item xs={12} sm={6} md={4} key={course._id}>
            <CourseCard course={course} />
          </Grid>
        ))}
      </Grid>

      {/* Dialog for Add/Edit/View Course */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'add' && 'إضافة كورس جديد'}
          {dialogType === 'edit' && 'تعديل الكورس'}
          {dialogType === 'view' && 'تفاصيل الكورس'}
        </DialogTitle>
        
        <DialogContent>
          {/* Content will be added in the next part */}
          <Typography>محتوى النافذة سيتم إضافته...</Typography>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          {dialogType !== 'view' && (
            <Button variant="contained" onClick={handleSaveCourse}>
              {dialogType === 'add' ? 'إضافة' : 'حفظ'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseManagementAdvanced;
