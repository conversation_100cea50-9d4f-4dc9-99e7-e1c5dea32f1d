/**
 * خدمة التزامن الفوري بين لوحة الإدارة وصفحة الطلاب
 * Real-time synchronization service between admin dashboard and student interface
 */

import { db } from '../firebase/config';
import { supabase } from '../supabase/config';
import {
  collection,
  onSnapshot,
  doc,
  updateDoc,
  addDoc,
  deleteDoc,
  serverTimestamp,
  query,
  where,
  orderBy,
  limit,
  increment,
  getDocs
} from 'firebase/firestore';
import toast from 'react-hot-toast';

/**
 * خدمة التزامن الفوري للكورسات
 */
export class RealTimeCoursesSync {
  constructor() {
    this.listeners = new Set();
    this.coursesCache = [];
  }

  // مراقبة تغييرات الكورسات في الوقت الفعلي
  watchCourses(callback) {
    console.log('🔄 بدء مراقبة الكورسات في الوقت الفعلي...');

    const unsubscribe = onSnapshot(
      collection(db, 'courses'),
      (snapshot) => {
        const courses = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));

        // ترتيب الكورسات حسب تاريخ الإنشاء
        courses.sort((a, b) => b.createdAt - a.createdAt);

        this.coursesCache = courses;

        // استدعاء callback الرئيسي
        if (typeof callback === 'function') {
          callback(courses);
        }

        console.log('✅ تم تحديث الكورسات في الوقت الفعلي:', courses.length);

        // إشعار جميع المستمعين الإضافيين
        this.listeners.forEach(listener => {
          if (typeof listener === 'function') {
            listener(courses);
          }
        });
      },
      (error) => {
        console.error('❌ خطأ في مراقبة الكورسات:', error);
        toast.error('فشل في مراقبة تحديثات الكورسات');
        // استدعاء callback مع مصفوفة فارغة في حالة الخطأ
        if (typeof callback === 'function') {
          callback([]);
        }
      }
    );

    return unsubscribe;
  }

  // إضافة كورس جديد مع تحديث فوري
  async addCourse(courseData) {
    try {
      console.log('➕ إضافة كورس جديد:', courseData.title);
      
      const docRef = await addDoc(collection(db, 'courses'), {
        ...courseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        isActive: true,
        enrolledStudents: 0,
        totalVideos: 0
      });

      console.log('✅ تم إضافة الكورس بنجاح:', docRef.id);
      
      // إشعار فوري للطلاب
      this.notifyStudentsOfNewCourse(docRef.id, courseData.title);
      
      return { success: true, id: docRef.id };
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      throw error;
    }
  }

  // تحديث كورس مع تزامن فوري
  async updateCourse(courseId, updateData) {
    try {
      console.log('🔄 تحديث الكورس:', courseId);
      
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });

      console.log('✅ تم تحديث الكورس بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تحديث الكورس:', error);
      throw error;
    }
  }

  // حذف كورس مع تحديث فوري
  async deleteCourse(courseId) {
    try {
      console.log('🗑️ حذف الكورس:', courseId);
      
      await deleteDoc(doc(db, 'courses', courseId));
      
      console.log('✅ تم حذف الكورس بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف الكورس:', error);
      throw error;
    }
  }

  // إشعار الطلاب بكورس جديد
  async notifyStudentsOfNewCourse(courseId, courseTitle) {
    try {
      // إضافة إشعار في قاعدة البيانات
      await addDoc(collection(db, 'notifications'), {
        type: 'new_course',
        title: 'كورس جديد متاح',
        message: `تم إضافة كورس جديد: ${courseTitle}`,
        courseId: courseId,
        targetAudience: 'students',
        isRead: false,
        createdAt: serverTimestamp()
      });

      console.log('📢 تم إرسال إشعار للطلاب بالكورس الجديد');
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعار:', error);
    }
  }

  // إضافة مستمع للتحديثات
  addListener(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // جلب جميع الكورسات
  async getAllCourses() {
    try {
      const snapshot = await getDocs(collection(db, 'courses'));
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('خطأ في جلب الكورسات:', error);
      return [];
    }
  }
}

/**
 * خدمة التزامن الفوري للتسجيلات
 */
export class RealTimeEnrollmentsSync {
  constructor() {
    this.listeners = new Set();
  }

  // مراقبة تغييرات التسجيلات في الوقت الفعلي
  watchEnrollments(callback) {
    console.log('🔄 بدء مراقبة التسجيلات في الوقت الفعلي...');

    const unsubscribe = onSnapshot(
      collection(db, 'enrollments'),
      (snapshot) => {
        const enrollments = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
          completedAt: doc.data().completedAt?.toDate?.() || null
        }));

        // ترتيب التسجيلات حسب تاريخ التسجيل
        enrollments.sort((a, b) => b.enrolledAt - a.enrolledAt);

        // استدعاء callback الرئيسي
        if (typeof callback === 'function') {
          callback(enrollments);
        }

        console.log('✅ تم تحديث التسجيلات في الوقت الفعلي:', enrollments.length);

        // إشعار جميع المستمعين الإضافيين
        this.listeners.forEach(listener => {
          if (typeof listener === 'function') {
            listener(enrollments);
          }
        });
      },
      (error) => {
        console.error('❌ خطأ في مراقبة التسجيلات:', error);
        toast.error('فشل في مراقبة تحديثات التسجيلات');
        // استدعاء callback مع مصفوفة فارغة في حالة الخطأ
        if (typeof callback === 'function') {
          callback([]);
        }
      }
    );

    return unsubscribe;
  }

  // تسجيل طالب في كورس مع تحديث فوري
  async enrollStudent(studentId, courseId) {
    try {
      console.log('📝 تسجيل طالب في كورس:', { studentId, courseId });
      
      // إضافة التسجيل
      const enrollmentRef = await addDoc(collection(db, 'enrollments'), {
        studentId,
        courseId,
        progress: 0,
        isCompleted: false,
        enrolledAt: serverTimestamp(),
        lastAccessed: serverTimestamp()
      });

      // تحديث عداد الطلاب في الكورس
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        enrolledStudents: increment(1),
        updatedAt: serverTimestamp()
      });

      // إشعار الطالب بالتسجيل الناجح
      await this.notifyStudentEnrollment(studentId, courseId);

      console.log('✅ تم تسجيل الطالب بنجاح');
      return { success: true, id: enrollmentRef.id };
    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      throw error;
    }
  }

  // إشعار الطالب بالتسجيل الناجح
  async notifyStudentEnrollment(studentId, courseId) {
    try {
      await addDoc(collection(db, 'notifications'), {
        type: 'enrollment_success',
        title: 'تم التسجيل بنجاح',
        message: 'تم تسجيلك في الكورس بنجاح. يمكنك البدء في التعلم الآن!',
        studentId: studentId,
        courseId: courseId,
        isRead: false,
        createdAt: serverTimestamp()
      });

      console.log('📢 تم إرسال إشعار للطالب بالتسجيل الناجح');
    } catch (error) {
      console.error('❌ خطأ في إرسال إشعار التسجيل:', error);
    }
  }

  // إضافة مستمع للتحديثات
  addListener(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // جلب جميع التسجيلات
  async getAllEnrollments() {
    try {
      const snapshot = await getDocs(collection(db, 'enrollments'));
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
        completedAt: doc.data().completedAt?.toDate?.() || null
      }));
    } catch (error) {
      console.error('خطأ في جلب التسجيلات:', error);
      return [];
    }
  }
}

/**
 * خدمة التزامن الفوري للإشعارات
 */
export class RealTimeNotificationsSync {
  // مراقبة الإشعارات للطلاب
  watchStudentNotifications(studentId, callback) {
    console.log('🔔 بدء مراقبة إشعارات الطالب:', studentId);
    
    const q = query(
      collection(db, 'notifications'),
      where('studentId', '==', studentId),
      orderBy('createdAt', 'desc'),
      limit(20)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date()
      }));

      callback(notifications);
      console.log('✅ تم تحديث إشعارات الطالب:', notifications.length);
    });

    return unsubscribe;
  }

  // مراقبة الإشعارات العامة للطلاب
  watchGeneralNotifications(callback) {
    console.log('🔔 بدء مراقبة الإشعارات العامة...');
    
    const q = query(
      collection(db, 'notifications'),
      where('targetAudience', '==', 'students'),
      orderBy('createdAt', 'desc'),
      limit(10)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date()
      }));

      callback(notifications);
      console.log('✅ تم تحديث الإشعارات العامة:', notifications.length);
    });

    return unsubscribe;
  }

  // جلب جميع الإشعارات
  async getAllNotifications() {
    try {
      const snapshot = await getDocs(collection(db, 'notifications'));
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('خطأ في جلب الإشعارات:', error);
      return [];
    }
  }
}

// إنشاء instances للخدمات
export const realTimeCoursesSync = new RealTimeCoursesSync();
export const realTimeEnrollmentsSync = new RealTimeEnrollmentsSync();
export const realTimeNotificationsSync = new RealTimeNotificationsSync();

// إنشاء خدمة موحدة
export const realTimeSyncService = {
  watchCourses: (callback) => realTimeCoursesSync.watchCourses(callback),
  watchEnrollments: (callback) => realTimeEnrollmentsSync.watchEnrollments(callback),
  watchNotifications: (callback) => realTimeNotificationsSync.watchNotifications(callback),
  getAllCourses: () => realTimeCoursesSync.getAllCourses(),
  getAllEnrollments: () => realTimeEnrollmentsSync.getAllEnrollments(),
  getAllNotifications: () => realTimeNotificationsSync.getAllNotifications()
};

// تصدير الخدمات كـ default
export default {
  courses: realTimeCoursesSync,
  enrollments: realTimeEnrollmentsSync,
  notifications: realTimeNotificationsSync,
  realTimeSyncService
};
