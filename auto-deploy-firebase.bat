@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔥 النشر التلقائي على Firebase
echo ========================================
echo.

echo 📦 تثبيت Firebase CLI...
call npm install -g firebase-tools
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Firebase CLI
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت المكتبات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت مكتبات Functions...
cd functions
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت مكتبات Functions
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo 🎨 بناء الواجهة الأمامية...
cd frontend
call npm install
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الواجهة الأمامية
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo 🔐 تسجيل الدخول إلى Firebase...
echo يرجى تسجيل الدخول في المتصفح الذي سيفتح...
call firebase login
if %errorlevel% neq 0 (
    echo ❌ فشل في تسجيل الدخول
    pause
    exit /b 1
)

echo.
echo 🚀 إعداد مشروع Firebase...
echo اتبع التعليمات التالية:
echo 1. اختر "Create a new project"
echo 2. أدخل اسم المشروع: alaa-courses
echo 3. اختر المنطقة الأقرب لك
echo 4. فعل Firestore Database
echo 5. فعل Firebase Hosting
echo 6. فعل Cloud Functions
echo.
call firebase init
if %errorlevel% neq 0 (
    echo ❌ فشل في إعداد Firebase
    pause
    exit /b 1
)

echo.
echo 🚀 النشر على Firebase...
call firebase deploy
if %errorlevel% neq 0 (
    echo ❌ فشل في النشر
    pause
    exit /b 1
)

echo.
echo ✅ تم النشر بنجاح!
echo.
echo 🌐 المنصة متاحة الآن على:
call firebase hosting:channel:list
echo.
echo 👨‍💼 بيانات المدير الافتراضية:
echo البريد: <EMAIL>
echo كلمة المرور: Admin123!
echo.
echo 👨‍🎓 أكواد الطلاب التجريبية:
echo 123456, 789012
echo.
echo 📱 لإدارة المشروع:
echo Firebase Console: https://console.firebase.google.com
echo.
pause
