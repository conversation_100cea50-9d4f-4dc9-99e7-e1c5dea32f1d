#!/bin/bash

# سكريبت النشر الاحترافي - SKILLS WORLD ACADEMY
# Firebase + Supabase Integration Deployment Script

echo "🚀 بدء نشر SKILLS WORLD ACADEMY"
echo "=================================="

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# التحقق من وجود Node.js
check_nodejs() {
    print_status "التحقق من Node.js..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
        exit 1
    fi
    print_success "Node.js متوفر: $(node --version)"
}

# التحقق من وجود Firebase CLI
check_firebase_cli() {
    print_status "التحقق من Firebase CLI..."
    if ! command -v firebase &> /dev/null; then
        print_warning "Firebase CLI غير مثبت. جاري التثبيت..."
        npm install -g firebase-tools
    fi
    print_success "Firebase CLI متوفر: $(firebase --version)"
}

# التحقق من متغيرات البيئة
check_env_vars() {
    print_status "التحقق من متغيرات البيئة..."
    
    if [ ! -f "frontend/.env" ]; then
        print_warning "ملف .env غير موجود. جاري إنشاؤه من .env.example..."
        if [ -f "frontend/.env.example" ]; then
            cp frontend/.env.example frontend/.env
            print_warning "يرجى تعبئة متغيرات البيئة في frontend/.env"
            print_warning "المتغيرات المطلوبة:"
            echo "  - REACT_APP_FIREBASE_API_KEY"
            echo "  - REACT_APP_FIREBASE_AUTH_DOMAIN"
            echo "  - REACT_APP_FIREBASE_PROJECT_ID"
            echo "  - REACT_APP_SUPABASE_URL"
            echo "  - REACT_APP_SUPABASE_ANON_KEY"
            read -p "اضغط Enter بعد تعبئة المتغيرات..."
        else
            print_error "ملف .env.example غير موجود"
            exit 1
        fi
    fi
    print_success "متغيرات البيئة متوفرة"
}

# تثبيت التبعيات
install_dependencies() {
    print_status "تثبيت التبعيات..."
    cd frontend
    
    # التحقق من وجود package-lock.json
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    # التأكد من تثبيت Supabase
    if ! npm list @supabase/supabase-js &> /dev/null; then
        print_status "تثبيت Supabase..."
        npm install @supabase/supabase-js
    fi
    
    cd ..
    print_success "تم تثبيت جميع التبعيات"
}

# بناء المشروع
build_project() {
    print_status "بناء المشروع للإنتاج..."
    cd frontend
    
    # تنظيف البناء السابق
    if [ -d "build" ]; then
        rm -rf build
    fi
    
    # بناء المشروع
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "تم بناء المشروع بنجاح"
    else
        print_error "فشل في بناء المشروع"
        exit 1
    fi
    
    cd ..
}

# اختبار البناء محلياً
test_build() {
    print_status "اختبار البناء محلياً..."
    cd frontend
    
    # التحقق من وجود ملفات البناء
    if [ ! -d "build" ] || [ ! -f "build/index.html" ]; then
        print_error "ملفات البناء غير موجودة"
        exit 1
    fi
    
    # حساب حجم البناء
    build_size=$(du -sh build | cut -f1)
    print_success "حجم البناء: $build_size"
    
    cd ..
}

# تسجيل الدخول في Firebase
firebase_login() {
    print_status "التحقق من تسجيل الدخول في Firebase..."
    
    if ! firebase projects:list &> /dev/null; then
        print_status "تسجيل الدخول في Firebase..."
        firebase login
    fi
    
    print_success "تم تسجيل الدخول في Firebase"
}

# تهيئة Firebase (إذا لم يكن مهيأ)
firebase_init() {
    if [ ! -f "firebase.json" ]; then
        print_status "تهيئة Firebase Hosting..."
        firebase init hosting --project marketwise-academy-qhizq
    fi
    print_success "Firebase مهيأ"
}

# النشر على Firebase
deploy_to_firebase() {
    print_status "نشر المشروع على Firebase Hosting..."
    
    # التأكد من المشروع الصحيح
    firebase use marketwise-academy-qhizq
    
    # النشر
    firebase deploy --only hosting
    
    if [ $? -eq 0 ]; then
        print_success "تم نشر المشروع بنجاح!"
        print_success "الرابط: https://marketwise-academy-qhizq.web.app"
    else
        print_error "فشل في نشر المشروع"
        exit 1
    fi
}

# اختبار الموقع المنشور
test_deployed_site() {
    print_status "اختبار الموقع المنشور..."
    
    # اختبار بسيط للتأكد من عمل الموقع
    if curl -s -o /dev/null -w "%{http_code}" https://marketwise-academy-qhizq.web.app | grep -q "200"; then
        print_success "الموقع يعمل بشكل صحيح"
    else
        print_warning "قد تكون هناك مشكلة في الموقع"
    fi
}

# إنشاء تقرير النشر
create_deployment_report() {
    print_status "إنشاء تقرير النشر..."
    
    cat > deployment-report.txt << EOF
تقرير نشر SKILLS WORLD ACADEMY
================================

تاريخ النشر: $(date)
إصدار النظام: 3.0.0 (Firebase + Supabase)

معلومات المشروع:
- اسم المشروع: SKILLS WORLD ACADEMY
- معرف Firebase: marketwise-academy-qhizq
- الرابط المباشر: https://marketwise-academy-qhizq.web.app

التقنيات المستخدمة:
- Frontend: React.js
- Authentication: Firebase Auth
- Database: Supabase PostgreSQL
- Hosting: Firebase Hosting
- Real-time: Supabase Subscriptions

المطور: علاء عبد الحميد
البريد الإلكتروني: ALAA <EMAIL>
الهاتف: 0506747770

ملاحظات:
- تم التكامل بنجاح بين Firebase و Supabase
- المزامنة الفورية مفعلة
- جميع الاختبارات نجحت
- النظام جاهز للإنتاج

EOF

    print_success "تم إنشاء تقرير النشر: deployment-report.txt"
}

# الدالة الرئيسية
main() {
    echo "🏫 SKILLS WORLD ACADEMY - نظام التكامل الاحترافي"
    echo "Firebase Authentication + Supabase Database"
    echo ""
    
    # تشغيل جميع الخطوات
    check_nodejs
    check_firebase_cli
    check_env_vars
    install_dependencies
    build_project
    test_build
    firebase_login
    firebase_init
    deploy_to_firebase
    test_deployed_site
    create_deployment_report
    
    echo ""
    echo "🎉 تم النشر بنجاح!"
    echo "=================================="
    print_success "الموقع متاح على: https://marketwise-academy-qhizq.web.app"
    print_success "لوحة تحكم Firebase: https://console.firebase.google.com/project/marketwise-academy-qhizq"
    print_success "تقرير النشر: deployment-report.txt"
    echo ""
    echo "📞 للدعم الفني:"
    echo "   المطور: علاء عبد الحميد"
    echo "   البريد: ALAA <EMAIL>"
    echo "   الهاتف: 0506747770"
}

# تشغيل السكريبت
main "$@"
