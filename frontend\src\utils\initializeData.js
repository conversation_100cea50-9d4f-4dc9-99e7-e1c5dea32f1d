import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  query, 
  where, 
  limit,
  serverTimestamp 
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword 
} from 'firebase/auth';
import { db, auth } from '../firebase/config';

// Default data
const defaultData = {
  admin: {
    name: 'علاء عبد الحميد',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    isActive: true
  },

  categories: [
    {
      name: 'التسويق الرقمي',
      description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
      icon: 'digital-marketing',
      color: '#2196F3',
      order: 1,
      isActive: true
    },
    {
      name: 'وسائل التواصل الاجتماعي',
      description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
      icon: 'social-media',
      color: '#4CAF50',
      order: 2,
      isActive: true
    },
    {
      name: 'التسويق بالمحتوى',
      description: 'إنشاء وتسويق المحتوى الفعال',
      icon: 'content-marketing',
      color: '#FF9800',
      order: 3,
      isActive: true
    },
    {
      name: 'التجارة الإلكترونية',
      description: 'بناء وإدارة المتاجر الإلكترونية',
      icon: 'ecommerce',
      color: '#9C27B0',
      order: 4,
      isActive: true
    }
  ],

  students: [
    {
      name: 'أحمد محمد علي',
      studentCode: '123456',
      role: 'student',
      isActive: true,
      enrolledCourses: [],
      progress: {}
    },
    {
      name: 'فاطمة علي حسن',
      studentCode: '789012',
      role: 'student',
      isActive: true,
      enrolledCourses: [],
      progress: {}
    },
    {
      name: 'محمد عبدالله أحمد',
      studentCode: '345678',
      role: 'student',
      isActive: true,
      enrolledCourses: [],
      progress: {}
    }
  ],

  courses: [
    {
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف مع أحدث الاستراتيجيات والأدوات',
      shortDescription: 'كورس شامل في أساسيات التسويق الرقمي',
      instructor: 'علاء عبد الحميد',
      thumbnail: '/images/digital-marketing-basics.jpg',
      level: 'مبتدئ',
      language: 'العربية',
      duration: '8 ساعات',
      totalVideos: 24,
      price: 299,
      originalPrice: 499,
      discount: 40,
      tags: ['تسويق', 'رقمي', 'مبتدئ', 'أساسيات'],
      requirements: [
        'لا توجد متطلبات مسبقة',
        'جهاز كمبيوتر أو هاتف ذكي',
        'اتصال بالإنترنت'
      ],
      whatYouWillLearn: [
        'فهم أساسيات التسويق الرقمي',
        'إنشاء استراتيجية تسويقية فعالة',
        'استخدام وسائل التواصل الاجتماعي للتسويق',
        'تحليل البيانات وقياس النتائج',
        'إنشاء حملات إعلانية ناجحة'
      ],
      lessons: [
        {
          title: 'مقدمة في التسويق الرقمي',
          description: 'تعرف على أساسيات التسويق الرقمي وأهميته',
          duration: 900,
          order: 1,
          isPreview: true
        },
        {
          title: 'الفرق بين التسويق التقليدي والرقمي',
          description: 'مقارنة بين أساليب التسويق التقليدية والرقمية',
          duration: 765,
          order: 2,
          isPreview: false
        }
      ],
      isActive: true,
      rating: 4.8,
      reviewsCount: 156,
      enrolledStudents: 0
    }
  ]
};

// Initialize Firebase data
export const initializeFirebaseData = async () => {
  try {
    console.log('🔧 بدء تهيئة البيانات الافتراضية...');

    // Check if data already exists
    const usersRef = collection(db, 'users');
    const adminQuery = query(usersRef, where('role', '==', 'admin'), limit(1));
    const adminSnapshot = await getDocs(adminQuery);

    if (!adminSnapshot.empty) {
      console.log('ℹ️ البيانات موجودة بالفعل');
      return { success: true, message: 'البيانات موجودة بالفعل' };
    }

    // Create admin user in Authentication
    try {
      const adminCredential = await createUserWithEmailAndPassword(
        auth, 
        defaultData.admin.email, 
        defaultData.admin.password
      );
      
      // Add admin user to Firestore
      await addDoc(usersRef, {
        uid: adminCredential.user.uid,
        name: defaultData.admin.name,
        email: defaultData.admin.email,
        role: defaultData.admin.role,
        isActive: defaultData.admin.isActive,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      console.log('👨‍💼 تم إنشاء حساب المدير');
    } catch (authError) {
      console.log('⚠️ المدير موجود في Authentication، إضافة البيانات فقط...');
      
      // Add admin user to Firestore without auth
      await addDoc(usersRef, {
        name: defaultData.admin.name,
        email: defaultData.admin.email,
        role: defaultData.admin.role,
        isActive: defaultData.admin.isActive,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }

    // Add categories
    const categoriesRef = collection(db, 'categories');
    for (const category of defaultData.categories) {
      await addDoc(categoriesRef, {
        ...category,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log('📂 تم إنشاء الأقسام الافتراضية');

    // Add students
    for (const student of defaultData.students) {
      await addDoc(usersRef, {
        ...student,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log('👥 تم إنشاء الطلاب التجريبيين');

    // Add courses
    const coursesRef = collection(db, 'courses');
    for (const course of defaultData.courses) {
      await addDoc(coursesRef, {
        ...course,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log('📚 تم إنشاء الكورسات التجريبية');

    console.log('✅ تم تهيئة جميع البيانات الافتراضية بنجاح!');
    
    return { 
      success: true, 
      message: 'تم تهيئة البيانات بنجاح',
      data: {
        admin: 1,
        categories: defaultData.categories.length,
        students: defaultData.students.length,
        courses: defaultData.courses.length
      }
    };

  } catch (error) {
    console.error('❌ خطأ في تهيئة البيانات:', error);
    return { 
      success: false, 
      message: 'خطأ في تهيئة البيانات: ' + error.message 
    };
  }
};

// Test Firebase connection
export const testFirebaseConnection = async () => {
  try {
    console.log('🧪 اختبار الاتصال بـ Firebase...');
    
    // Test Firestore
    const testCollection = collection(db, 'test');
    console.log('✅ Firestore متصل');
    
    // Test Auth
    const currentUser = auth.currentUser;
    console.log('✅ Firebase Auth متصل');
    
    return { 
      success: true, 
      firestore: true, 
      auth: true,
      user: currentUser ? currentUser.email : null
    };
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

export default { initializeFirebaseData, testFirebaseConnection, defaultData };
