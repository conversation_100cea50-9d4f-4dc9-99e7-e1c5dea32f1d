-- قاعدة بيانات منصة كورسات علاء عبد الحميد - PlanetScale
-- تم تحسينها للعمل مع مشروع marketwise-academy-qhizq

-- جدول المستخدمين (المدراء والطلاب)
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('admin', 'student') NOT NULL,
    student_code VARCHAR(6),
    password_hash VARCHAR(255),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_email (email),
    UNIQUE KEY unique_student_code (student_code),
    INDEX idx_role (role),
    INDEX idx_active (is_active),
    INDEX idx_student_code_lookup (student_code, role, is_active)
);

-- جدول الكورسات
CREATE TABLE courses (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    level ENUM('مبتدئ', 'متوسط', 'متقدم') DEFAULT 'مبتدئ',
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    enrolled_students INT DEFAULT 0,
    total_videos INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_instructor (instructor),
    INDEX idx_level (level)
);

-- جدول فيديوهات الكورسات
CREATE TABLE course_videos (
    id VARCHAR(36) PRIMARY KEY,
    course_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT,
    duration_minutes INT DEFAULT 0,
    video_order INT NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_course_order (course_id, video_order),
    INDEX idx_course (course_id),
    INDEX idx_free_videos (course_id, is_free)
);

-- جدول تسجيل الطلاب في الكورسات
CREATE TABLE enrollments (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    last_accessed TIMESTAMP NULL,
    
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_completed (is_completed),
    INDEX idx_progress (student_id, progress_percentage)
);

-- جدول الشهادات
CREATE TABLE certificates (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    certificate_number VARCHAR(50) NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    certificate_url TEXT,
    is_valid BOOLEAN DEFAULT TRUE,
    
    UNIQUE KEY unique_certificate (student_id, course_id),
    UNIQUE KEY unique_cert_number (certificate_number),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_valid (is_valid)
);

-- جدول الأسئلة الشائعة
CREATE TABLE faqs (
    id VARCHAR(36) PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_active (is_active),
    INDEX idx_order (display_order),
    INDEX idx_category_active (category, is_active, display_order)
);

-- جدول الإعدادات العامة
CREATE TABLE settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_setting_key (setting_key),
    INDEX idx_public (is_public)
);

-- جدول الدردشة والدعم
CREATE TABLE chat_messages (
    id VARCHAR(36) PRIMARY KEY,
    sender_id VARCHAR(36) NOT NULL,
    receiver_id VARCHAR(36),
    message TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file') DEFAULT 'text',
    file_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_unread (receiver_id, is_read, sent_at)
);

-- جدول الأنشطة والسجلات
CREATE TABLE activity_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    metadata JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_user_action (user_id, action, created_at)
);

-- إدراج الإعدادات الأساسية
INSERT INTO settings (id, setting_key, setting_value, setting_type, description, is_public) VALUES
('set-001', 'academy_name', 'SKILLS WORLD ACADEMY', 'string', 'اسم الأكاديمية', TRUE),
('set-002', 'admin_name', 'علاء عبد الحميد', 'string', 'اسم المدير', TRUE),
('set-003', 'admin_email', 'ALAA <EMAIL>', 'string', 'بريد المدير', FALSE),
('set-004', 'admin_phone', '0506747770', 'string', 'هاتف المدير', FALSE),
('set-005', 'academy_version', '2.0.0', 'string', 'إصدار النظام', TRUE),
('set-006', 'firebase_project', 'marketwise-academy-qhizq', 'string', 'معرف مشروع Firebase', FALSE),
('set-007', 'database_type', 'sql', 'string', 'نوع قاعدة البيانات', FALSE),
('set-008', 'setup_date', NOW(), 'string', 'تاريخ الإعداد', FALSE);

-- إنشاء المدير الافتراضي
INSERT INTO users (id, name, email, phone, role, is_active, created_at, updated_at) VALUES
('admin-001', 'علاء عبد الحميد', '<EMAIL>', '0506747770', 'admin', TRUE, NOW(), NOW());

-- إنشاء طلاب تجريبيين مع أكواد محددة
INSERT INTO users (id, name, email, phone, role, student_code, is_active, created_at, updated_at) VALUES
('student-001', 'أحمد محمد علي', '<EMAIL>', '0501234567', 'student', '123456', TRUE, NOW(), NOW()),
('student-002', 'فاطمة أحمد حسن', '<EMAIL>', '0507654321', 'student', '654321', TRUE, NOW(), NOW()),
('student-003', 'محمد عبد الله', '<EMAIL>', '0509876543', 'student', '111111', TRUE, NOW(), NOW()),
('student-004', 'سارة أحمد', '<EMAIL>', '0502468135', 'student', '222222', TRUE, NOW(), NOW()),
('student-005', 'يوسف محمد', '<EMAIL>', '0508642097', 'student', '333333', TRUE, NOW(), NOW()),
('student-006', 'نور الهدى', '<EMAIL>', '0501357924', 'student', '444444', TRUE, NOW(), NOW()),
('student-007', 'خالد أحمد', '<EMAIL>', '0509753186', 'student', '555555', TRUE, NOW(), NOW());

-- إنشاء أسئلة شائعة شاملة
INSERT INTO faqs (id, question, answer, category, display_order, is_active, created_at, updated_at) VALUES
('faq-001', 'كيف يمكنني التسجيل في الكورسات؟', 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير. ادخل الكود في صفحة تسجيل الدخول للطلاب.', 'التسجيل', 1, TRUE, NOW(), NOW()),
('faq-002', 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟', 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات. تقدمك محفوظ ويمكنك العودة من حيث توقفت.', 'المشاهدة', 2, TRUE, NOW(), NOW()),
('faq-003', 'كيف أحصل على شهادة إتمام الكورس؟', 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس بنسبة 100%. الشهادة ستظهر في ملفك الشخصي.', 'الشهادات', 3, TRUE, NOW(), NOW()),
('faq-004', 'ماذا أفعل إذا نسيت كود الطالب؟', 'تواصل مع المدير عبر البريد الإلكتروني ALAA <EMAIL> أو الهاتف 0506747770 للحصول على كود الطالب.', 'الدعم', 4, TRUE, NOW(), NOW()),
('faq-005', 'هل يمكنني تحميل الفيديوهات؟', 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط لحماية حقوق الملكية الفكرية.', 'المشاهدة', 5, TRUE, NOW(), NOW()),
('faq-006', 'كيف يمكنني تتبع تقدمي في الكورس؟', 'يمكنك رؤية تقدمك في لوحة التحكم الخاصة بك. ستجد نسبة الإنجاز لكل كورس مسجل به.', 'التقدم', 6, TRUE, NOW(), NOW()),
('faq-007', 'هل هناك مدة محددة لإنهاء الكورس؟', 'لا، يمكنك إنهاء الكورس في الوقت الذي يناسبك. الوصول للكورس مفتوح بدون قيود زمنية.', 'المشاهدة', 7, TRUE, NOW(), NOW()),
('faq-008', 'كيف يمكنني التواصل مع المدرب؟', 'يمكنك استخدام نظام الدردشة المدمج في المنصة أو التواصل مباشرة عبر بيانات الاتصال المتوفرة.', 'الدعم', 8, TRUE, NOW(), NOW());

-- إنشاء كورسات شاملة
INSERT INTO courses (id, title, description, instructor, duration, level, is_active, total_videos, created_at, updated_at) VALUES
('course-001', 'مقدمة في التسويق الرقمي', 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين. يغطي هذا الكورس جميع جوانب التسويق الرقمي من الأساسيات إلى الاستراتيجيات المتقدمة.', 'علاء عبد الحميد', '4 ساعات', 'مبتدئ', TRUE, 4, NOW(), NOW()),
('course-002', 'إدارة وسائل التواصل الاجتماعي', 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية وإنشاء محتوى جذاب يحقق أهدافك التسويقية.', 'علاء عبد الحميد', '6 ساعات', 'متوسط', TRUE, 5, NOW(), NOW()),
('course-003', 'التجارة الإلكترونية للمبتدئين', 'دليل شامل لبدء متجرك الإلكتروني من الصفر. تعلم كيفية اختيار المنتجات وإنشاء المتجر وتسويقه بفعالية.', 'علاء عبد الحميد', '8 ساعات', 'مبتدئ', TRUE, 6, NOW(), NOW()),
('course-004', 'تحليل البيانات التسويقية', 'تعلم كيفية جمع وتحليل البيانات التسويقية لاتخاذ قرارات مدروسة وتحسين أداء حملاتك التسويقية.', 'علاء عبد الحميد', '5 ساعات', 'متقدم', TRUE, 4, NOW(), NOW());

-- إدراج فيديوهات الكورسات
INSERT INTO course_videos (id, course_id, title, description, duration_minutes, video_order, is_free, created_at, updated_at) VALUES
-- كورس التسويق الرقمي
('video-001', 'course-001', 'مقدمة في التسويق الرقمي', 'نظرة عامة على التسويق الرقمي وأهميته في العصر الحديث', 15, 1, TRUE, NOW(), NOW()),
('video-002', 'course-001', 'استراتيجيات التسويق الرقمي', 'تعلم أهم استراتيجيات التسويق الرقمي وكيفية تطبيقها', 25, 2, FALSE, NOW(), NOW()),
('video-003', 'course-001', 'أدوات التسويق الرقمي', 'استعراض أهم الأدوات المستخدمة في التسويق الرقمي', 20, 3, FALSE, NOW(), NOW()),
('video-004', 'course-001', 'قياس النتائج والتحليل', 'كيفية قياس نجاح حملاتك التسويقية وتحليل البيانات', 18, 4, FALSE, NOW(), NOW()),

-- كورس وسائل التواصل الاجتماعي
('video-005', 'course-002', 'مقدمة في وسائل التواصل', 'أساسيات إدارة وسائل التواصل الاجتماعي للأعمال', 12, 1, TRUE, NOW(), NOW()),
('video-006', 'course-002', 'استراتيجية المحتوى', 'كيفية إنشاء استراتيجية محتوى فعالة ومؤثرة', 22, 2, FALSE, NOW(), NOW()),
('video-007', 'course-002', 'التفاعل مع الجمهور', 'طرق التفاعل الفعال مع المتابعين وبناء مجتمع قوي', 18, 3, FALSE, NOW(), NOW()),
('video-008', 'course-002', 'الإعلانات المدفوعة', 'كيفية إنشاء وإدارة الإعلانات المدفوعة على وسائل التواصل', 24, 4, FALSE, NOW(), NOW()),
('video-009', 'course-002', 'تحليل الأداء', 'قياس وتحليل أداء حساباتك على وسائل التواصل', 16, 5, FALSE, NOW(), NOW()),

-- كورس التجارة الإلكترونية
('video-010', 'course-003', 'مقدمة في التجارة الإلكترونية', 'أساسيات التجارة الإلكترونية والفرص المتاحة', 10, 1, TRUE, NOW(), NOW()),
('video-011', 'course-003', 'اختيار المنتجات المربحة', 'كيفية البحث عن المنتجات المربحة وتحليل السوق', 20, 2, FALSE, NOW(), NOW()),
('video-012', 'course-003', 'إنشاء المتجر الإلكتروني', 'خطوات إنشاء متجر إلكتروني احترافي', 28, 3, FALSE, NOW(), NOW()),
('video-013', 'course-003', 'استراتيجيات التسويق للمتجر', 'طرق تسويق المتجر الإلكتروني وجذب العملاء', 25, 4, FALSE, NOW(), NOW()),
('video-014', 'course-003', 'خدمة العملاء والشحن', 'أفضل ممارسات خدمة العملاء وإدارة الشحن', 18, 5, FALSE, NOW(), NOW()),
('video-015', 'course-003', 'تحليل المبيعات والأرباح', 'كيفية تتبع وتحليل مبيعاتك وحساب الأرباح', 15, 6, FALSE, NOW(), NOW()),

-- كورس تحليل البيانات
('video-016', 'course-004', 'مقدمة في تحليل البيانات', 'أهمية تحليل البيانات في التسويق الرقمي', 14, 1, TRUE, NOW(), NOW()),
('video-017', 'course-004', 'أدوات جمع البيانات', 'استعراض أهم أدوات جمع وتتبع البيانات', 22, 2, FALSE, NOW(), NOW()),
('video-018', 'course-004', 'تحليل سلوك العملاء', 'كيفية فهم وتحليل سلوك العملاء من خلال البيانات', 26, 3, FALSE, NOW(), NOW()),
('video-019', 'course-004', 'إنشاء التقارير والرؤى', 'كيفية إنشاء تقارير مفيدة واستخراج الرؤى القيمة', 20, 4, FALSE, NOW(), NOW());

-- تسجيل بعض الطلاب في الكورسات
INSERT INTO enrollments (id, student_id, course_id, enrolled_at, progress_percentage, last_accessed) VALUES
('enroll-001', 'student-001', 'course-001', NOW(), 75.0, NOW()),
('enroll-002', 'student-002', 'course-001', NOW(), 50.0, NOW()),
('enroll-003', 'student-003', 'course-002', NOW(), 25.0, NOW()),
('enroll-004', 'student-001', 'course-002', NOW(), 100.0, NOW()),
('enroll-005', 'student-004', 'course-003', NOW(), 60.0, NOW()),
('enroll-006', 'student-005', 'course-001', NOW(), 90.0, NOW()),
('enroll-007', 'student-006', 'course-004', NOW(), 40.0, NOW()),
('enroll-008', 'student-002', 'course-003', NOW(), 80.0, NOW());

-- إنشاء شهادات للطلاب المكملين
INSERT INTO certificates (id, student_id, course_id, certificate_number, issued_at, is_valid) VALUES
('cert-001', 'student-001', 'course-002', 'CERT-2024-001', NOW(), TRUE),
('cert-002', 'student-005', 'course-001', 'CERT-2024-002', NOW(), TRUE);

-- تسجيل بعض الأنشطة
INSERT INTO activity_logs (id, user_id, action, description, created_at) VALUES
('log-001', 'admin-001', 'admin_login', 'تسجيل دخول المدير', NOW()),
('log-002', 'student-001', 'student_login', 'تسجيل دخول الطالب أحمد محمد علي', NOW()),
('log-003', 'student-001', 'course_enrollment', 'تسجيل في كورس التسويق الرقمي', NOW()),
('log-004', 'student-001', 'course_completion', 'إتمام كورس إدارة وسائل التواصل', NOW()),
('log-005', 'student-002', 'student_login', 'تسجيل دخول الطالبة فاطمة أحمد', NOW());
