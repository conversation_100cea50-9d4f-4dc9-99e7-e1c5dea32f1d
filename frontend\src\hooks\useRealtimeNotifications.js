import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

/**
 * Hook لإدارة الإشعارات الفورية للتحديثات
 */
export const useRealtimeNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [lastActivity, setLastActivity] = useState(null);

  // إضافة إشعار جديد
  const addNotification = (notification) => {
    const newNotification = {
      id: Date.now(),
      timestamp: new Date(),
      read: false,
      ...notification
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 49)]); // الاحتفاظ بآخر 50 إشعار
    setLastActivity(new Date());

    // عرض toast للإشعارات المهمة
    if (notification.priority === 'high') {
      toast.success(notification.message, {
        duration: 5000,
        icon: notification.icon || '🔔'
      });
    }
  };

  // تمييز إشعار كمقروء
  const markAsRead = (notificationId) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // تمييز جميع الإشعارات كمقروءة
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // حذف إشعار
  const removeNotification = (notificationId) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  // مسح جميع الإشعارات
  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // الحصول على عدد الإشعارات غير المقروءة
  const unreadCount = notifications.filter(n => !n.read).length;

  // إشعارات خاصة بالأنشطة المختلفة
  const notifyStudentRegistration = (studentName, studentCode) => {
    addNotification({
      type: 'student_registration',
      title: 'طالب جديد',
      message: `تم تسجيل الطالب ${studentName} بكود ${studentCode}`,
      priority: 'medium',
      icon: '👨‍🎓',
      data: { studentName, studentCode }
    });
  };

  const notifyCourseCreation = (courseTitle) => {
    addNotification({
      type: 'course_creation',
      title: 'كورس جديد',
      message: `تم إنشاء كورس جديد: ${courseTitle}`,
      priority: 'medium',
      icon: '📚',
      data: { courseTitle }
    });
  };

  const notifyVideoUpload = (courseTitle, videoTitle) => {
    addNotification({
      type: 'video_upload',
      title: 'فيديو جديد',
      message: `تم إضافة فيديو "${videoTitle}" إلى كورس ${courseTitle}`,
      priority: 'low',
      icon: '🎥',
      data: { courseTitle, videoTitle }
    });
  };

  const notifyStudentEnrollment = (studentName, courseTitle) => {
    addNotification({
      type: 'student_enrollment',
      title: 'تسجيل في كورس',
      message: `تم تسجيل ${studentName} في كورس ${courseTitle}`,
      priority: 'medium',
      icon: '✅',
      data: { studentName, courseTitle }
    });
  };

  const notifySystemUpdate = (message) => {
    addNotification({
      type: 'system_update',
      title: 'تحديث النظام',
      message,
      priority: 'high',
      icon: '🔄',
      data: {}
    });
  };

  const notifyError = (errorMessage) => {
    addNotification({
      type: 'error',
      title: 'خطأ في النظام',
      message: errorMessage,
      priority: 'high',
      icon: '⚠️',
      data: { errorMessage }
    });
  };

  // تنظيف الإشعارات القديمة (أكثر من 7 أيام)
  useEffect(() => {
    const cleanupOldNotifications = () => {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      setNotifications(prev =>
        prev.filter(notification => notification.timestamp > sevenDaysAgo)
      );
    };

    // تنظيف كل ساعة
    const interval = setInterval(cleanupOldNotifications, 60 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  return {
    notifications,
    unreadCount,
    lastActivity,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    // دوال الإشعارات المخصصة
    notifyStudentRegistration,
    notifyCourseCreation,
    notifyVideoUpload,
    notifyStudentEnrollment,
    notifySystemUpdate,
    notifyError
  };
};

/**
 * Hook لمراقبة التغييرات في البيانات وإرسال إشعارات
 */
export const useDataChangeNotifications = (data, dataType) => {
  const { addNotification } = useRealtimeNotifications();
  const [previousData, setPreviousData] = useState(null);

  useEffect(() => {
    if (previousData && data) {
      // مقارنة البيانات الجديدة مع السابقة
      if (Array.isArray(data) && Array.isArray(previousData)) {
        // التحقق من إضافة عناصر جديدة
        const newItems = data.filter(item => 
          !previousData.some(prevItem => prevItem.id === item.id)
        );

        newItems.forEach(item => {
          if (dataType === 'students') {
            addNotification({
              type: 'data_change',
              title: 'طالب جديد',
              message: `تم إضافة الطالب ${item.name}`,
              priority: 'medium',
              icon: '👨‍🎓'
            });
          } else if (dataType === 'courses') {
            addNotification({
              type: 'data_change',
              title: 'كورس جديد',
              message: `تم إضافة الكورس ${item.title}`,
              priority: 'medium',
              icon: '📚'
            });
          }
        });

        // التحقق من تحديث العناصر الموجودة
        data.forEach(item => {
          const prevItem = previousData.find(prev => prev.id === item.id);
          if (prevItem && JSON.stringify(item) !== JSON.stringify(prevItem)) {
            addNotification({
              type: 'data_update',
              title: 'تحديث البيانات',
              message: `تم تحديث ${dataType === 'students' ? 'بيانات الطالب' : 'بيانات الكورس'} ${item.name || item.title}`,
              priority: 'low',
              icon: '🔄'
            });
          }
        });
      }
    }

    setPreviousData(data);
  }, [data, dataType, addNotification, previousData]);
};

export default useRealtimeNotifications;
