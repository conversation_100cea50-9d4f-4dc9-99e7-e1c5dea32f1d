/**
 * أداة تنظيف قاعدة البيانات الشاملة
 * Database Cleaner Tool - Removes all mock/test data from Firebase and Supabase
 */

import { db } from '../firebase/config';
import { supabase } from '../supabase/config';
import {
  collection,
  getDocs,
  deleteDoc,
  doc,
  writeBatch,
  query,
  where
} from 'firebase/firestore';
import toast from 'react-hot-toast';

/**
 * أداة تنظيف قاعدة البيانات
 */
export class DatabaseCleaner {
  constructor() {
    this.deletedCounts = {
      firebase: {
        users: 0,
        courses: 0,
        enrollments: 0,
        faqs: 0,
        interactions: 0,
        notifications: 0
      },
      supabase: {
        users: 0,
        courses: 0,
        enrollments: 0,
        faqs: 0
      }
    };
  }

  /**
   * تنظيف شامل لجميع البيانات
   */
  async cleanAllData() {
    try {
      console.log('🧹 بدء تنظيف قاعدة البيانات الشامل...');
      toast.loading('جاري تنظيف قاعدة البيانات...', { id: 'cleaning' });

      // تنظيف Firebase
      await this.cleanFirebaseData();
      
      // تنظيف Supabase
      await this.cleanSupabaseData();

      // عرض النتائج
      this.showCleaningResults();
      
      console.log('✅ تم تنظيف قاعدة البيانات بنجاح');
      toast.success('تم تنظيف قاعدة البيانات بنجاح', { id: 'cleaning' });
      
      return {
        success: true,
        deletedCounts: this.deletedCounts
      };
    } catch (error) {
      console.error('❌ خطأ في تنظيف قاعدة البيانات:', error);
      toast.error('فشل في تنظيف قاعدة البيانات', { id: 'cleaning' });
      throw error;
    }
  }

  /**
   * تنظيف بيانات Firebase
   */
  async cleanFirebaseData() {
    console.log('🔥 تنظيف بيانات Firebase...');

    // تنظيف المستخدمين (الطلاب فقط، الاحتفاظ بالمدراء)
    await this.cleanFirebaseCollection('users', 'role', '==', 'student');
    
    // تنظيف الكورسات
    await this.cleanFirebaseCollection('courses');
    
    // تنظيف التسجيلات
    await this.cleanFirebaseCollection('enrollments');
    
    // تنظيف الأسئلة الشائعة
    await this.cleanFirebaseCollection('faqs');
    
    // تنظيف التفاعلات
    await this.cleanFirebaseCollection('user_interactions');
    await this.cleanFirebaseCollection('critical_interactions');
    
    // تنظيف الإشعارات
    await this.cleanFirebaseCollection('notifications');

    console.log('✅ تم تنظيف بيانات Firebase');
  }

  /**
   * تنظيف مجموعة Firebase
   */
  async cleanFirebaseCollection(collectionName, filterField = null, operator = null, filterValue = null) {
    try {
      console.log(`🗑️ تنظيف مجموعة ${collectionName}...`);
      
      let q;
      if (filterField && operator && filterValue) {
        q = query(collection(db, collectionName), where(filterField, operator, filterValue));
      } else {
        q = collection(db, collectionName);
      }
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log(`ℹ️ مجموعة ${collectionName} فارغة بالفعل`);
        return;
      }

      // استخدام batch للحذف السريع
      const batch = writeBatch(db);
      let batchCount = 0;
      let totalDeleted = 0;

      for (const docSnapshot of snapshot.docs) {
        batch.delete(doc(db, collectionName, docSnapshot.id));
        batchCount++;
        totalDeleted++;

        // تنفيذ batch كل 500 عملية (حد Firestore)
        if (batchCount === 500) {
          await batch.commit();
          console.log(`📦 تم حذف ${batchCount} عنصر من ${collectionName}`);
          batchCount = 0;
        }
      }

      // تنفيذ آخر batch
      if (batchCount > 0) {
        await batch.commit();
        console.log(`📦 تم حذف ${batchCount} عنصر من ${collectionName}`);
      }

      // تحديث العداد
      const collectionKey = this.getFirebaseCollectionKey(collectionName);
      if (collectionKey) {
        this.deletedCounts.firebase[collectionKey] = totalDeleted;
      }

      console.log(`✅ تم حذف ${totalDeleted} عنصر من ${collectionName}`);
    } catch (error) {
      console.error(`❌ خطأ في تنظيف ${collectionName}:`, error);
    }
  }

  /**
   * تنظيف بيانات Supabase
   */
  async cleanSupabaseData() {
    console.log('🐘 تنظيف بيانات Supabase...');

    // تنظيف التسجيلات أولاً (foreign key constraints)
    await this.cleanSupabaseTable('enrollments');
    
    // تنظيف المستخدمين (الطلاب فقط)
    await this.cleanSupabaseTable('users', "role = 'student'");
    
    // تنظيف الكورسات
    await this.cleanSupabaseTable('courses');
    
    // تنظيف الأسئلة الشائعة
    await this.cleanSupabaseTable('faqs');

    console.log('✅ تم تنظيف بيانات Supabase');
  }

  /**
   * تنظيف جدول Supabase
   */
  async cleanSupabaseTable(tableName, condition = null) {
    try {
      console.log(`🗑️ تنظيف جدول ${tableName}...`);
      
      let query = supabase.from(tableName);
      
      if (condition) {
        // للشروط المعقدة، نستخدم select ثم delete
        const { data: itemsToDelete, error: selectError } = await supabase
          .from(tableName)
          .select('id')
          .filter('role', 'eq', 'student');
          
        if (selectError) throw selectError;
        
        if (itemsToDelete && itemsToDelete.length > 0) {
          const ids = itemsToDelete.map(item => item.id);
          const { error: deleteError } = await supabase
            .from(tableName)
            .delete()
            .in('id', ids);
            
          if (deleteError) throw deleteError;
          
          this.deletedCounts.supabase[tableName] = itemsToDelete.length;
          console.log(`✅ تم حذف ${itemsToDelete.length} عنصر من ${tableName}`);
        } else {
          console.log(`ℹ️ جدول ${tableName} فارغ بالفعل`);
        }
      } else {
        // حذف جميع البيانات
        const { data: allItems, error: selectError } = await supabase
          .from(tableName)
          .select('id');
          
        if (selectError) throw selectError;
        
        if (allItems && allItems.length > 0) {
          const { error: deleteError } = await supabase
            .from(tableName)
            .delete()
            .neq('id', 0); // حذف جميع الصفوف
            
          if (deleteError) throw deleteError;
          
          this.deletedCounts.supabase[tableName] = allItems.length;
          console.log(`✅ تم حذف ${allItems.length} عنصر من ${tableName}`);
        } else {
          console.log(`ℹ️ جدول ${tableName} فارغ بالفعل`);
        }
      }
    } catch (error) {
      console.error(`❌ خطأ في تنظيف ${tableName}:`, error);
    }
  }

  /**
   * الحصول على مفتاح المجموعة في Firebase
   */
  getFirebaseCollectionKey(collectionName) {
    const mapping = {
      'users': 'users',
      'courses': 'courses',
      'enrollments': 'enrollments',
      'faqs': 'faqs',
      'user_interactions': 'interactions',
      'critical_interactions': 'interactions',
      'notifications': 'notifications'
    };
    return mapping[collectionName];
  }

  /**
   * عرض نتائج التنظيف
   */
  showCleaningResults() {
    console.log('\n📊 نتائج تنظيف قاعدة البيانات:');
    console.log('🔥 Firebase:');
    Object.entries(this.deletedCounts.firebase).forEach(([key, count]) => {
      if (count > 0) {
        console.log(`   ${key}: ${count} عنصر محذوف`);
      }
    });
    
    console.log('🐘 Supabase:');
    Object.entries(this.deletedCounts.supabase).forEach(([key, count]) => {
      if (count > 0) {
        console.log(`   ${key}: ${count} عنصر محذوف`);
      }
    });
  }

  /**
   * تنظيف انتقائي للكورسات فقط
   */
  async cleanCoursesOnly() {
    try {
      console.log('📚 تنظيف الكورسات فقط...');
      
      // تنظيف التسجيلات أولاً
      await this.cleanFirebaseCollection('enrollments');
      await this.cleanSupabaseTable('enrollments');
      
      // ثم تنظيف الكورسات
      await this.cleanFirebaseCollection('courses');
      await this.cleanSupabaseTable('courses');
      
      console.log('✅ تم تنظيف الكورسات بنجاح');
      toast.success('تم تنظيف الكورسات بنجاح');
      
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تنظيف الكورسات:', error);
      toast.error('فشل في تنظيف الكورسات');
      throw error;
    }
  }

  /**
   * تنظيف انتقائي للطلاب فقط
   */
  async cleanStudentsOnly() {
    try {
      console.log('👨‍🎓 تنظيف الطلاب فقط...');
      
      // تنظيف التسجيلات أولاً
      await this.cleanFirebaseCollection('enrollments');
      await this.cleanSupabaseTable('enrollments');
      
      // ثم تنظيف الطلاب
      await this.cleanFirebaseCollection('users', 'role', '==', 'student');
      await this.cleanSupabaseTable('users', "role = 'student'");
      
      console.log('✅ تم تنظيف الطلاب بنجاح');
      toast.success('تم تنظيف الطلاب بنجاح');
      
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تنظيف الطلاب:', error);
      toast.error('فشل في تنظيف الطلاب');
      throw error;
    }
  }
}

// إنشاء instance واحد للاستخدام
export const databaseCleaner = new DatabaseCleaner();

// تصدير الكلاس والinstance
export default databaseCleaner;
