#!/bin/bash

echo "🚀 نشر منصة كورسات علاء عبد الحميد على Firebase"
echo "=================================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "📦 تثبيت Firebase CLI..."
    npm install -g firebase-tools
fi

# Build frontend
echo "🔨 بناء الواجهة الأمامية..."
cd frontend
npm install
npm run build
cd ..

# Install functions dependencies
echo "📦 تثبيت dependencies للـ Functions..."
cd functions
npm install
cd ..

# Login to Firebase (if not already logged in)
echo "🔐 تسجيل الدخول في Firebase..."
firebase login

# Initialize project (if not already initialized)
if [ ! -f ".firebaserc" ]; then
    echo "🔧 ربط المشروع بـ Firebase..."
    firebase use --add
fi

# Deploy to Firebase
echo "🚀 نشر المشروع..."
firebase deploy

echo ""
echo "✅ تم النشر بنجاح!"
echo ""
echo "🔗 روابط المشروع:"
echo "   📱 الموقع: https://$(firebase use | grep 'default' | cut -d' ' -f3).web.app"
echo "   🔍 API Health: https://$(firebase use | grep 'default' | cut -d' ' -f3).web.app/api/health"
echo ""
echo "👨‍💼 بيانات المدير:"
echo "   البريد: <EMAIL>"
echo "   كلمة المرور: Admin123!"
echo ""
echo "👨‍🎓 أكواد الطلاب:"
echo "   123456, 789012"
echo ""
echo "🎉 المشروع منشور ويعمل!"
