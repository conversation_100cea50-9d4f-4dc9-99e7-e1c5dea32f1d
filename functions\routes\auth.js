const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const admin = require('firebase-admin');

const router = express.Router();

// إنشاء توكن JWT
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET || 'alaa_courses_secret_key', { expiresIn: '7d' });
};

// تسجيل دخول المدير
router.post('/admin/login', [
  body('email').isEmail().withMessage('البريد الإلكتروني غير صالح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;
    const db = req.db;

    // البحث عن المدير
    const adminQuery = await db.collection('users')
      .where('email', '==', email)
      .where('role', '==', 'admin')
      .limit(1)
      .get();

    if (adminQuery.empty) {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    const adminDoc = adminQuery.docs[0];
    const admin = { id: adminDoc.id, ...adminDoc.data() };

    // التحقق من كلمة المرور
    const isMatch = await bcrypt.compare(password, admin.password);
    if (!isMatch) {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    // التحقق من أن الحساب مفعل
    if (!admin.isActive) {
      return res.status(401).json({ message: 'الحساب غير مفعل' });
    }

    // تحديث آخر تسجيل دخول
    await db.collection('users').doc(admin.id).update({
      lastLogin: admin.firestore.FieldValue.serverTimestamp()
    });

    // إنشاء التوكن
    const token = generateToken(admin.id);

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: admin.id,
        name: admin.name,
        email: admin.email,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول المدير:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل دخول الطالب بالكود
router.post('/student/login', [
  body('code').isLength({ min: 6, max: 6 }).withMessage('الكود يجب أن يكون 6 أرقام')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { code } = req.body;
    const db = req.db;

    // البحث عن الطالب بالكود
    const studentQuery = await db.collection('users')
      .where('studentCode', '==', code)
      .where('role', '==', 'student')
      .limit(1)
      .get();

    if (studentQuery.empty) {
      return res.status(401).json({ message: 'كود غير صحيح' });
    }

    const studentDoc = studentQuery.docs[0];
    const student = { id: studentDoc.id, ...studentDoc.data() };

    // التحقق من أن الحساب مفعل
    if (!student.isActive) {
      return res.status(401).json({ message: 'الحساب غير مفعل' });
    }

    // تحديث آخر تسجيل دخول
    await db.collection('users').doc(student.id).update({
      lastLogin: admin.firestore.FieldValue.serverTimestamp()
    });

    // إنشاء التوكن
    const token = generateToken(student.id);

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: student.id,
        name: student.name,
        role: student.role,
        studentCode: student.studentCode
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// Middleware للتحقق من التوكن
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد توكن، الوصول مرفوض' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'alaa_courses_secret_key');
    const userDoc = await req.db.collection('users').doc(decoded.id).get();
    
    if (!userDoc.exists) {
      return res.status(401).json({ message: 'التوكن غير صالح' });
    }

    req.user = { id: userDoc.id, ...userDoc.data() };
    next();
  } catch (error) {
    res.status(401).json({ message: 'التوكن غير صالح' });
  }
};

// الحصول على بيانات المستخدم الحالي
router.get('/me', auth, async (req, res) => {
  try {
    const user = req.user;

    res.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        studentCode: user.studentCode,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('خطأ في الحصول على بيانات المستخدم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل الخروج
router.post('/logout', auth, async (req, res) => {
  try {
    res.json({ message: 'تم تسجيل الخروج بنجاح' });
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
