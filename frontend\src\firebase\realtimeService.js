import { db } from './config';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  onSnapshot, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  getDocs,
  writeBatch
} from 'firebase/firestore';

/**
 * خدمة التحديثات الفورية لـ Firebase
 * تضمن ظهور التغييرات فوراً لجميع المستخدمين
 */
class RealtimeService {
  constructor() {
    this.listeners = new Map();
  }

  /**
   * إضافة كورس جديد مع تحديث فوري
   */
  async addCourse(courseData) {
    try {
      console.log('📚 إضافة كورس جديد:', courseData.title);
      
      const courseRef = await addDoc(collection(db, 'courses'), {
        ...courseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        isActive: true,
        studentsCount: 0
      });

      // إضافة إشعار للطلاب
      await this.notifyStudents('new_course', {
        courseId: courseRef.id,
        courseTitle: courseData.title,
        message: `تم إضافة كورس جديد: ${courseData.title}`
      });

      console.log('✅ تم إضافة الكورس بنجاح:', courseRef.id);
      return { success: true, id: courseRef.id };
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      throw error;
    }
  }

  /**
   * تسجيل طالب في كورس مع تحديث فوري
   */
  async enrollStudentInCourse(studentId, courseId, enrollmentData = {}) {
    try {
      console.log('📝 تسجيل طالب في كورس:', { studentId, courseId });
      
      const batch = writeBatch(db);
      
      // إضافة التسجيل
      const enrollmentRef = doc(collection(db, 'enrollments'));
      batch.set(enrollmentRef, {
        studentId,
        courseId,
        enrolledAt: serverTimestamp(),
        status: 'active',
        progress: 0,
        completedLessons: [],
        lastAccessedAt: serverTimestamp(),
        ...enrollmentData
      });

      // تحديث عدد الطلاب في الكورس
      const courseRef = doc(db, 'courses', courseId);
      batch.update(courseRef, {
        studentsCount: (await this.getCourseStudentsCount(courseId)) + 1,
        updatedAt: serverTimestamp()
      });

      // إضافة إشعار للطالب
      const notificationRef = doc(collection(db, 'notifications'));
      batch.set(notificationRef, {
        userId: studentId,
        type: 'course_enrollment',
        title: 'تم تسجيلك في كورس جديد',
        message: 'تم تسجيلك بنجاح في الكورس',
        data: { courseId, enrollmentId: enrollmentRef.id },
        isRead: false,
        createdAt: serverTimestamp()
      });

      await batch.commit();
      
      console.log('✅ تم تسجيل الطالب بنجاح');
      return { success: true, enrollmentId: enrollmentRef.id };
    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      throw error;
    }
  }

  /**
   * إضافة سؤال شائع مع تحديث فوري
   */
  async addFAQ(faqData) {
    try {
      console.log('❓ إضافة سؤال شائع جديد:', faqData.question);
      
      const faqRef = await addDoc(collection(db, 'faqs'), {
        ...faqData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        isActive: true,
        views: 0,
        helpful: 0
      });

      // إضافة إشعار للطلاب
      await this.notifyStudents('new_faq', {
        faqId: faqRef.id,
        question: faqData.question,
        message: `تم إضافة سؤال شائع جديد`
      });

      console.log('✅ تم إضافة السؤال الشائع بنجاح:', faqRef.id);
      return { success: true, id: faqRef.id };
    } catch (error) {
      console.error('❌ خطأ في إضافة السؤال الشائع:', error);
      throw error;
    }
  }

  /**
   * مراقبة الكورسات للطالب مع تحديثات فورية
   */
  watchStudentCourses(studentId, callback) {
    const listenerId = `student_courses_${studentId}`;

    // إيقاف المراقبة السابقة إن وجدت
    if (this.listeners.has(listenerId)) {
      this.listeners.get(listenerId)();
    }

    console.log('🔄 بدء مراقبة كورسات الطالب:', studentId);

    // مراقبة تسجيلات الطالب (بدون فلتر status لتجنب مشاكل الفهارس)
    const enrollmentsQuery = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId)
    );

    const unsubscribe = onSnapshot(enrollmentsQuery, async (enrollmentsSnapshot) => {
      try {
        const enrollments = enrollmentsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
          lastAccessed: doc.data().lastAccessed?.toDate?.() || new Date()
        }))
        .filter(enrollment => !enrollment.isDeleted) // فلترة محلية بدلاً من في الاستعلام
        .sort((a, b) => b.enrolledAt - a.enrolledAt); // ترتيب محلي

        // جلب تفاصيل الكورسات
        const courseIds = enrollments.map(e => e.courseId);
        const courses = [];

        if (courseIds.length > 0) {
          // تقسيم courseIds إلى مجموعات من 10 (حد Firebase)
          const chunks = [];
          for (let i = 0; i < courseIds.length; i += 10) {
            chunks.push(courseIds.slice(i, i + 10));
          }

          for (const chunk of chunks) {
            const coursesQuery = query(
              collection(db, 'courses'),
              where('__name__', 'in', chunk)
            );

            const coursesSnapshot = await getDocs(coursesQuery);
            coursesSnapshot.docs.forEach(doc => {
              const courseData = doc.data();
              const enrollment = enrollments.find(e => e.courseId === doc.id);

              // التأكد من أن الكورس نشط
              if (courseData.isActive !== false) {
                courses.push({
                  id: doc.id,
                  ...courseData,
                  enrollment: enrollment,
                  progress: enrollment?.progress || 0,
                  enrolledAt: enrollment?.enrolledAt,
                  lastAccessedAt: enrollment?.lastAccessed,
                  createdAt: courseData.createdAt?.toDate?.() || new Date(),
                  updatedAt: courseData.updatedAt?.toDate?.() || new Date()
                });
              }
            });
          }
        }

        // ترتيب الكورسات حسب تاريخ التسجيل
        courses.sort((a, b) => b.enrolledAt - a.enrolledAt);

        console.log(`📚 تحديث كورسات الطالب ${studentId}:`, courses.length);

        // استدعاء callback مع التحقق من صحته
        if (typeof callback === 'function') {
          callback(courses);
        }
      } catch (error) {
        console.error('❌ خطأ في مراقبة كورسات الطالب:', error);
        // استدعاء callback مع مصفوفة فارغة في حالة الخطأ
        if (typeof callback === 'function') {
          callback([]);
        }
      }
    }, (error) => {
      console.error('❌ خطأ في الاستماع لتغييرات التسجيلات:', error);
      if (typeof callback === 'function') {
        callback([]);
      }
    });

    this.listeners.set(listenerId, unsubscribe);
    return unsubscribe;
  }

  /**
   * مراقبة الأسئلة الشائعة مع تحديثات فورية
   */
  watchFAQs(callback) {
    const listenerId = 'faqs_watch';
    
    // إيقاف المراقبة السابقة إن وجدت
    if (this.listeners.has(listenerId)) {
      this.listeners.get(listenerId)();
    }

    const faqsQuery = query(
      collection(db, 'faqs'),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(faqsQuery, (snapshot) => {
      const faqs = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log('❓ تحديث الأسئلة الشائعة:', faqs.length);
      callback(faqs);
    });

    this.listeners.set(listenerId, unsubscribe);
    return unsubscribe;
  }

  /**
   * مراقبة الإشعارات للمستخدم
   */
  watchUserNotifications(userId, callback) {
    const listenerId = `notifications_${userId}`;
    
    // إيقاف المراقبة السابقة إن وجدت
    if (this.listeners.has(listenerId)) {
      this.listeners.get(listenerId)();
    }

    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log(`🔔 تحديث إشعارات المستخدم ${userId}:`, notifications.length);
      callback(notifications);
    });

    this.listeners.set(listenerId, unsubscribe);
    return unsubscribe;
  }

  /**
   * إرسال إشعار لجميع الطلاب
   */
  async notifyStudents(type, data) {
    try {
      // جلب جميع الطلاب النشطين
      const studentsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'student'),
        where('isActive', '==', true)
      );

      const studentsSnapshot = await getDocs(studentsQuery);
      const batch = writeBatch(db);

      studentsSnapshot.docs.forEach(studentDoc => {
        const notificationRef = doc(collection(db, 'notifications'));
        batch.set(notificationRef, {
          userId: studentDoc.id,
          type: type,
          title: this.getNotificationTitle(type),
          message: data.message,
          data: data,
          isRead: false,
          createdAt: serverTimestamp()
        });
      });

      await batch.commit();
      console.log(`🔔 تم إرسال إشعار ${type} لجميع الطلاب`);
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعارات:', error);
    }
  }

  /**
   * الحصول على عنوان الإشعار حسب النوع
   */
  getNotificationTitle(type) {
    const titles = {
      'new_course': 'كورس جديد متاح',
      'new_faq': 'سؤال شائع جديد',
      'course_enrollment': 'تم تسجيلك في كورس',
      'course_update': 'تحديث في الكورس',
      'system_announcement': 'إعلان من الإدارة'
    };
    return titles[type] || 'إشعار جديد';
  }

  /**
   * الحصول على عدد الطلاب في الكورس
   */
  async getCourseStudentsCount(courseId) {
    try {
      const enrollmentsQuery = query(
        collection(db, 'enrollments'),
        where('courseId', '==', courseId),
        where('status', '==', 'active')
      );
      
      const snapshot = await getDocs(enrollmentsQuery);
      return snapshot.size;
    } catch (error) {
      console.error('❌ خطأ في حساب عدد الطلاب:', error);
      return 0;
    }
  }

  /**
   * تحديث تقدم الطالب في الكورس
   */
  async updateStudentProgress(enrollmentId, progressData) {
    try {
      const enrollmentRef = doc(db, 'enrollments', enrollmentId);
      await updateDoc(enrollmentRef, {
        ...progressData,
        lastAccessedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      console.log('📈 تم تحديث تقدم الطالب');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تحديث التقدم:', error);
      throw error;
    }
  }

  /**
   * تحديث حالة الكورس
   */
  async updateCourseStatus(courseId, status) {
    try {
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        isActive: status,
        updatedAt: serverTimestamp()
      });

      console.log(`📚 تم تحديث حالة الكورس: ${status}`);
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تحديث حالة الكورس:', error);
      throw error;
    }
  }

  /**
   * إيقاف جميع المراقبات
   */
  stopAllListeners() {
    this.listeners.forEach((unsubscribe, listenerId) => {
      console.log(`🛑 إيقاف مراقبة: ${listenerId}`);
      unsubscribe();
    });
    this.listeners.clear();
  }

  /**
   * إيقاف مراقبة محددة
   */
  stopListener(listenerId) {
    if (this.listeners.has(listenerId)) {
      this.listeners.get(listenerId)();
      this.listeners.delete(listenerId);
      console.log(`🛑 تم إيقاف مراقبة: ${listenerId}`);
    }
  }
}

// إنشاء مثيل عام
const realtimeService = new RealtimeService();

export default realtimeService;
