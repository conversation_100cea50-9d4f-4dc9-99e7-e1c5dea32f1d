<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة صورة المشاركة الاجتماعية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
            background: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .preview-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .social-card {
            width: 600px;
            height: 315px;
            position: relative;
            background: linear-gradient(135deg, #0000FF 0%, #4169E1 50%, #6366F1 100%);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: scale(1);
            transition: transform 0.3s ease;
        }
        
        .social-card:hover {
            transform: scale(1.02);
        }
        
        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(255,215,0,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,215,0,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%);
        }
        
        .logo-section {
            position: absolute;
            top: 30px;
            left: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: url('91ad9430-b4ac-42c6-8268-86e74ea939e1.jpg') center/cover;
            border-radius: 50%;
            border: 3px solid rgba(255, 215, 0, 0.8);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .brand-text {
            color: white;
        }
        
        .brand-name {
            font-size: 24px;
            font-weight: 900;
            margin: 0;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .brand-subtitle {
            font-size: 12px;
            font-weight: 500;
            margin: 2px 0 0 0;
            opacity: 0.9;
        }
        
        .main-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            width: 80%;
        }
        
        .main-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
            line-height: 1.2;
        }
        
        .main-description {
            font-size: 16px;
            font-weight: 400;
            margin-bottom: 20px;
            opacity: 0.95;
            line-height: 1.4;
        }
        
        .features-row {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .feature-badge {
            background: rgba(255, 255, 255, 0.15);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .feature-icon {
            font-size: 14px;
        }
        
        .url-badge {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        .decorative-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 50%;
        }
        
        .shape1 {
            width: 100px;
            height: 100px;
            top: -50px;
            right: -50px;
        }
        
        .shape2 {
            width: 60px;
            height: 60px;
            bottom: -30px;
            left: -30px;
        }
        
        .shape3 {
            width: 40px;
            height: 40px;
            top: 40%;
            left: 10%;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #0000FF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4169E1;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #FFD700;
            color: #0000FF;
        }
        
        .btn-secondary:hover {
            background: #FFA500;
            transform: translateY(-2px);
        }
        
        .info {
            text-align: center;
            color: #666;
            font-size: 14px;
            max-width: 600px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2 style="text-align: center; color: #333; margin-bottom: 20px;">معاينة صورة المشاركة الاجتماعية</h2>
        
        <div class="social-card" id="socialCard">
            <div class="background-pattern"></div>
            <div class="decorative-shapes">
                <div class="shape shape1"></div>
                <div class="shape shape2"></div>
                <div class="shape shape3"></div>
            </div>
            
            <div class="logo-section">
                <div class="logo"></div>
                <div class="brand-text">
                    <h3 class="brand-name">SKILLS WORLD</h3>
                    <p class="brand-subtitle">ACADEMY</p>
                </div>
            </div>
            
            <div class="main-content">
                <h1 class="main-title">منصة التعلم والتطوير المهني العالمية</h1>
                <p class="main-description">تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية والمدربين المحترفين</p>
                
                <div class="features-row">
                    <div class="feature-badge">
                        <span class="feature-icon">📚</span>
                        <span>كورسات متنوعة</span>
                    </div>
                    <div class="feature-badge">
                        <span class="feature-icon">🎓</span>
                        <span>شهادات معتمدة</span>
                    </div>
                    <div class="feature-badge">
                        <span class="feature-icon">👨‍🏫</span>
                        <span>مدربين خبراء</span>
                    </div>
                </div>
            </div>
            
            <div class="url-badge">
                marketwise-academy-qhizq.web.app
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button class="btn btn-primary" onclick="downloadAsImage()">تحميل كصورة PNG</button>
        <button class="btn btn-secondary" onclick="copyToClipboard()">نسخ كصورة</button>
    </div>
    
    <div class="info">
        <p><strong>الأبعاد:</strong> 600x315 بكسل (مناسب للمشاركة على Facebook, LinkedIn, Twitter)</p>
        <p><strong>الاستخدام:</strong> هذه الصورة ستظهر عند مشاركة رابط الموقع على وسائل التواصل الاجتماعي</p>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        async function downloadAsImage() {
            const element = document.getElementById('socialCard');
            
            try {
                const canvas = await html2canvas(element, {
                    width: 1200,
                    height: 630,
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null
                });
                
                const link = document.createElement('a');
                link.download = 'skills-world-academy-social-share.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            } catch (error) {
                console.error('خطأ في إنشاء الصورة:', error);
                alert('حدث خطأ في إنشاء الصورة. يرجى المحاولة مرة أخرى.');
            }
        }
        
        async function copyToClipboard() {
            const element = document.getElementById('socialCard');
            
            try {
                const canvas = await html2canvas(element, {
                    width: 1200,
                    height: 630,
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null
                });
                
                canvas.toBlob(async (blob) => {
                    try {
                        await navigator.clipboard.write([
                            new ClipboardItem({ 'image/png': blob })
                        ]);
                        alert('تم نسخ الصورة إلى الحافظة!');
                    } catch (err) {
                        console.error('خطأ في نسخ الصورة:', err);
                        alert('لا يمكن نسخ الصورة إلى الحافظة. استخدم زر التحميل بدلاً من ذلك.');
                    }
                });
            } catch (error) {
                console.error('خطأ في إنشاء الصورة:', error);
                alert('حدث خطأ في إنشاء الصورة. يرجى المحاولة مرة أخرى.');
            }
        }
    </script>
</body>
</html>
