import { db } from './config';
import { 
  collection, 
  addDoc, 
  serverTimestamp, 
  writeBatch, 
  doc,
  setDoc,
  getDocs,
  query,
  where
} from 'firebase/firestore';

/**
 * تهيئة جداول Firebase المطلوبة للنظام الفوري
 */
class FirebaseCollectionsInitializer {
  constructor() {
    this.collections = [
      'courses',
      'enrollments', 
      'faqs',
      'notifications',
      'user_interactions',
      'critical_interactions',
      'analytics_reports'
    ];
  }

  /**
   * تهيئة جميع الجداول المطلوبة
   */
  async initializeAllCollections() {
    try {
      console.log('🔧 بدء تهيئة جداول Firebase...');
      
      await Promise.all([
        this.initializeCoursesCollection(),
        this.initializeEnrollmentsCollection(),
        this.initializeFAQsCollection(),
        this.initializeNotificationsCollection(),
        this.initializeInteractionsCollections(),
        this.initializeAnalyticsCollection()
      ]);

      console.log('✅ تم تهيئة جميع جداول Firebase بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تهيئة جداول Firebase:', error);
      throw error;
    }
  }

  /**
   * تهيئة جدول الكورسات
   */
  async initializeCoursesCollection() {
    try {
      console.log('📚 تهيئة جدول الكورسات...');
      
      // التحقق من وجود كورسات
      const coursesSnapshot = await getDocs(collection(db, 'courses'));
      
      if (coursesSnapshot.empty) {
        // تم تعطيل إنشاء الكورسات الافتراضية للإنتاج
        console.log('📚 لا توجد كورسات - سيتم إنشاؤها من لوحة التحكم');
        return;
        // إنشاء كورسات افتراضية - DISABLED FOR PRODUCTION
        const defaultCourses = [
          {
            title: 'أساسيات التسويق الرقمي',
            description: 'تعلم أساسيات التسويق الرقمي من الصفر',
            instructor: 'المدير',
            level: 'مبتدئ',
            duration: '4 أسابيع',
            price: 0,
            thumbnail: '/course1.jpg',
            tags: ['تسويق', 'رقمي', 'أساسيات'],
            isPublished: true,
            isActive: true,
            studentsCount: 0,
            modules: [
              {
                id: 'module_1',
                title: 'مقدمة في التسويق الرقمي',
                description: 'فهم أساسيات التسويق الرقمي',
                order: 1,
                lessons: [
                  {
                    id: 'lesson_1',
                    title: 'ما هو التسويق الرقمي؟',
                    type: 'video',
                    duration: 15,
                    order: 1,
                    videoUrl: 'https://example.com/video1.mp4'
                  }
                ]
              }
            ],
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          },
          {
            title: 'إدارة وسائل التواصل الاجتماعي',
            description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية',
            instructor: 'المدير',
            level: 'متوسط',
            duration: '6 أسابيع',
            price: 299,
            thumbnail: '/course2.jpg',
            tags: ['وسائل التواصل', 'إدارة', 'محتوى'],
            isPublished: true,
            isActive: true,
            studentsCount: 0,
            modules: [],
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          }
        ];

        const batch = writeBatch(db);
        defaultCourses.forEach((course, index) => {
          const courseRef = doc(collection(db, 'courses'));
          batch.set(courseRef, course);
        });

        await batch.commit();
        console.log('✅ تم إنشاء الكورسات الافتراضية');
      }
    } catch (error) {
      console.error('❌ خطأ في تهيئة جدول الكورسات:', error);
    }
  }

  /**
   * تهيئة جدول التسجيلات
   */
  async initializeEnrollmentsCollection() {
    try {
      console.log('📝 تهيئة جدول التسجيلات...');
      
      // إنشاء فهرس للتسجيلات
      const enrollmentRef = doc(collection(db, 'enrollments'));
      await setDoc(enrollmentRef, {
        _index: true,
        description: 'جدول تسجيلات الطلاب في الكورسات',
        fields: {
          studentId: 'معرف الطالب',
          courseId: 'معرف الكورس',
          status: 'حالة التسجيل (active, completed, dropped)',
          progress: 'نسبة التقدم (0-100)',
          enrolledAt: 'تاريخ التسجيل',
          completedAt: 'تاريخ الإكمال',
          lastAccessedAt: 'آخر وصول'
        },
        createdAt: serverTimestamp()
      });

      console.log('✅ تم تهيئة جدول التسجيلات');
    } catch (error) {
      console.error('❌ خطأ في تهيئة جدول التسجيلات:', error);
    }
  }

  /**
   * تهيئة جدول الأسئلة الشائعة
   */
  async initializeFAQsCollection() {
    try {
      console.log('❓ تهيئة جدول الأسئلة الشائعة...');
      
      // التحقق من وجود أسئلة شائعة
      const faqsSnapshot = await getDocs(collection(db, 'faqs'));
      
      if (faqsSnapshot.empty) {
        // تم تعطيل إنشاء الأسئلة الشائعة الافتراضية للإنتاج
        console.log('❓ لا توجد أسئلة شائعة - سيتم إنشاؤها من لوحة التحكم');
        return;
        // إنشاء أسئلة شائعة افتراضية - DISABLED FOR PRODUCTION
        const defaultFAQs = [
          {
            question: 'كيف يمكنني التسجيل في الكورسات؟',
            answer: 'يمكنك التسجيل في الكورسات من خلال لوحة التحكم الخاصة بك. اختر الكورس المطلوب واضغط على زر التسجيل.',
            category: 'التسجيل',
            priority: 1,
            isActive: true,
            views: 0,
            helpful: 0,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          },
          {
            question: 'هل يمكنني الوصول للكورسات من الهاتف؟',
            answer: 'نعم، يمكنك الوصول لجميع الكورسات من خلال المتصفح على هاتفك المحمول.',
            category: 'التقنية',
            priority: 2,
            isActive: true,
            views: 0,
            helpful: 0,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          },
          {
            question: 'كيف أحصل على الشهادة؟',
            answer: 'بعد إكمال جميع دروس الكورس بنجاح، ستحصل على شهادة إتمام تلقائياً.',
            category: 'الشهادات',
            priority: 1,
            isActive: true,
            views: 0,
            helpful: 0,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          }
        ];

        const batch = writeBatch(db);
        defaultFAQs.forEach((faq) => {
          const faqRef = doc(collection(db, 'faqs'));
          batch.set(faqRef, faq);
        });

        await batch.commit();
        console.log('✅ تم إنشاء الأسئلة الشائعة الافتراضية');
      }
    } catch (error) {
      console.error('❌ خطأ في تهيئة جدول الأسئلة الشائعة:', error);
    }
  }

  /**
   * تهيئة جدول الإشعارات
   */
  async initializeNotificationsCollection() {
    try {
      console.log('🔔 تهيئة جدول الإشعارات...');
      
      // إنشاء فهرس للإشعارات
      const notificationRef = doc(collection(db, 'notifications'));
      await setDoc(notificationRef, {
        _index: true,
        description: 'جدول إشعارات المستخدمين',
        fields: {
          userId: 'معرف المستخدم',
          type: 'نوع الإشعار',
          title: 'عنوان الإشعار',
          message: 'محتوى الإشعار',
          data: 'بيانات إضافية',
          isRead: 'حالة القراءة',
          priority: 'أولوية الإشعار',
          createdAt: 'تاريخ الإنشاء'
        },
        createdAt: serverTimestamp()
      });

      console.log('✅ تم تهيئة جدول الإشعارات');
    } catch (error) {
      console.error('❌ خطأ في تهيئة جدول الإشعارات:', error);
    }
  }

  /**
   * تهيئة جداول التفاعلات
   */
  async initializeInteractionsCollections() {
    try {
      console.log('🔍 تهيئة جداول التفاعلات...');
      
      // جدول التفاعلات العامة
      const interactionRef = doc(collection(db, 'user_interactions'));
      await setDoc(interactionRef, {
        _index: true,
        description: 'جدول تفاعلات المستخدمين',
        fields: {
          userId: 'معرف المستخدم',
          sessionId: 'معرف الجلسة',
          interactions: 'قائمة التفاعلات',
          batchTimestamp: 'وقت الحفظ'
        },
        createdAt: serverTimestamp()
      });

      // جدول التفاعلات الحرجة
      const criticalRef = doc(collection(db, 'critical_interactions'));
      await setDoc(criticalRef, {
        _index: true,
        description: 'جدول التفاعلات الحرجة',
        fields: {
          userId: 'معرف المستخدم',
          type: 'نوع التفاعل',
          data: 'بيانات التفاعل',
          timestamp: 'وقت التفاعل'
        },
        createdAt: serverTimestamp()
      });

      console.log('✅ تم تهيئة جداول التفاعلات');
    } catch (error) {
      console.error('❌ خطأ في تهيئة جداول التفاعلات:', error);
    }
  }

  /**
   * تهيئة جدول التحليلات
   */
  async initializeAnalyticsCollection() {
    try {
      console.log('📊 تهيئة جدول التحليلات...');
      
      const analyticsRef = doc(collection(db, 'analytics_reports'));
      await setDoc(analyticsRef, {
        _index: true,
        description: 'جدول تقارير التحليلات',
        fields: {
          reportType: 'نوع التقرير',
          data: 'بيانات التقرير',
          metadata: 'معلومات إضافية',
          createdAt: 'تاريخ الإنشاء'
        },
        createdAt: serverTimestamp()
      });

      console.log('✅ تم تهيئة جدول التحليلات');
    } catch (error) {
      console.error('❌ خطأ في تهيئة جدول التحليلات:', error);
    }
  }

  /**
   * التحقق من حالة الجداول
   */
  async checkCollectionsStatus() {
    try {
      const status = {};
      
      for (const collectionName of this.collections) {
        const snapshot = await getDocs(collection(db, collectionName));
        status[collectionName] = {
          exists: !snapshot.empty,
          documentsCount: snapshot.size
        };
      }

      console.log('📋 حالة جداول Firebase:', status);
      return status;
    } catch (error) {
      console.error('❌ خطأ في فحص حالة الجداول:', error);
      return {};
    }
  }
}

// إنشاء مثيل عام
const firebaseInitializer = new FirebaseCollectionsInitializer();

export default firebaseInitializer;
