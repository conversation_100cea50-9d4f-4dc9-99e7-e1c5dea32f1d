@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 إعداد النشر السريع - علاء عبد الحميد
echo ========================================
echo.

echo 📦 تثبيت المكتبات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo.
echo 🎨 بناء الواجهة الأمامية...
cd frontend
call npm install
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الواجهة الأمامية
    pause
    exit /b 1
)
cd ..

echo.
echo 📁 إعداد Git...
git init
git add .
git commit -m "Initial commit - منصة كورسات علاء عبد الحميد"

echo.
echo ✅ الإعداد مكتمل!
echo.
echo 📋 الخطوات التالية:
echo 1. أنشئ repository على GitHub
echo 2. نفذ الأوامر التالية:
echo    git remote add origin https://github.com/username/alaa-courses.git
echo    git push -u origin main
echo 3. اذهب إلى https://render.com وأنشئ Web Service
echo 4. اربط مع GitHub repository
echo 5. أضف متغيرات البيئة
echo.
pause
