/**
 * عارض محتوى الكورس - SKILLS WORLD ACADEMY
 * Course Content Viewer for Students
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  LinearProgress,
  Tabs,
  Tab,
  Alert
} from '@mui/material';
import {
  PlayArrow,
  PictureAsPdf,
  Download,
  Visibility,
  CheckCircle,
  Lock,
  Schedule,
  Description,
  VideoLibrary,
  MenuBook
} from '@mui/icons-material';
import { hybridCourses } from '../services/hybridDatabaseService';
import toast from 'react-hot-toast';

const CourseContentViewer = ({ courseId, isEnrolled = false, onVideoComplete }) => {
  const [tabValue, setTabValue] = useState(0);
  const [videos, setVideos] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [openVideoDialog, setOpenVideoDialog] = useState(false);
  const [completedVideos, setCompletedVideos] = useState(new Set());

  useEffect(() => {
    if (courseId) {
      loadCourseContent();
    }
  }, [courseId]);

  /**
   * تحميل محتوى الكورس
   */
  const loadCourseContent = async () => {
    try {
      setLoading(true);
      
      // تحميل الفيديوهات والمستندات بشكل متوازي
      const [videosData, documentsData] = await Promise.all([
        hybridCourses.getCourseVideos(courseId),
        hybridCourses.getCourseDocuments(courseId)
      ]);

      setVideos(videosData || []);
      setDocuments(documentsData || []);
    } catch (error) {
      console.error('❌ خطأ في تحميل محتوى الكورس:', error);
      toast.error('فشل في تحميل محتوى الكورس');
    } finally {
      setLoading(false);
    }
  };

  /**
   * فتح فيديو للمشاهدة
   */
  const handleVideoClick = (video) => {
    if (!isEnrolled && !video.is_free) {
      toast.error('يجب التسجيل في الكورس لمشاهدة هذا الفيديو');
      return;
    }

    setSelectedVideo(video);
    setOpenVideoDialog(true);
  };

  /**
   * تحميل مستند PDF
   */
  const handleDocumentDownload = async (document) => {
    if (!isEnrolled && !document.is_public) {
      toast.error('يجب التسجيل في الكورس لتحميل هذا المستند');
      return;
    }

    try {
      // فتح الرابط في نافذة جديدة
      window.open(document.pdf_url, '_blank');
      
      // تحديث عداد التحميل (اختياري)
      // await hybridCourses.incrementDocumentDownload(document.id);
    } catch (error) {
      console.error('❌ خطأ في تحميل المستند:', error);
      toast.error('فشل في تحميل المستند');
    }
  };

  /**
   * تحديد إكمال الفيديو
   */
  const handleVideoComplete = (videoId) => {
    setCompletedVideos(prev => new Set([...prev, videoId]));
    if (onVideoComplete) {
      onVideoComplete(videoId);
    }
  };

  /**
   * تنسيق حجم الملف
   */
  const formatFileSize = (bytes) => {
    if (!bytes) return 'غير محدد';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * الحصول على لون الفئة
   */
  const getCategoryColor = (category) => {
    switch (category) {
      case 'material': return 'primary';
      case 'assignment': return 'warning';
      case 'reference': return 'info';
      default: return 'default';
    }
  };

  /**
   * الحصول على تسمية الفئة
   */
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'material': return 'مادة تعليمية';
      case 'assignment': return 'واجب';
      case 'reference': return 'مرجع';
      default: return 'مستند';
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
          جاري تحميل محتوى الكورس...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* تبويبات المحتوى */}
      <Tabs
        value={tabValue}
        onChange={(e, newValue) => setTabValue(newValue)}
        variant="fullWidth"
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          '& .MuiTab-root': {
            minHeight: 48,
            fontSize: '1rem',
            fontWeight: 600
          }
        }}
      >
        <Tab
          icon={<VideoLibrary />}
          label={`الفيديوهات (${videos.length})`}
          iconPosition="start"
        />
        <Tab
          icon={<MenuBook />}
          label={`المستندات (${documents.length})`}
          iconPosition="start"
        />
      </Tabs>

      {/* محتوى التبويبات */}
      <Box sx={{ mt: 2 }}>
        {/* تبويب الفيديوهات */}
        {tabValue === 0 && (
          <Card>
            <CardContent>
              {videos.length === 0 ? (
                <Alert severity="info">
                  لا توجد فيديوهات متاحة في هذا الكورس حالياً
                </Alert>
              ) : (
                <List>
                  {videos.map((video, index) => (
                    <React.Fragment key={video.id}>
                      <ListItem
                        button
                        onClick={() => handleVideoClick(video)}
                        sx={{
                          borderRadius: 1,
                          mb: 1,
                          '&:hover': {
                            bgcolor: 'action.hover'
                          }
                        }}
                      >
                        <ListItemIcon>
                          {completedVideos.has(video.id) ? (
                            <CheckCircle color="success" />
                          ) : !isEnrolled && !video.is_free ? (
                            <Lock color="disabled" />
                          ) : (
                            <PlayArrow color="primary" />
                          )}
                        </ListItemIcon>
                        
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle1" fontWeight={600}>
                                {video.title}
                              </Typography>
                              {video.is_free && (
                                <Chip label="مجاني" size="small" color="success" />
                              )}
                              {!video.is_published && (
                                <Chip label="قريباً" size="small" color="warning" />
                              )}
                            </Box>
                          }
                          secondary={
                            <Box>
                              {video.description && (
                                <Typography variant="body2" color="text.secondary">
                                  {video.description}
                                </Typography>
                              )}
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
                                {video.duration && (
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                    <Schedule fontSize="small" />
                                    <Typography variant="caption">
                                      {video.duration}
                                    </Typography>
                                  </Box>
                                )}
                                <Typography variant="caption" color="text.secondary">
                                  الترتيب: {video.video_order}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                        
                        <ListItemSecondaryAction>
                          <IconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              handleVideoClick(video);
                            }}
                            disabled={!isEnrolled && !video.is_free}
                          >
                            <Visibility />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      
                      {index < videos.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        )}

        {/* تبويب المستندات */}
        {tabValue === 1 && (
          <Card>
            <CardContent>
              {documents.length === 0 ? (
                <Alert severity="info">
                  لا توجد مستندات متاحة في هذا الكورس حالياً
                </Alert>
              ) : (
                <List>
                  {documents.map((document, index) => (
                    <React.Fragment key={document.id}>
                      <ListItem
                        button
                        onClick={() => handleDocumentDownload(document)}
                        sx={{
                          borderRadius: 1,
                          mb: 1,
                          '&:hover': {
                            bgcolor: 'action.hover'
                          }
                        }}
                      >
                        <ListItemIcon>
                          {!isEnrolled && !document.is_public ? (
                            <Lock color="disabled" />
                          ) : (
                            <PictureAsPdf color="error" />
                          )}
                        </ListItemIcon>
                        
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle1" fontWeight={600}>
                                {document.title}
                              </Typography>
                              <Chip 
                                label={getCategoryLabel(document.category)} 
                                size="small" 
                                color={getCategoryColor(document.category)} 
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              {document.description && (
                                <Typography variant="body2" color="text.secondary">
                                  {document.description}
                                </Typography>
                              )}
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
                                <Typography variant="caption" color="text.secondary">
                                  الحجم: {formatFileSize(document.file_size)}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  الترتيب: {document.document_order}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                        
                        <ListItemSecondaryAction>
                          <IconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDocumentDownload(document);
                            }}
                            disabled={!isEnrolled && !document.is_public}
                          >
                            <Download />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      
                      {index < documents.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        )}
      </Box>

      {/* نافذة مشاهدة الفيديو */}
      <Dialog
        open={openVideoDialog}
        onClose={() => setOpenVideoDialog(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { minHeight: '70vh' }
        }}
      >
        <DialogTitle>
          {selectedVideo?.title}
        </DialogTitle>
        <DialogContent>
          {selectedVideo && (
            <Box>
              {selectedVideo.description && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {selectedVideo.description}
                </Typography>
              )}
              
              {/* مشغل الفيديو */}
              <Box
                sx={{
                  width: '100%',
                  height: '400px',
                  bgcolor: 'black',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <video
                  controls
                  width="100%"
                  height="100%"
                  src={selectedVideo.video_url}
                  onEnded={() => handleVideoComplete(selectedVideo.id)}
                  style={{ borderRadius: '4px' }}
                >
                  متصفحك لا يدعم تشغيل الفيديو
                </video>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenVideoDialog(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseContentViewer;
