import { initializeDatabase } from './databaseService';

/**
 * تهيئة التطبيق وقاعدة البيانات
 */
export const initializeApp = async () => {
  try {
    console.log('🚀 بدء تهيئة التطبيق...');

    // تهيئة قاعدة البيانات
    await initializeDatabase();

    console.log('✅ تم تهيئة التطبيق بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة التطبيق:', error);
    throw error;
  }
};

export default initializeApp;
