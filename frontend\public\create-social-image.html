<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء صورة المشاركة الاجتماعية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
        }
        
        .social-card {
            width: 1200px;
            height: 630px;
            position: relative;
            background: linear-gradient(135deg, #0000FF 0%, #4169E1 50%, #6366F1 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            margin: 0 auto;
        }
        
        .background-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('91ad9430-b4ac-42c6-8268-86e74ea939e1.jpg') center/cover;
            opacity: 0.15;
            filter: blur(2px);
        }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 60px;
            color: white;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: url('91ad9430-b4ac-42c6-8268-86e74ea939e1.jpg') center/cover;
            border-radius: 50%;
            border: 6px solid rgba(255, 215, 0, 0.8);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 64px;
            font-weight: 900;
            margin-bottom: 20px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }
        
        .subtitle {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 30px;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
            opacity: 0.95;
        }
        
        .description {
            font-size: 24px;
            font-weight: 400;
            margin-bottom: 40px;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
            opacity: 0.9;
            max-width: 800px;
            line-height: 1.4;
        }
        
        .features {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 30px;
        }
        
        .feature {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 20px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            background: #FFD700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #0000FF;
            font-weight: bold;
        }
        
        .url {
            font-size: 18px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 25px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 215, 0, 0.1);
        }
        
        .circle1 {
            width: 200px;
            height: 200px;
            top: -100px;
            right: -100px;
        }
        
        .circle2 {
            width: 150px;
            height: 150px;
            bottom: -75px;
            left: -75px;
        }
        
        .circle3 {
            width: 100px;
            height: 100px;
            top: 50%;
            left: 50px;
            transform: translateY(-50%);
        }
        
        .download-btn {
            margin-top: 20px;
            padding: 15px 30px;
            background: #FFD700;
            color: #0000FF;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
        }
    </style>
</head>
<body>
    <div class="social-card" id="socialCard">
        <div class="background-overlay"></div>
        <div class="decorative-elements">
            <div class="circle circle1"></div>
            <div class="circle circle2"></div>
            <div class="circle circle3"></div>
        </div>
        <div class="content">
            <div class="logo"></div>
            <h1 class="title">SKILLS WORLD ACADEMY</h1>
            <h2 class="subtitle">منصة التعلم والتطوير المهني العالمية</h2>
            <p class="description">تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية والمدربين المحترفين</p>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📚</div>
                    <span>كورسات متنوعة</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎓</div>
                    <span>شهادات معتمدة</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">👨‍🏫</div>
                    <span>مدربين خبراء</span>
                </div>
            </div>
            <div class="url">marketwise-academy-qhizq.web.app</div>
        </div>
    </div>
    
    <button class="download-btn" onclick="downloadImage()">تحميل صورة المشاركة</button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        function downloadImage() {
            const element = document.getElementById('socialCard');
            
            html2canvas(element, {
                width: 1200,
                height: 630,
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: null
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = 'skills-world-academy-social-share.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        }
        
        // تحميل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                downloadImage();
            }, 2000);
        });
    </script>
</body>
</html>
