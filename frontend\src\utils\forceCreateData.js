import { 
  doc, 
  setDoc, 
  addDoc,
  collection,
  serverTimestamp,
  deleteDoc,
  getDocs,
  query,
  where
} from 'firebase/firestore';
import { db } from '../firebase/config';

// إجبار إنشاء البيانات الحقيقية

export const forceCreateAllData = async () => {
  try {
    console.log('🚀 إجبار إنشاء جميع البيانات الحقيقية...');

    // حذف البيانات القديمة أولاً
    await clearOldData();

    // إنشاء البيانات الجديدة
    await createProgressData();
    await createActivityData();

    console.log('✅ تم إنشاء جميع البيانات الحقيقية بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات:', error);
    throw error;
  }
};

const clearOldData = async () => {
  try {
    console.log('🧹 حذف البيانات القديمة...');

    // حذف تقدم الطلاب القديم
    const progressQuery = query(collection(db, 'userProgress'));
    const progressSnapshot = await getDocs(progressQuery);
    for (const doc of progressSnapshot.docs) {
      await deleteDoc(doc.ref);
    }

    // حذف النشاطات القديمة
    const activityQuery = query(collection(db, 'userActivity'));
    const activitySnapshot = await getDocs(activityQuery);
    for (const doc of activitySnapshot.docs) {
      await deleteDoc(doc.ref);
    }

    console.log('✅ تم حذف البيانات القديمة');
  } catch (error) {
    console.log('⚠️ خطأ في حذف البيانات القديمة:', error.message);
  }
};

const createProgressData = async () => {
  console.log('📊 إنشاء بيانات التقدم...');

  // تقدم الطالب 1 في الدورة 1
  await setDoc(doc(db, 'userProgress', 'student1_course1'), {
    userId: 'student1',
    courseId: 'course1',
    completedVideos: 8,
    totalVideos: 12,
    progress: 67,
    lastWatchedVideo: 'video8',
    totalWatchTime: 240,
    isCompleted: false,
    startedAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  // تقدم الطالب 1 في الدورة 2
  await setDoc(doc(db, 'userProgress', 'student1_course2'), {
    userId: 'student1',
    courseId: 'course2',
    completedVideos: 3,
    totalVideos: 10,
    progress: 30,
    lastWatchedVideo: 'video3',
    totalWatchTime: 90,
    isCompleted: false,
    startedAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  // تقدم الطالب 2 في الدورة 1
  await setDoc(doc(db, 'userProgress', 'student2_course1'), {
    userId: 'student2',
    courseId: 'course1',
    completedVideos: 5,
    totalVideos: 12,
    progress: 42,
    lastWatchedVideo: 'video5',
    totalWatchTime: 150,
    isCompleted: false,
    startedAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  console.log('✅ تم إنشاء بيانات التقدم');
};

const createActivityData = async () => {
  console.log('📝 إنشاء بيانات النشاط...');

  const now = new Date();

  const activities = [
    // نشاطات الطالب 1
    {
      userId: 'student1',
      action: 'video_completed',
      details: {
        description: 'تم إكمال فيديو: مقدمة في التسويق الرقمي',
        courseId: 'course1',
        videoId: 'video8',
        courseTitle: 'أساسيات التسويق الرقمي',
        videoTitle: 'مقدمة في التسويق الرقمي'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      userId: 'student1',
      action: 'video_started',
      details: {
        description: 'بدء مشاهدة: استراتيجيات التسويق المتقدمة',
        courseId: 'course1',
        videoId: 'video9',
        courseTitle: 'أساسيات التسويق الرقمي',
        videoTitle: 'استراتيجيات التسويق المتقدمة'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString()
    },
    {
      userId: 'student1',
      action: 'course_enrolled',
      details: {
        description: 'التسجيل في دورة: إدارة وسائل التواصل الاجتماعي',
        courseId: 'course2',
        courseTitle: 'إدارة وسائل التواصل الاجتماعي'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()
    },
    // نشاطات الطالب 2
    {
      userId: 'student2',
      action: 'login',
      details: {
        description: 'تسجيل الدخول إلى المنصة'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date(now.getTime() - 30 * 60 * 1000).toISOString()
    },
    {
      userId: 'student2',
      action: 'video_completed',
      details: {
        description: 'تم إكمال فيديو: أساسيات وسائل التواصل',
        courseId: 'course1',
        videoId: 'video5',
        courseTitle: 'أساسيات التسويق الرقمي',
        videoTitle: 'أساسيات وسائل التواصل'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date(now.getTime() - 3 * 60 * 60 * 1000).toISOString()
    },
    {
      userId: 'student2',
      action: 'profile_updated',
      details: {
        description: 'تحديث الملف الشخصي'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString()
    }
  ];

  // إضافة النشاطات
  for (const activity of activities) {
    await addDoc(collection(db, 'userActivity'), activity);
  }

  console.log('✅ تم إنشاء بيانات النشاط');
};

export default {
  forceCreateAllData
};
