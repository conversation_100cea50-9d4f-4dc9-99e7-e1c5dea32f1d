import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Divider
} from '@mui/material';
import {
  Close,
  ExpandMore,
  Help
} from '@mui/icons-material';

const FAQDialog = ({ open, onClose }) => {
  const [expanded, setExpanded] = useState(false);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const faqs = [
    {
      id: 'faq1',
      question: 'كيف يمكنني الوصول إلى الدورات؟',
      answer: 'يمكنك الوصول إلى جميع الدورات المسجل بها من خلال صفحة "دوراتي" في لوحة التحكم الخاصة بك. انقر على أي دورة لبدء المشاهدة.'
    },
    {
      id: 'faq2',
      question: 'كيف يمكنني تتبع تقدمي في الدورات؟',
      answer: 'يتم تتبع تقدمك تلقائياً. يمكنك رؤية نسبة الإنجاز لكل دورة في صفحة "دوراتي" والإحصائيات العامة في أعلى الصفحة.'
    },
    {
      id: 'faq3',
      question: 'هل يمكنني تحميل الفيديوهات للمشاهدة دون اتصال؟',
      answer: 'حالياً، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط. نعمل على إضافة ميزة التحميل في التحديثات القادمة.'
    },
    {
      id: 'faq4',
      question: 'كيف يمكنني الحصول على شهادة إتمام الدورة؟',
      answer: 'ستحصل على شهادة إتمام تلقائياً عند إنهاء جميع فيديوهات الدورة بنسبة 100%. ستظهر الشهادة في قسم "شهاداتي".'
    },
    {
      id: 'faq5',
      question: 'ماذا لو واجهت مشكلة تقنية؟',
      answer: 'يمكنك التواصل مع فريق الدعم الفني من خلال زر "تواصل مع المدير" أو إرسال رسالة عبر نموذج الاتصال.'
    },
    {
      id: 'faq6',
      question: 'هل يمكنني تغيير كلمة المرور؟',
      answer: 'نعم، يمكنك تغيير كلمة المرور من خلال الملف الشخصي. انقر على أيقونة الملف الشخصي ثم "تعديل الملف الشخصي".'
    },
    {
      id: 'faq7',
      question: 'كم من الوقت لدي للوصول إلى الدورة؟',
      answer: 'لديك وصول مدى الحياة لجميع الدورات المسجل بها. يمكنك العودة ومراجعة المحتوى في أي وقت.'
    },
    {
      id: 'faq8',
      question: 'هل توجد تطبيق للهاتف المحمول؟',
      answer: 'المنصة متوافقة مع جميع الأجهزة ويمكن الوصول إليها عبر متصفح الهاتف. نعمل على تطوير تطبيق مخصص قريباً.'
    }
  ];

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Help sx={{ color: '#0000FF' }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#0000FF' }}>
            الأسئلة الشائعة
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 2 }}>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
          إليك إجابات على الأسئلة الأكثر شيوعاً. إذا لم تجد إجابة لسؤالك، لا تتردد في التواصل معنا.
        </Typography>

        {faqs.map((faq) => (
          <Accordion
            key={faq.id}
            expanded={expanded === faq.id}
            onChange={handleChange(faq.id)}
            sx={{
              mb: 1,
              '&:before': {
                display: 'none',
              },
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              borderRadius: '8px !important',
              '&.Mui-expanded': {
                margin: '0 0 8px 0',
              }
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMore />}
              sx={{
                backgroundColor: expanded === faq.id ? '#f8f9fa' : 'white',
                borderRadius: '8px',
                '&.Mui-expanded': {
                  borderBottomLeftRadius: 0,
                  borderBottomRightRadius: 0,
                }
              }}
            >
              <Typography sx={{ fontWeight: 'medium', color: '#333' }}>
                {faq.question}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ 
              backgroundColor: '#f8f9fa',
              borderBottomLeftRadius: '8px',
              borderBottomRightRadius: '8px'
            }}>
              <Typography color="textSecondary" sx={{ lineHeight: 1.6 }}>
                {faq.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}

        <Box sx={{ 
          mt: 4, 
          p: 3, 
          backgroundColor: '#e3f2fd', 
          borderRadius: 2,
          textAlign: 'center'
        }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1, color: '#1976d2' }}>
            لم تجد إجابة لسؤالك؟
          </Typography>
          <Typography variant="body2" color="textSecondary">
            تواصل مع فريق الدعم وسنكون سعداء لمساعدتك
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default FAQDialog;
