version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:5.0
    container_name: alaa-courses-db-prod
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: alaa-courses
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - alaa-courses-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API
  backend:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: alaa-courses-backend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb:27017/alaa-courses?authSource=admin
      JWT_SECRET: ${JWT_SECRET}
      PORT: 5000
      FRONTEND_URL: ${FRONTEND_URL}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - alaa-courses-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React App
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL}
    container_name: alaa-courses-frontend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - alaa-courses-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: alaa-courses-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - alaa-courses-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local

networks:
  alaa-courses-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
