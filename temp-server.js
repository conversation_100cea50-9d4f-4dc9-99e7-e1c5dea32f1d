const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 5001;
const JWT_SECRET = 'your-secret-key';

// Middleware
app.use(cors());
app.use(express.json());

// بيانات مؤقتة في الذاكرة
let users = [
  {
    _id: '1',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
    role: 'admin',
    name: 'المدير العام',
    isActive: true
  }
];

let students = [
  {
    _id: '2',
    studentCode: '123456',
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '+966501234567',
    isActive: true,
    enrolledCourses: ['1', '2'],
    completedCourses: [],
    certificates: [],
    totalWatchTime: 450, // بالدقائق
    progress: {
      '1': { completedVideos: 8, totalVideos: 24, progress: 33 },
      '2': { completedVideos: 3, totalVideos: 18, progress: 17 }
    },
    joinDate: '2024-01-15',
    lastActivity: new Date().toISOString(),
    avatar: '/uploads/avatars/student1.jpg',
    notes: 'طالب مجتهد ومتفاعل'
  },
  {
    _id: '3',
    studentCode: '789012',
    name: 'فاطمة علي حسن',
    email: '<EMAIL>',
    phone: '+966507654321',
    isActive: true,
    enrolledCourses: ['1'],
    completedCourses: ['3'],
    certificates: ['CERT-2024-001'],
    totalWatchTime: 680,
    progress: {
      '1': { completedVideos: 24, totalVideos: 24, progress: 100 }
    },
    joinDate: '2024-01-10',
    lastActivity: new Date().toISOString(),
    avatar: '/uploads/avatars/student2.jpg',
    notes: 'طالبة متميزة وسريعة التعلم'
  },
  {
    _id: '4',
    studentCode: '345678',
    name: 'محمد عبدالله',
    email: '<EMAIL>',
    phone: '+966509876543',
    isActive: true,
    enrolledCourses: ['2', '3'],
    completedCourses: [],
    certificates: [],
    totalWatchTime: 120,
    progress: {
      '2': { completedVideos: 5, totalVideos: 18, progress: 28 },
      '3': { completedVideos: 2, totalVideos: 15, progress: 13 }
    },
    joinDate: '2024-02-01',
    lastActivity: new Date().toISOString(),
    avatar: '/uploads/avatars/student3.jpg',
    notes: 'يحتاج إلى متابعة إضافية'
  },
  {
    _id: '5',
    studentCode: '567890',
    name: 'سارة أحمد',
    email: '<EMAIL>',
    phone: '+966512345678',
    isActive: false,
    enrolledCourses: ['1'],
    completedCourses: [],
    certificates: [],
    totalWatchTime: 45,
    progress: {
      '1': { completedVideos: 2, totalVideos: 24, progress: 8 }
    },
    joinDate: '2024-02-10',
    lastActivity: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    avatar: '/uploads/avatars/student4.jpg',
    notes: 'توقفت عن المتابعة'
  }
];

let courses = [
  {
    _id: '1',
    title: 'أساسيات التسويق الرقمي',
    description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف مع أحدث الاستراتيجيات والأدوات',
    price: 299,
    originalPrice: 499,
    discount: 40,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.8,
    reviewsCount: 156,
    totalVideos: 24,
    duration: '8 ساعات',
    level: 'مبتدئ',
    language: 'العربية',
    category: 'التسويق الرقمي',
    tags: ['تسويق', 'رقمي', 'مبتدئ', 'أساسيات'],
    thumbnail: '/api/placeholder/300/200',
    trailer: '/api/placeholder/video/trailer1.mp4',
    requirements: [
      'لا توجد متطلبات مسبقة',
      'جهاز كمبيوتر أو هاتف ذكي',
      'اتصال بالإنترنت'
    ],
    whatYouWillLearn: [
      'فهم أساسيات التسويق الرقمي',
      'إنشاء استراتيجية تسويقية فعالة',
      'استخدام وسائل التواصل الاجتماعي للتسويق',
      'تحليل البيانات وقياس النتائج',
      'إنشاء حملات إعلانية ناجحة'
    ],
    modules: [
      {
        title: 'مقدمة في التسويق الرقمي',
        description: 'تعرف على أساسيات التسويق الرقمي وأهميته',
        order: 1,
        videos: [
          {
            id: 1,
            title: 'ما هو التسويق الرقمي؟',
            description: 'مقدمة شاملة عن التسويق الرقمي وأهميته في العصر الحديث',
            duration: '15:30',
            videoUrl: '/api/placeholder/video/module1-video1.mp4',
            order: 1,
            isPreview: true,
            isCompleted: false
          },
          {
            id: 2,
            title: 'الفرق بين التسويق التقليدي والرقمي',
            description: 'مقارنة بين أساليب التسويق التقليدية والرقمية',
            duration: '12:45',
            videoUrl: '/api/placeholder/video/module1-video2.mp4',
            order: 2,
            isPreview: false,
            isCompleted: false
          }
        ]
      },
      {
        title: 'استراتيجيات التسويق الرقمي',
        description: 'تعلم كيفية وضع استراتيجية تسويقية فعالة',
        order: 2,
        videos: [
          {
            id: 3,
            title: 'تحديد الجمهور المستهدف',
            description: 'كيفية تحديد وفهم جمهورك المستهدف',
            duration: '18:20',
            videoUrl: '/api/placeholder/video/module2-video1.mp4',
            order: 1,
            isPreview: false,
            isCompleted: false
          },
          {
            id: 4,
            title: 'وضع الأهداف التسويقية',
            description: 'تعلم كيفية وضع أهداف SMART للتسويق',
            duration: '16:15',
            videoUrl: '/api/placeholder/video/module2-video2.mp4',
            order: 2,
            isPreview: false,
            isCompleted: false
          }
        ]
      }
    ],
    enrolledStudents: ['2', '3']
  },
  {
    _id: '2',
    title: 'إدارة وسائل التواصل الاجتماعي',
    description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية وزيادة المتابعين والتفاعل',
    price: 399,
    originalPrice: 599,
    discount: 33,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.9,
    reviewsCount: 89,
    totalVideos: 18,
    duration: '6 ساعات',
    level: 'متوسط',
    language: 'العربية',
    category: 'وسائل التواصل الاجتماعي',
    tags: ['سوشيال ميديا', 'إدارة', 'محتوى', 'تفاعل'],
    thumbnail: '/api/placeholder/300/200',
    trailer: '/api/placeholder/video/trailer2.mp4',
    requirements: [
      'معرفة أساسية بوسائل التواصل الاجتماعي',
      'حساب على منصات التواصل الاجتماعي',
      'جهاز كمبيوتر أو هاتف ذكي'
    ],
    whatYouWillLearn: [
      'إنشاء استراتيجية محتوى فعالة',
      'جدولة المنشورات وإدارة الوقت',
      'زيادة التفاعل والمتابعين',
      'تحليل الأداء وقياس النتائج',
      'التعامل مع التعليقات والرسائل'
    ],
    modules: [
      {
        title: 'أساسيات إدارة وسائل التواصل',
        description: 'تعلم الأساسيات المهمة لإدارة حسابات التواصل الاجتماعي',
        order: 1,
        videos: [
          {
            id: 1,
            title: 'اختيار المنصات المناسبة',
            description: 'كيفية اختيار منصات التواصل المناسبة لعملك',
            duration: '20:15',
            videoUrl: '/api/placeholder/video/social-module1-video1.mp4',
            order: 1,
            isPreview: true,
            isCompleted: false
          },
          {
            id: 2,
            title: 'إنشاء ملف تعريفي احترافي',
            description: 'كيفية إنشاء ملف تعريفي جذاب ومحترف',
            duration: '18:30',
            videoUrl: '/api/placeholder/video/social-module1-video2.mp4',
            order: 2,
            isPreview: false,
            isCompleted: false
          }
        ]
      }
    ],
    enrolledStudents: ['2']
  },
  {
    _id: '3',
    title: 'التسويق عبر البريد الإلكتروني',
    description: 'استراتيجيات فعالة للتسويق عبر البريد الإلكتروني وزيادة المبيعات',
    price: 249,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.7,
    totalVideos: 8,
    duration: '5 ساعات',
    level: 'مبتدئ',
    category: 'التسويق الإلكتروني',
    thumbnail: '/api/placeholder/300/200',
    videos: [
      { id: 1, title: 'بناء قائمة بريدية', duration: '30:00', isCompleted: false },
      { id: 2, title: 'تصميم الرسائل', duration: '25:45', isCompleted: false }
    ],
    enrolledStudents: []
  }
];

// Routes
app.get('/api/test', (req, res) => {
  res.json({ message: 'الخادم يعمل بشكل صحيح!' });
});

// التحقق من المصادقة
app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = users.find(u => u._id === decoded.userId) ||
                 students.find(s => s._id === decoded.userId);

    if (!user) {
      return res.status(401).json({ message: 'المستخدم غير موجود' });
    }

    res.json({ user: {
      id: user._id,
      email: user.email || undefined,
      studentCode: user.studentCode || undefined,
      name: user.name,
      role: decoded.role
    }});
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// تسجيل دخول المدير
app.post('/api/auth/admin/login', async (req, res) => {
  try {
    console.log('🔐 طلب تسجيل دخول مدير:', req.body);
    const { email, password } = req.body;
    
    const admin = users.find(u => u.email === email && u.role === 'admin');
    if (!admin) {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    // للاختبار، سنقبل كلمة المرور "Admin123!"
    if (password !== 'Admin123!') {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    const token = jwt.sign(
      { userId: admin._id, role: admin.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: admin._id,
        email: admin.email,
        name: admin.name,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول المدير:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل دخول الطالب
app.post('/api/auth/student/login', async (req, res) => {
  try {
    console.log('🎓 طلب تسجيل دخول طالب:', req.body);
    const { code } = req.body;
    const studentCode = code;
    
    const student = students.find(s => s.studentCode === studentCode);
    if (!student) {
      return res.status(401).json({ message: 'كود الطالب غير صحيح' });
    }

    const token = jwt.sign(
      { userId: student._id, role: 'student' },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: student._id,
        studentCode: student.studentCode,
        name: student.name,
        role: 'student'
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الإحصائيات
app.get('/api/admin/stats', (req, res) => {
  res.json({
    totalStudents: students.length,
    totalCourses: courses.length,
    totalVideos: courses.reduce((total, course) => total + course.videos.length, 0),
    totalCertificates: 0
  });
});

// الحصول على الطلاب
app.get('/api/admin/students', (req, res) => {
  res.json(students);
});

// الحصول على الدورات
app.get('/api/admin/courses', (req, res) => {
  res.json(courses);
});

// إضافة طالب جديد
app.post('/api/admin/students', (req, res) => {
  const { name } = req.body;
  const studentCode = Math.floor(100000 + Math.random() * 900000).toString();
  
  const newStudent = {
    _id: (students.length + 1).toString(),
    studentCode,
    name,
    isActive: true,
    courses: []
  };
  
  students.push(newStudent);
  res.json(newStudent);
});

// الحصول على جميع الكورسات (للمدير)
app.get('/api/admin/courses', (req, res) => {
  console.log('📚 طلب جلب الكورسات من المدير');
  res.json(courses);
});

// إضافة دورة جديدة
app.post('/api/admin/courses', (req, res) => {
  const { title, description, price, level, duration, category } = req.body;

  const newCourse = {
    _id: (courses.length + 1).toString(),
    title,
    description,
    price: parseInt(price),
    level: level || 'مبتدئ',
    duration: duration || '',
    category: category || '',
    instructor: 'علاء عبد الحميد',
    rating: 4.5,
    totalVideos: 0,
    isActive: true,
    videos: [],
    enrolledStudents: []
  };

  courses.push(newCourse);
  res.json(newCourse);
});

// الحصول على دورات الطالب
app.get('/api/student/courses', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const student = students.find(s => s._id === decoded.userId);

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    const studentCourses = student.enrolledCourses.map(courseId => {
      const course = courses.find(c => c._id === courseId);
      if (!course) return null;

      const progress = student.progress[courseId] || { completedVideos: 0, totalVideos: course.totalVideos, progress: 0 };

      return {
        _id: course._id,
        title: course.title,
        description: course.description,
        instructor: course.instructor,
        rating: course.rating,
        level: course.level,
        duration: course.duration,
        category: course.category,
        totalVideos: course.totalVideos,
        completedVideos: progress.completedVideos,
        progress: progress.progress,
        isEnrolled: true,
        videos: course.videos
      };
    }).filter(course => course !== null);

    res.json(studentCourses);
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// الحصول على إحصائيات الطالب
app.get('/api/student/stats', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const student = students.find(s => s._id === decoded.userId);

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    let totalVideos = 0;
    let completedVideos = 0;
    let certificates = 0;

    student.enrolledCourses.forEach(courseId => {
      const course = courses.find(c => c._id === courseId);
      if (course) {
        totalVideos += course.totalVideos;
        const progress = student.progress[courseId];
        if (progress) {
          completedVideos += progress.completedVideos;
          if (progress.progress === 100) {
            certificates++;
          }
        }
      }
    });

    const overallProgress = totalVideos > 0 ? Math.round((completedVideos / totalVideos) * 100) : 0;

    res.json({
      enrolledCourses: student.enrolledCourses.length,
      completedVideos,
      totalVideos,
      certificates,
      overallProgress
    });
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// الحصول على جميع الطلاب (للمدير)
app.get('/api/admin/students', (req, res) => {
  console.log('👥 طلب جلب الطلاب من المدير');
  const studentsWithCourseNames = students.map(student => ({
    ...student,
    enrolledCoursesNames: student.enrolledCourses.map(courseId => {
      const course = courses.find(c => c._id === courseId);
      return course ? course.title : 'كورس محذوف';
    })
  }));
  res.json(studentsWithCourseNames);
});

// إضافة طالب جديد
app.post('/api/admin/students', (req, res) => {
  const { name, isActive = true } = req.body;

  const newStudent = {
    _id: (students.length + 2).toString(),
    studentCode: Math.floor(100000 + Math.random() * 900000).toString(),
    name,
    isActive,
    enrolledCourses: [],
    progress: {},
    joinDate: new Date().toISOString().split('T')[0],
    lastActivity: new Date().toISOString()
  };

  students.push(newStudent);
  res.json(newStudent);
});

// تحديث طالب
app.put('/api/admin/students/:id', (req, res) => {
  const { id } = req.params;
  const { name, isActive } = req.body;

  const studentIndex = students.findIndex(s => s._id === id);
  if (studentIndex === -1) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  students[studentIndex] = {
    ...students[studentIndex],
    name: name || students[studentIndex].name,
    isActive: isActive !== undefined ? isActive : students[studentIndex].isActive
  };

  res.json(students[studentIndex]);
});

// حذف طالب
app.delete('/api/admin/students/:id', (req, res) => {
  const { id } = req.params;
  const studentIndex = students.findIndex(s => s._id === id);

  if (studentIndex === -1) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  students.splice(studentIndex, 1);
  res.json({ message: 'تم حذف الطالب بنجاح' });
});

// تسجيل طالب في كورس
app.post('/api/admin/students/:studentId/enroll/:courseId', (req, res) => {
  const { studentId, courseId } = req.params;

  const student = students.find(s => s._id === studentId);
  const course = courses.find(c => c._id === courseId);

  if (!student) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  if (!course) {
    return res.status(404).json({ message: 'الكورس غير موجود' });
  }

  if (!student.enrolledCourses.includes(courseId)) {
    student.enrolledCourses.push(courseId);
    student.progress[courseId] = {
      completedVideos: 0,
      totalVideos: course.totalVideos,
      progress: 0
    };

    if (!course.enrolledStudents.includes(studentId)) {
      course.enrolledStudents.push(studentId);
    }
  }

  res.json({ message: 'تم تسجيل الطالب في الكورس بنجاح' });
});

// إلغاء تسجيل طالب من كورس
app.delete('/api/admin/students/:studentId/unenroll/:courseId', (req, res) => {
  const { studentId, courseId } = req.params;

  const student = students.find(s => s._id === studentId);
  const course = courses.find(c => c._id === courseId);

  if (!student) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  student.enrolledCourses = student.enrolledCourses.filter(id => id !== courseId);
  delete student.progress[courseId];

  if (course) {
    course.enrolledStudents = course.enrolledStudents.filter(id => id !== studentId);
  }

  res.json({ message: 'تم إلغاء تسجيل الطالب من الكورس بنجاح' });
});

// إرسال رسالة للمدير
app.post('/api/student/contact-admin', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const student = students.find(s => s._id === decoded.userId);

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    const { subject, message, priority, contactMethod } = req.body;

    // في التطبيق الحقيقي، ستحفظ الرسالة في قاعدة البيانات وترسل إشعار للمدير
    const contactMessage = {
      id: Date.now().toString(),
      from: {
        studentId: student._id,
        studentCode: student.studentCode,
        name: student.name
      },
      subject,
      message,
      priority,
      contactMethod,
      timestamp: new Date().toISOString(),
      status: 'جديد'
    };

    console.log('📧 رسالة جديدة من الطالب:', contactMessage);

    res.json({
      message: 'تم إرسال رسالتك بنجاح! سيتم الرد عليك في أقرب وقت ممكن.',
      messageId: contactMessage.id
    });
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// الحصول على إحصائيات لوحة التحكم (للمدير)
app.get('/api/admin/dashboard-stats', (req, res) => {
  console.log('📊 طلب إحصائيات لوحة التحكم من المدير');

  const totalStudents = students.length;
  const activeStudents = students.filter(s => s.isActive).length;
  const totalCourses = courses.length;
  const activeCourses = courses.filter(c => c.isActive).length;
  const totalVideos = courses.reduce((total, course) => {
    return total + course.modules.reduce((moduleTotal, module) => {
      return moduleTotal + module.videos.length;
    }, 0);
  }, 0);
  const totalEnrollments = students.reduce((total, student) => total + (student.enrolledCourses?.length || 0), 0);
  const totalRevenue = totalEnrollments * 350; // متوسط سعر الكورس

  res.json({
    totalStudents,
    activeStudents,
    totalCourses,
    activeCourses,
    totalVideos,
    totalCertificates: Math.floor(totalEnrollments * 0.7), // 70% معدل إكمال
    totalRevenue,
    monthlyRevenue: Math.floor(totalRevenue * 0.3),
    recentActivity: [
      {
        id: 1,
        type: 'student',
        title: 'طالب جديد انضم',
        description: 'أحمد محمد انضم إلى المنصة',
        time: 'منذ 5 دقائق',
        icon: 'person_add'
      },
      {
        id: 2,
        type: 'course',
        title: 'تم تسجيل طالب في كورس',
        description: 'فاطمة علي سجلت في أساسيات التسويق الرقمي',
        time: 'منذ ساعة',
        icon: 'school'
      },
      {
        id: 3,
        type: 'certificate',
        title: 'شهادة جديدة',
        description: 'محمد عبدالله حصل على شهادة إتمام كورس',
        time: 'منذ 3 ساعات',
        icon: 'workspace_premium'
      },
      {
        id: 4,
        type: 'payment',
        title: 'دفعة جديدة',
        description: 'تم استلام دفعة بقيمة 399 ريال',
        time: 'منذ 6 ساعات',
        icon: 'payment'
      }
    ]
  });
});

// إحصائيات مفصلة للمدير
app.get('/api/admin/analytics', (req, res) => {
  const { period = '7d' } = req.query;

  // محاكاة بيانات تحليلية
  const analytics = {
    enrollments: {
      labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
      data: [12, 19, 8, 15, 22, 18, 25]
    },
    revenue: {
      labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
      data: [3588, 5681, 2392, 4485, 6578, 5382, 7475]
    },
    topCourses: [
      { name: 'أساسيات التسويق الرقمي', enrollments: 156, revenue: 46644 },
      { name: 'إدارة وسائل التواصل الاجتماعي', enrollments: 89, revenue: 35511 },
      { name: 'التسويق عبر البريد الإلكتروني', enrollments: 67, revenue: 16683 }
    ],
    studentProgress: {
      completed: 45,
      inProgress: 78,
      notStarted: 23
    }
  };

  res.json(analytics);
});

// إدارة الكورسات - تحديث كورس
app.put('/api/admin/courses/:id', (req, res) => {
  const { id } = req.params;
  const courseIndex = courses.findIndex(c => c._id === id);

  if (courseIndex === -1) {
    return res.status(404).json({ message: 'الكورس غير موجود' });
  }

  courses[courseIndex] = {
    ...courses[courseIndex],
    ...req.body,
    updatedAt: new Date()
  };

  res.json(courses[courseIndex]);
});

// إدارة الكورسات - حذف كورس
app.delete('/api/admin/courses/:id', (req, res) => {
  const { id } = req.params;
  const courseIndex = courses.findIndex(c => c._id === id);

  if (courseIndex === -1) {
    return res.status(404).json({ message: 'الكورس غير موجود' });
  }

  courses.splice(courseIndex, 1);
  res.json({ message: 'تم حذف الكورس بنجاح' });
});

// إدارة الفيديوهات - إضافة فيديو لكورس
app.post('/api/admin/courses/:courseId/videos', (req, res) => {
  const { courseId } = req.params;
  const { moduleIndex, video } = req.body;

  const course = courses.find(c => c._id === courseId);
  if (!course) {
    return res.status(404).json({ message: 'الكورس غير موجود' });
  }

  if (!course.modules[moduleIndex]) {
    return res.status(404).json({ message: 'الوحدة غير موجودة' });
  }

  const newVideo = {
    id: Date.now(),
    ...video,
    order: course.modules[moduleIndex].videos.length + 1,
    isCompleted: false
  };

  course.modules[moduleIndex].videos.push(newVideo);
  course.totalVideos = course.modules.reduce((total, module) => total + module.videos.length, 0);
  course.updatedAt = new Date();

  res.json(newVideo);
});

// إدارة الإعدادات
app.get('/api/admin/settings', (req, res) => {
  const settings = {
    siteName: 'منصة كورسات علاء عبد الحميد',
    siteDescription: 'منصة تعليمية متخصصة في التسويق الرقمي',
    logo: '/uploads/logo.png',
    contactEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    phone: '+966501234567',
    address: 'الرياض، المملكة العربية السعودية',
    socialLinks: {
      facebook: 'https://facebook.com/alaa.courses',
      instagram: 'https://instagram.com/alaa.courses',
      twitter: 'https://twitter.com/alaa_courses',
      linkedin: 'https://linkedin.com/company/alaa-courses',
      youtube: 'https://youtube.com/c/alaacourses'
    },
    paymentMethods: ['visa', 'mastercard', 'mada', 'applepay'],
    currency: 'SAR',
    timezone: 'Asia/Riyadh',
    language: 'ar',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: false
  };

  res.json(settings);
});

// تحديث الإعدادات
app.put('/api/admin/settings', (req, res) => {
  // في التطبيق الحقيقي، ستحفظ الإعدادات في قاعدة البيانات
  console.log('تحديث الإعدادات:', req.body);
  res.json({ message: 'تم تحديث الإعدادات بنجاح', settings: req.body });
});

// إدارة الشهادات
app.get('/api/admin/certificates', (req, res) => {
  const certificates = [
    {
      _id: '1',
      studentId: '2',
      studentName: 'فاطمة علي حسن',
      courseId: '1',
      courseName: 'أساسيات التسويق الرقمي',
      certificateNumber: 'CERT-2024-001',
      issuedDate: new Date('2024-02-15'),
      grade: 'ممتاز',
      certificateUrl: '/uploads/certificates/cert-001.pdf',
      isActive: true
    },
    {
      _id: '2',
      studentId: '3',
      studentName: 'محمد عبدالله',
      courseId: '2',
      courseName: 'إدارة وسائل التواصل الاجتماعي',
      certificateNumber: 'CERT-2024-002',
      issuedDate: new Date('2024-02-20'),
      grade: 'جيد جداً',
      certificateUrl: '/uploads/certificates/cert-002.pdf',
      isActive: true
    }
  ];

  res.json(certificates);
});

// إنشاء شهادة جديدة
app.post('/api/admin/certificates', (req, res) => {
  const { studentId, courseId, grade } = req.body;

  const student = students.find(s => s._id === studentId);
  const course = courses.find(c => c._id === courseId);

  if (!student || !course) {
    return res.status(404).json({ message: 'الطالب أو الكورس غير موجود' });
  }

  const certificate = {
    _id: Date.now().toString(),
    studentId,
    studentName: student.name,
    courseId,
    courseName: course.title,
    certificateNumber: `CERT-2024-${String(Date.now()).slice(-3)}`,
    issuedDate: new Date(),
    grade: grade || 'ممتاز',
    certificateUrl: `/uploads/certificates/cert-${Date.now()}.pdf`,
    isActive: true
  };

  res.json(certificate);
});

app.listen(PORT, () => {
  console.log(`🚀 الخادم المؤقت يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الموقع متاح على: http://localhost:${PORT}`);
  console.log(`📱 API متاح على: http://localhost:${PORT}/api/test`);
  console.log(`✅ الخادم يعمل بدون قاعدة بيانات للاختبار`);
  console.log(`👨‍💼 بيانات المدير: <EMAIL> / Admin123!`);
  console.log(`👨‍🎓 كود طالب تجريبي: 123456`);
});
