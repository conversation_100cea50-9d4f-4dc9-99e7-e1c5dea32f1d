import { createClient } from '@supabase/supabase-js';

/**
 * إعداد Supabase للأكاديمية
 * Skills World Academy - Supabase Configuration
 */

// إعدادات Supabase من متغيرات البيئة مع قيم احتياطية
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://auwpeiicfwcysoexoogf.supabase.co';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1d3BlaWljZndjeXNvZXhvb2dmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzA3MzAsImV4cCI6MjA2Nzc0NjczMH0.3twZ1c6M2EyBoBe66PjmwsUwvzK0nnV89Bt3yLcGBjY';

// التحقق من وجود المتغيرات المطلوبة
if (!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('your-project')) {
  console.warn('⚠️ استخدام إعدادات Supabase الافتراضية');
  console.log('للحصول على أفضل أداء، يرجى تحديث ملف .env بالمتغيرات الصحيحة');
} else {
  console.log('✅ تم تحميل إعدادات Supabase بنجاح');
}

// إنشاء عميل Supabase واحد مع إعدادات محسنة
let supabaseInstance = null;

// دالة للحصول على instance واحد فقط
const getSupabaseInstance = () => {
  if (!supabaseInstance) {
    console.log('🔧 إنشاء Supabase client جديد...');
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'skills-world-academy-auth'
      },
      realtime: {
        params: {
          eventsPerSecond: 5
        }
      },
      db: {
        schema: 'public'
      },
      global: {
        headers: {
          'X-Client-Info': 'skills-world-academy@3.0.0'
        }
      }
    });

    // تسجيل معالج واحد فقط للأحداث
    supabaseInstance.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_IN') {
        console.log('✅ Supabase: تم تسجيل الدخول');
      } else if (event === 'SIGNED_OUT') {
        console.log('🚪 Supabase: تم تسجيل الخروج');
      }
    });
  }
  return supabaseInstance;
};

export const supabase = getSupabaseInstance();

// دالة اختبار الاتصال
export const testSupabaseConnection = async () => {
  try {
    console.log('🔄 اختبار الاتصال مع Supabase...');

    // اختبار بسيط للاتصال
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .eq('key', 'academy_name')
      .limit(1)
      .maybeSingle();

    if (error && error.code !== 'PGRST116') {
      console.error('❌ خطأ في الاتصال مع Supabase:', error);
      return false;
    }

    console.log('✅ تم الاتصال بنجاح مع Supabase');

    if (data && data.value) {
      console.log('🏫 اسم الأكاديمية:', data.value);
    } else {
      console.log('🏫 اسم الأكاديمية: SKILLS WORLD ACADEMY (افتراضي)');
    }

    return true;
  } catch (error) {
    console.error('❌ خطأ في اختبار الاتصال:', error);
    return false;
  }
};

// دالة تهيئة Supabase مع معالجة أخطاء محسنة
export const initializeSupabase = async () => {
  try {
    console.log('🚀 تهيئة Supabase...');

    // اختبار الاتصال مع معالجة أخطاء محسنة
    const isConnected = await testSupabaseConnection();

    if (!isConnected) {
      console.warn('⚠️ تحذير: لا يمكن الاتصال مع Supabase، سيتم المتابعة بدون المزامنة الفورية');
      return false;
    }

    // إعداد مستمعات المزامنة الفورية
    setupRealtimeListeners();

    console.log('✅ تم تهيئة Supabase بنجاح');
    return true;
  } catch (error) {
    console.warn('⚠️ تحذير في تهيئة Supabase:', error.message);
    // لا نرمي الخطأ لتجنب توقف التطبيق
    return false;
  }
};

// إعداد مستمعات المزامنة الفورية مع معالجة أخطاء محسنة
const setupRealtimeListeners = () => {
  console.log('🔄 إعداد مستمعات المزامنة الفورية...');

  try {
    // مستمع تغييرات الكورسات
    supabase
      .channel('courses_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'courses' },
        (payload) => {
          try {
            console.log('📚 تغيير في الكورسات:', payload);
          } catch (error) {
            console.error('❌ خطأ في معالجة تغيير الكورسات:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ تم الاشتراك في تغييرات الكورسات');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ خطأ في قناة الكورسات');
        }
      });

    // مستمع تغييرات الطلاب
    supabase
      .channel('users_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'users', filter: 'role=eq.student' },
        (payload) => {
          try {
            console.log('👨‍🎓 تغيير في الطلاب:', payload);
          } catch (error) {
            console.error('❌ خطأ في معالجة تغيير الطلاب:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ تم الاشتراك في تغييرات الطلاب');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ خطأ في قناة الطلاب');
        }
      });

    // مستمع تغييرات التسجيلات
    supabase
      .channel('enrollments_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'enrollments' },
        (payload) => {
          try {
            console.log('📝 تغيير في التسجيلات:', payload);
          } catch (error) {
            console.error('❌ خطأ في معالجة تغيير التسجيلات:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ تم الاشتراك في تغييرات التسجيلات');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ خطأ في قناة التسجيلات');
        }
      });

    // مستمع تغييرات الأسئلة الشائعة
    supabase
      .channel('faqs_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'faqs' },
        (payload) => {
          try {
            console.log('❓ تغيير في الأسئلة الشائعة:', payload);
          } catch (error) {
            console.error('❌ خطأ في معالجة تغيير الأسئلة الشائعة:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ تم الاشتراك في تغييرات الأسئلة الشائعة');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ خطأ في قناة الأسئلة الشائعة');
        }
      });

    console.log('✅ تم إعداد مستمعات المزامنة الفورية');
  } catch (error) {
    console.error('❌ خطأ في إعداد مستمعات المزامنة الفورية:', error);
  }
};

// دالة تنظيف الاتصالات
export const cleanupSupabase = () => {
  console.log('🧹 تنظيف اتصالات Supabase...');
  supabase.removeAllChannels();
};

// تصدير العميل الافتراضي
export default supabase;
