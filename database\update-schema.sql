-- تحديث مخطط قاعدة البيانات - SKILLS WORLD ACADEMY
-- Database Schema Update for Video and Document Management

-- إنشاء جدول فيديوهات الكورسات المحدث
DROP TABLE IF EXISTS course_videos CASCADE;
CREATE TABLE course_videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    backup_url TEXT, -- رابط النسخة الاحتياطية في Supabase
    duration VARCHAR(20), -- مدة الفيديو (مثل "15:30")
    video_order INTEGER NOT NULL DEFAULT 1,
    is_published BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT FALSE,
    thumbnail_url TEXT,
    file_name VARCHAR(255),
    file_size BIGINT, -- حج<PERSON> الملف بالبايت
    upload_path TEXT, -- م<PERSON>ار الملف في التخزين
    views_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول مستندات الكورسات
DROP TABLE IF EXISTS course_documents CASCADE;
CREATE TABLE course_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    pdf_url TEXT NOT NULL,
    backup_url TEXT, -- رابط النسخة الاحتياطية في Supabase
    category VARCHAR(50) DEFAULT 'material' CHECK (category IN ('material', 'assignment', 'reference')),
    document_order INTEGER NOT NULL DEFAULT 1,
    is_public BOOLEAN DEFAULT TRUE,
    is_downloadable BOOLEAN DEFAULT TRUE,
    file_name VARCHAR(255),
    file_size BIGINT, -- حجم الملف بالبايت
    upload_path TEXT, -- مسار الملف في التخزين
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول تقدم الطلاب في الفيديوهات
DROP TABLE IF EXISTS student_video_progress CASCADE;
CREATE TABLE student_video_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    video_id UUID NOT NULL REFERENCES course_videos(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    is_completed BOOLEAN DEFAULT FALSE,
    watch_time INTEGER DEFAULT 0, -- الوقت المشاهد بالثواني
    last_position INTEGER DEFAULT 0, -- آخر موضع توقف بالثواني
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(student_id, video_id)
);

-- إنشاء جدول تحميلات المستندات
DROP TABLE IF EXISTS document_downloads CASCADE;
CREATE TABLE document_downloads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID NOT NULL REFERENCES course_documents(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    downloaded_at TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_course_videos_course_id ON course_videos(course_id);
CREATE INDEX idx_course_videos_order ON course_videos(course_id, video_order);
CREATE INDEX idx_course_videos_published ON course_videos(course_id, is_published);
CREATE INDEX idx_course_videos_free ON course_videos(is_free);

CREATE INDEX idx_course_documents_course_id ON course_documents(course_id);
CREATE INDEX idx_course_documents_order ON course_documents(course_id, document_order);
CREATE INDEX idx_course_documents_category ON course_documents(course_id, category);
CREATE INDEX idx_course_documents_public ON course_documents(course_id, is_public);

CREATE INDEX idx_student_video_progress_student ON student_video_progress(student_id);
CREATE INDEX idx_student_video_progress_video ON student_video_progress(video_id);
CREATE INDEX idx_student_video_progress_course ON student_video_progress(course_id);
CREATE INDEX idx_student_video_progress_completed ON student_video_progress(student_id, is_completed);

CREATE INDEX idx_document_downloads_student ON document_downloads(student_id);
CREATE INDEX idx_document_downloads_document ON document_downloads(document_id);
CREATE INDEX idx_document_downloads_date ON document_downloads(downloaded_at);

-- إنشاء triggers للتحديث التلقائي
CREATE TRIGGER update_course_videos_updated_at 
    BEFORE UPDATE ON course_videos 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_course_documents_updated_at 
    BEFORE UPDATE ON course_documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_video_progress_updated_at 
    BEFORE UPDATE ON student_video_progress 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تفعيل Row Level Security (RLS)
ALTER TABLE course_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_video_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_downloads ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات RLS للفيديوهات
CREATE POLICY "course_videos_select_policy" ON course_videos
    FOR SELECT USING (
        is_published = true OR 
        EXISTS (
            SELECT 1 FROM enrollments 
            WHERE enrollments.course_id = course_videos.course_id 
            AND enrollments.student_id = auth.uid()
        )
    );

CREATE POLICY "course_videos_insert_policy" ON course_videos
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "course_videos_update_policy" ON course_videos
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "course_videos_delete_policy" ON course_videos
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- إنشاء سياسات RLS للمستندات
CREATE POLICY "course_documents_select_policy" ON course_documents
    FOR SELECT USING (
        is_public = true OR 
        EXISTS (
            SELECT 1 FROM enrollments 
            WHERE enrollments.course_id = course_documents.course_id 
            AND enrollments.student_id = auth.uid()
        )
    );

CREATE POLICY "course_documents_insert_policy" ON course_documents
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "course_documents_update_policy" ON course_documents
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "course_documents_delete_policy" ON course_documents
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- إنشاء سياسات RLS لتقدم الطلاب
CREATE POLICY "student_video_progress_select_policy" ON student_video_progress
    FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "student_video_progress_insert_policy" ON student_video_progress
    FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "student_video_progress_update_policy" ON student_video_progress
    FOR UPDATE USING (student_id = auth.uid());

-- إنشاء سياسات RLS لتحميلات المستندات
CREATE POLICY "document_downloads_select_policy" ON document_downloads
    FOR SELECT USING (
        student_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "document_downloads_insert_policy" ON document_downloads
    FOR INSERT WITH CHECK (student_id = auth.uid());

-- إنشاء دوال مساعدة
CREATE OR REPLACE FUNCTION get_course_progress(p_course_id UUID, p_student_id UUID)
RETURNS JSON AS $$
DECLARE
    total_videos INTEGER;
    completed_videos INTEGER;
    progress_percentage NUMERIC;
BEGIN
    -- حساب إجمالي الفيديوهات المنشورة
    SELECT COUNT(*) INTO total_videos
    FROM course_videos 
    WHERE course_id = p_course_id AND is_published = true;
    
    -- حساب الفيديوهات المكتملة
    SELECT COUNT(*) INTO completed_videos
    FROM student_video_progress 
    WHERE course_id = p_course_id 
    AND student_id = p_student_id 
    AND is_completed = true;
    
    -- حساب النسبة المئوية
    IF total_videos > 0 THEN
        progress_percentage := ROUND((completed_videos::NUMERIC / total_videos::NUMERIC) * 100, 2);
    ELSE
        progress_percentage := 0;
    END IF;
    
    RETURN json_build_object(
        'total_videos', total_videos,
        'completed_videos', completed_videos,
        'progress_percentage', progress_percentage
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء دالة لتحديث إحصائيات الكورس
CREATE OR REPLACE FUNCTION update_course_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث عدد الفيديوهات في جدول الكورسات
    UPDATE courses 
    SET total_videos = (
        SELECT COUNT(*) 
        FROM course_videos 
        WHERE course_id = NEW.course_id 
        AND is_published = true
    )
    WHERE id = NEW.course_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث إحصائيات الكورس
CREATE TRIGGER update_course_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON course_videos
    FOR EACH ROW EXECUTE FUNCTION update_course_stats();

-- إدراج بيانات تجريبية (اختياري)
-- يمكن حذف هذا القسم في الإنتاج
/*
INSERT INTO course_videos (course_id, title, description, video_url, duration, video_order, is_published, is_free) VALUES
(
    (SELECT id FROM courses LIMIT 1),
    'مقدمة في البرمجة',
    'فيديو تعريفي عن أساسيات البرمجة',
    'https://example.com/video1.mp4',
    '15:30',
    1,
    true,
    true
);

INSERT INTO course_documents (course_id, title, description, pdf_url, category, document_order, is_public) VALUES
(
    (SELECT id FROM courses LIMIT 1),
    'كتاب أساسيات البرمجة',
    'كتاب شامل عن أساسيات البرمجة',
    'https://example.com/book1.pdf',
    'material',
    1,
    true
);
*/

-- إنشاء views مفيدة
CREATE OR REPLACE VIEW course_content_summary AS
SELECT 
    c.id as course_id,
    c.title as course_title,
    COUNT(DISTINCT cv.id) as total_videos,
    COUNT(DISTINCT cd.id) as total_documents,
    COUNT(DISTINCT cv.id) FILTER (WHERE cv.is_published = true) as published_videos,
    COUNT(DISTINCT cv.id) FILTER (WHERE cv.is_free = true) as free_videos,
    COUNT(DISTINCT cd.id) FILTER (WHERE cd.is_public = true) as public_documents
FROM courses c
LEFT JOIN course_videos cv ON c.id = cv.course_id
LEFT JOIN course_documents cd ON c.id = cd.course_id
GROUP BY c.id, c.title;

COMMENT ON TABLE course_videos IS 'جدول فيديوهات الكورسات مع دعم التخزين المختلط';
COMMENT ON TABLE course_documents IS 'جدول مستندات الكورسات مع إدارة التحميل';
COMMENT ON TABLE student_video_progress IS 'جدول تتبع تقدم الطلاب في مشاهدة الفيديوهات';
COMMENT ON TABLE document_downloads IS 'جدول تتبع تحميلات المستندات';
