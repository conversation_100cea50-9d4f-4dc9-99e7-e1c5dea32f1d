/**
 * خدمة التخزين المتقدمة - SKILLS WORLD ACADEMY
 * Advanced Storage Service
 * يدعم Firebase Storage و Supabase Storage مع المزامنة التلقائية
 */

import { storage } from '../firebase/config';
import { supabase } from '../supabase/config';
import {
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  getMetadata
} from 'firebase/storage';

/**
 * فئة خدمة التخزين المتقدمة
 */
export class StorageService {
  constructor() {
    this.uploadProgress = {};
    this.activeUploads = new Map();
    this.storageConfig = {
      firebase: {
        enabled: true,
        buckets: {
          videos: 'course-videos',
          documents: 'course-documents',
          images: 'course-images',
          certificates: 'certificates'
        }
      },
      supabase: {
        enabled: true,
        buckets: {
          videos: 'course-videos',
          documents: 'course-documents', 
          images: 'course-images',
          certificates: 'certificates'
        }
      }
    };
  }

  /**
   * تحقق من وجود buckets في Supabase (بدون إنشاء)
   */
  async checkSupabaseBuckets() {
    try {
      console.log('🔍 فحص buckets في Supabase...');

      // الحصول على قائمة buckets الموجودة
      const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.warn('⚠️ تحذير: لا يمكن الوصول إلى Supabase Storage:', listError.message);
        return false;
      }

      const existingBucketNames = existingBuckets?.map(bucket => bucket.name) || [];
      const requiredBuckets = Object.values(this.storageConfig.supabase.buckets);

      console.log('📋 Buckets الموجودة:', existingBucketNames);
      console.log('📋 Buckets المطلوبة:', requiredBuckets);

      const missingBuckets = requiredBuckets.filter(bucket => !existingBucketNames.includes(bucket));

      if (missingBuckets.length > 0) {
        console.warn('⚠️ Buckets مفقودة:', missingBuckets);
        console.warn('💡 يرجى إنشاء هذه الـ buckets يدوياً في Supabase Dashboard');
        return false;
      }

      console.log('✅ جميع الـ buckets المطلوبة موجودة');
      return true;
    } catch (error) {
      console.error('❌ خطأ في تهيئة Supabase buckets:', error);
    }
  }

  /**
   * رفع ملف إلى Firebase Storage مع تتبع التقدم
   */
  async uploadToFirebase(file, path, onProgress = null) {
    try {
      const fileName = this.generateUniqueFileName(file.name);
      const fullPath = `${path}/${fileName}`;
      const storageRef = ref(storage, fullPath);

      const uploadTask = uploadBytesResumable(storageRef, file);
      this.activeUploads.set(fullPath, uploadTask);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            this.uploadProgress[fullPath] = progress;
            
            if (onProgress) {
              onProgress(progress, snapshot);
            }
          },
          (error) => {
            this.activeUploads.delete(fullPath);
            console.error('❌ خطأ في رفع الملف إلى Firebase:', error);
            reject(error);
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              const metadata = await getMetadata(uploadTask.snapshot.ref);
              
              this.activeUploads.delete(fullPath);
              
              resolve({
                url: downloadURL,
                path: fullPath,
                fileName: fileName,
                size: file.size,
                type: file.type,
                storage: 'firebase',
                metadata: metadata,
                uploadedAt: new Date().toISOString()
              });
            } catch (error) {
              reject(error);
            }
          }
        );
      });
    } catch (error) {
      console.error('❌ خطأ في رفع الملف إلى Firebase:', error);
      throw error;
    }
  }

  /**
   * رفع ملف إلى Supabase Storage
   */
  async uploadToSupabase(file, bucket, path, onProgress = null) {
    try {
      const fileName = this.generateUniqueFileName(file.name);
      const fullPath = `${path}/${fileName}`;

      // محاكاة تقدم الرفع
      if (onProgress) {
        const progressInterval = setInterval(() => {
          const currentProgress = this.uploadProgress[fullPath] || 0;
          if (currentProgress < 90) {
            this.uploadProgress[fullPath] = currentProgress + 10;
            onProgress(this.uploadProgress[fullPath]);
          } else {
            clearInterval(progressInterval);
          }
        }, 200);
      }

      const { error } = await supabase.storage
        .from(bucket)
        .upload(fullPath, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: file.type
        });

      if (error) {
        throw error;
      }

      // الحصول على الرابط العام
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(fullPath);

      if (onProgress) {
        this.uploadProgress[fullPath] = 100;
        onProgress(100);
      }

      return {
        url: urlData.publicUrl,
        path: fullPath,
        fileName: fileName,
        size: file.size,
        type: file.type,
        storage: 'supabase',
        bucket: bucket,
        uploadedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ خطأ في رفع الملف إلى Supabase:', error);
      throw error;
    }
  }

  /**
   * رفع ملف مع نسخة احتياطية تلقائية
   */
  async uploadWithBackup(file, category, courseId, onProgress = null) {
    try {
      const path = `courses/${courseId}/${category}`;
      let primaryResult, backupResult;

      // رفع إلى Firebase أولاً (الأساسي)
      try {
        primaryResult = await this.uploadToFirebase(file, path, (progress) => {
          if (onProgress) onProgress(progress * 0.7); // 70% للرفع الأساسي
        });
      } catch (firebaseError) {
        console.error('❌ فشل في رفع الملف إلى Firebase:', firebaseError);
        throw new Error('فشل في رفع الملف إلى التخزين الأساسي');
      }

      // رفع نسخة احتياطية إلى Supabase
      try {
        const bucket = this.storageConfig.supabase.buckets[category] || 'course-documents';
        backupResult = await this.uploadToSupabase(file, bucket, path, (progress) => {
          if (onProgress) onProgress(70 + (progress * 0.3)); // 30% للنسخة الاحتياطية
        });
        
        primaryResult.backupUrl = backupResult.url;
        primaryResult.backupStorage = 'supabase';
      } catch (supabaseError) {
        console.warn('⚠️ تحذير: فشل في رفع النسخة الاحتياطية إلى Supabase:', supabaseError);
        // لا نرمي خطأ هنا لأن الرفع الأساسي نجح
      }

      return primaryResult;
    } catch (error) {
      console.error('❌ خطأ في رفع الملف مع النسخة الاحتياطية:', error);
      throw error;
    }
  }

  /**
   * حذف ملف من كلا التخزينين
   */
  async deleteFile(filePath, backupBucket = null) {
    const results = { firebase: false, supabase: false };

    // حذف من Firebase
    try {
      const fileRef = ref(storage, filePath);
      await deleteObject(fileRef);
      results.firebase = true;
      console.log('✅ تم حذف الملف من Firebase');
    } catch (error) {
      console.error('❌ خطأ في حذف الملف من Firebase:', error);
    }

    // حذف من Supabase
    if (backupBucket) {
      try {
        const { error } = await supabase.storage
          .from(backupBucket)
          .remove([filePath]);

        if (!error) {
          results.supabase = true;
          console.log('✅ تم حذف الملف من Supabase');
        }
      } catch (error) {
        console.error('❌ خطأ في حذف الملف من Supabase:', error);
      }
    }

    return results;
  }

  /**
   * الحصول على معلومات الملف
   */
  async getFileInfo(filePath, storage = 'firebase') {
    try {
      if (storage === 'firebase') {
        const fileRef = ref(storage, filePath);
        const metadata = await getMetadata(fileRef);
        const url = await getDownloadURL(fileRef);
        
        return {
          url,
          size: metadata.size,
          type: metadata.contentType,
          created: metadata.timeCreated,
          updated: metadata.updated
        };
      } else if (storage === 'supabase') {
        // يمكن إضافة منطق للحصول على معلومات الملف من Supabase
        return null;
      }
    } catch (error) {
      console.error('❌ خطأ في الحصول على معلومات الملف:', error);
      return null;
    }
  }

  /**
   * توليد اسم ملف فريد
   */
  generateUniqueFileName(originalName, prefix = '') {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.substring(originalName.lastIndexOf('.'));
    return `${prefix}${timestamp}_${randomString}${extension}`;
  }

  /**
   * إلغاء رفع الملف
   */
  cancelUpload(filePath) {
    const uploadTask = this.activeUploads.get(filePath);
    if (uploadTask) {
      uploadTask.cancel();
      this.activeUploads.delete(filePath);
      delete this.uploadProgress[filePath];
      return true;
    }
    return false;
  }

  /**
   * الحصول على تقدم الرفع
   */
  getUploadProgress(filePath) {
    return this.uploadProgress[filePath] || 0;
  }

  /**
   * تنظيف الذاكرة
   */
  cleanup() {
    this.uploadProgress = {};
    this.activeUploads.clear();
  }
}

// إنشاء مثيل واحد للخدمة
export const storageService = new StorageService();

// فحص Supabase buckets عند تحميل الخدمة
storageService.checkSupabaseBuckets().catch(console.error);

export default storageService;
