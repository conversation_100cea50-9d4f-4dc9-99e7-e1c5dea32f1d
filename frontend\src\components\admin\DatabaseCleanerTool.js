import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  <PERSON>ton,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  DeleteSweep,
  Warning,
  School,
  People,
  Assignment,
  Help,
  Notifications,
  Storage,
  CheckCircle,
  Cancel
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد أداة التنظيف
import { databaseCleaner } from '../../utils/databaseCleaner';

const DatabaseCleanerTool = () => {
  const [loading, setLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    type: '',
    title: '',
    message: ''
  });
  const [cleaningResults, setCleaningResults] = useState(null);

  // فتح حوار التأكيد
  const openConfirmDialog = (type, title, message) => {
    setConfirmDialog({
      open: true,
      type,
      title,
      message
    });
  };

  // إغلاق حوار التأكيد
  const closeConfirmDialog = () => {
    setConfirmDialog({
      open: false,
      type: '',
      title: '',
      message: ''
    });
  };

  // تنفيذ عملية التنظيف
  const executeCleanup = async () => {
    try {
      setLoading(true);
      closeConfirmDialog();

      let result;
      switch (confirmDialog.type) {
        case 'all':
          result = await databaseCleaner.cleanAllData();
          break;
        case 'courses':
          result = await databaseCleaner.cleanCoursesOnly();
          break;
        case 'students':
          result = await databaseCleaner.cleanStudentsOnly();
          break;
        default:
          throw new Error('نوع تنظيف غير معروف');
      }

      setCleaningResults(result);
      toast.success('تم تنظيف قاعدة البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في التنظيف:', error);
      toast.error('فشل في تنظيف قاعدة البيانات');
    } finally {
      setLoading(false);
    }
  };

  // تنظيف شامل
  const handleCleanAll = () => {
    openConfirmDialog(
      'all',
      'تنظيف شامل لقاعدة البيانات',
      'سيتم حذف جميع البيانات الوهمية من Firebase و Supabase. هذا الإجراء لا يمكن التراجع عنه!'
    );
  };

  // تنظيف الكورسات فقط
  const handleCleanCourses = () => {
    openConfirmDialog(
      'courses',
      'تنظيف الكورسات',
      'سيتم حذف جميع الكورسات والتسجيلات المرتبطة بها. هذا الإجراء لا يمكن التراجع عنه!'
    );
  };

  // تنظيف الطلاب فقط
  const handleCleanStudents = () => {
    openConfirmDialog(
      'students',
      'تنظيف الطلاب',
      'سيتم حذف جميع الطلاب والتسجيلات المرتبطة بهم. هذا الإجراء لا يمكن التراجع عنه!'
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <DeleteSweep color="primary" />
        أداة تنظيف قاعدة البيانات
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        هذه الأداة تساعدك في إزالة جميع البيانات الوهمية من قاعدة البيانات لتصبح جاهزة للبيانات الحقيقية
      </Typography>

      {/* تحذير مهم */}
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          ⚠️ تحذير مهم
        </Typography>
        <Typography>
          عمليات التنظيف هذه لا يمكن التراجع عنها. تأكد من أنك تريد حذف البيانات قبل المتابعة.
          سيتم الاحتفاظ بحسابات المدراء فقط.
        </Typography>
      </Alert>

      {/* خيارات التنظيف */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* تنظيف شامل */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Storage sx={{ fontSize: 60, color: '#f44336', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                تنظيف شامل
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                حذف جميع البيانات الوهمية من Firebase و Supabase
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><People fontSize="small" /></ListItemIcon>
                  <ListItemText primary="جميع الطلاب" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><School fontSize="small" /></ListItemIcon>
                  <ListItemText primary="جميع الكورسات" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Assignment fontSize="small" /></ListItemIcon>
                  <ListItemText primary="جميع التسجيلات" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Help fontSize="small" /></ListItemIcon>
                  <ListItemText primary="الأسئلة الشائعة" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Notifications fontSize="small" /></ListItemIcon>
                  <ListItemText primary="التفاعلات والإشعارات" />
                </ListItem>
              </List>
              <Button
                variant="contained"
                color="error"
                fullWidth
                onClick={handleCleanAll}
                disabled={loading}
                startIcon={<DeleteSweep />}
              >
                تنظيف شامل
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* تنظيف الكورسات */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <School sx={{ fontSize: 60, color: '#ff9800', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                تنظيف الكورسات
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                حذف جميع الكورسات والتسجيلات المرتبطة بها
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><School fontSize="small" /></ListItemIcon>
                  <ListItemText primary="جميع الكورسات" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Assignment fontSize="small" /></ListItemIcon>
                  <ListItemText primary="التسجيلات المرتبطة" />
                </ListItem>
              </List>
              <Button
                variant="contained"
                color="warning"
                fullWidth
                onClick={handleCleanCourses}
                disabled={loading}
                startIcon={<School />}
              >
                تنظيف الكورسات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* تنظيف الطلاب */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <People sx={{ fontSize: 60, color: '#2196f3', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                تنظيف الطلاب
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                حذف جميع الطلاب والتسجيلات المرتبطة بهم
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><People fontSize="small" /></ListItemIcon>
                  <ListItemText primary="جميع الطلاب" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Assignment fontSize="small" /></ListItemIcon>
                  <ListItemText primary="التسجيلات المرتبطة" />
                </ListItem>
              </List>
              <Button
                variant="contained"
                color="info"
                fullWidth
                onClick={handleCleanStudents}
                disabled={loading}
                startIcon={<People />}
              >
                تنظيف الطلاب
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* نتائج التنظيف */}
      {cleaningResults && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckCircle color="success" />
              نتائج التنظيف
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {cleaningResults.deletedCounts && (
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    🔥 Firebase
                  </Typography>
                  {Object.entries(cleaningResults.deletedCounts.firebase).map(([key, count]) => (
                    count > 0 && (
                      <Chip
                        key={key}
                        label={`${key}: ${count} محذوف`}
                        color="primary"
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    )
                  ))}
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    🐘 Supabase
                  </Typography>
                  {Object.entries(cleaningResults.deletedCounts.supabase).map(([key, count]) => (
                    count > 0 && (
                      <Chip
                        key={key}
                        label={`${key}: ${count} محذوف`}
                        color="secondary"
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    )
                  ))}
                </Grid>
              </Grid>
            )}
          </CardContent>
        </Card>
      )}

      {/* حوار التأكيد */}
      <Dialog
        open={confirmDialog.open}
        onClose={closeConfirmDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Warning color="warning" />
          {confirmDialog.title}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {confirmDialog.message}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeConfirmDialog} startIcon={<Cancel />}>
            إلغاء
          </Button>
          <Button
            onClick={executeCleanup}
            color="error"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <DeleteSweep />}
          >
            {loading ? 'جاري التنظيف...' : 'تأكيد التنظيف'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* مؤشر التحميل */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default DatabaseCleanerTool;
