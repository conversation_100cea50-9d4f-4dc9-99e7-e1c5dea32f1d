const express = require('express');
const { body, validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');

const router = express.Router();

// Middleware للتحقق من صلاحيات المدير
const adminAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد توكن، الوصول مرفوض' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'alaa_courses_secret_key');
    const userDoc = await req.db.collection('users').doc(decoded.id).get();
    
    if (!userDoc.exists) {
      return res.status(401).json({ message: 'التوكن غير صالح' });
    }

    const user = { id: userDoc.id, ...userDoc.data() };
    
    if (user.role !== 'admin') {
      return res.status(403).json({ message: 'ليس لديك صلاحية للوصول' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: 'التوكن غير صالح' });
  }
};

// جميع المسارات تتطلب صلاحيات المدير
router.use(adminAuth);

// إحصائيات لوحة التحكم
router.get('/dashboard/stats', async (req, res) => {
  try {
    const db = req.db;

    // حساب الإحصائيات
    const [studentsSnapshot, coursesSnapshot, categoriesSnapshot, certificatesSnapshot] = await Promise.all([
      db.collection('users').where('role', '==', 'student').get(),
      db.collection('courses').get(),
      db.collection('categories').get(),
      db.collection('certificates').get()
    ]);

    const totalStudents = studentsSnapshot.size;
    const totalCourses = coursesSnapshot.size;
    const totalCategories = categoriesSnapshot.size;
    const totalCertificates = certificatesSnapshot.size;

    // حساب الطلاب النشطين (آخر 30 يوم)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activeStudentsSnapshot = await db.collection('users')
      .where('role', '==', 'student')
      .where('lastLogin', '>=', thirtyDaysAgo)
      .get();

    const activeStudents = activeStudentsSnapshot.size;

    // حساب الكورسات المنشورة
    const publishedCoursesSnapshot = await db.collection('courses')
      .where('isPublished', '==', true)
      .get();

    const publishedCourses = publishedCoursesSnapshot.size;
    
    res.json({
      totalStudents,
      totalCourses,
      totalCategories,
      totalCertificates,
      activeStudents,
      publishedCourses
    });
  } catch (error) {
    console.error('خطأ في الحصول على الإحصائيات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إنشاء طالب جديد
router.post('/students', [
  body('name').notEmpty().withMessage('اسم الطالب مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name } = req.body;
    const db = req.db;
    
    // إنشاء كود للطالب
    let studentCode;
    let isUnique = false;
    
    while (!isUnique) {
      studentCode = Math.floor(100000 + Math.random() * 900000).toString();
      const existingStudent = await db.collection('users')
        .where('studentCode', '==', studentCode)
        .limit(1)
        .get();
      
      if (existingStudent.empty) {
        isUnique = true;
      }
    }
    
    const studentRef = db.collection('users').doc();
    await studentRef.set({
      name,
      role: 'student',
      studentCode,
      isActive: true,
      createdBy: req.user.id,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    res.status(201).json({
      message: 'تم إنشاء الطالب بنجاح',
      student: {
        id: studentRef.id,
        name,
        studentCode,
        createdAt: new Date()
      }
    });
  } catch (error) {
    console.error('خطأ في إنشاء الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على جميع الطلاب
router.get('/students', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const db = req.db;

    const studentsSnapshot = await db.collection('users')
      .where('role', '==', 'student')
      .orderBy('createdAt', 'desc')
      .get();

    const students = [];
    studentsSnapshot.forEach(doc => {
      const data = doc.data();
      students.push({
        id: doc.id,
        name: data.name,
        studentCode: data.studentCode,
        isActive: data.isActive,
        lastLogin: data.lastLogin,
        createdAt: data.createdAt
      });
    });

    const total = students.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedStudents = students.slice(startIndex, endIndex);

    res.json({
      students: paginatedStudents,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('خطأ في الحصول على الطلاب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إنشاء قسم جديد
router.post('/categories', [
  body('name').notEmpty().withMessage('اسم القسم مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name, description, icon, color, order } = req.body;
    const db = req.db;

    const categoryRef = db.collection('categories').doc();
    await categoryRef.set({
      name,
      description: description || '',
      icon: icon || 'category',
      color: color || '#2196F3',
      order: order || 0,
      isActive: true,
      createdBy: req.user.id,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    res.status(201).json({
      message: 'تم إنشاء القسم بنجاح',
      category: {
        id: categoryRef.id,
        name,
        description,
        icon,
        color,
        order
      }
    });
  } catch (error) {
    console.error('خطأ في إنشاء القسم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على جميع الأقسام
router.get('/categories', async (req, res) => {
  try {
    const db = req.db;
    
    const categoriesSnapshot = await db.collection('categories')
      .orderBy('order', 'asc')
      .get();

    const categories = [];
    categoriesSnapshot.forEach(doc => {
      categories.push({
        id: doc.id,
        ...doc.data()
      });
    });

    res.json({ categories });
  } catch (error) {
    console.error('خطأ في الحصول على الأقسام:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث قسم
router.put('/categories/:id', [
  body('name').optional().notEmpty().withMessage('اسم القسم لا يمكن أن يكون فارغاً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const db = req.db;
    const categoryRef = db.collection('categories').doc(req.params.id);
    const categoryDoc = await categoryRef.get();

    if (!categoryDoc.exists) {
      return res.status(404).json({ message: 'القسم غير موجود' });
    }

    const updateData = {
      ...req.body,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await categoryRef.update(updateData);

    res.json({
      message: 'تم تحديث القسم بنجاح',
      category: {
        id: req.params.id,
        ...categoryDoc.data(),
        ...req.body
      }
    });
  } catch (error) {
    console.error('خطأ في تحديث القسم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// حذف قسم
router.delete('/categories/:id', async (req, res) => {
  try {
    const db = req.db;
    const categoryRef = db.collection('categories').doc(req.params.id);
    const categoryDoc = await categoryRef.get();

    if (!categoryDoc.exists) {
      return res.status(404).json({ message: 'القسم غير موجود' });
    }

    // التحقق من وجود كورسات في هذا القسم
    const coursesSnapshot = await db.collection('courses')
      .where('category', '==', req.params.id)
      .limit(1)
      .get();

    if (!coursesSnapshot.empty) {
      return res.status(400).json({ 
        message: 'لا يمكن حذف القسم لأنه يحتوي على كورسات' 
      });
    }

    await categoryRef.delete();

    res.json({ message: 'تم حذف القسم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف القسم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// ===== إدارة الكورسات =====

// جلب جميع الكورسات
router.get('/courses', async (req, res) => {
  try {
    const db = req.db;
    const coursesSnapshot = await db.collection('courses').orderBy('createdAt', 'desc').get();

    const courses = coursesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
    }));

    res.json(courses);
  } catch (error) {
    console.error('خطأ في جلب الكورسات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// جلب كورس محدد
router.get('/courses/:id', async (req, res) => {
  try {
    const db = req.db;
    const courseDoc = await db.collection('courses').doc(req.params.id).get();

    if (!courseDoc.exists) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    const course = {
      id: courseDoc.id,
      ...courseDoc.data(),
      createdAt: courseDoc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: courseDoc.data().updatedAt?.toDate?.() || new Date()
    };

    res.json(course);
  } catch (error) {
    console.error('خطأ في جلب الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إنشاء كورس جديد
router.post('/courses', [
  body('title').notEmpty().withMessage('عنوان الكورس مطلوب'),
  body('description').notEmpty().withMessage('وصف الكورس مطلوب'),
  body('category').notEmpty().withMessage('قسم الكورس مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const db = req.db;
    const courseData = {
      ...req.body,
      isPublished: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    const courseRef = await db.collection('courses').add(courseData);

    res.status(201).json({
      id: courseRef.id,
      message: 'تم إنشاء الكورس بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إنشاء الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث كورس
router.put('/courses/:id', async (req, res) => {
  try {
    const db = req.db;
    const courseRef = db.collection('courses').doc(req.params.id);
    const courseDoc = await courseRef.get();

    if (!courseDoc.exists) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    const updateData = {
      ...req.body,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await courseRef.update(updateData);

    res.json({ message: 'تم تحديث الكورس بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تغيير حالة النشر للكورس
router.patch('/courses/:id/status', async (req, res) => {
  try {
    const db = req.db;
    const { isPublished } = req.body;
    const courseRef = db.collection('courses').doc(req.params.id);
    const courseDoc = await courseRef.get();

    if (!courseDoc.exists) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    await courseRef.update({
      isPublished: Boolean(isPublished),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    res.json({
      message: isPublished ? 'تم نشر الكورس بنجاح' : 'تم إلغاء نشر الكورس بنجاح'
    });
  } catch (error) {
    console.error('خطأ في تغيير حالة الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// حذف كورس
router.delete('/courses/:id', async (req, res) => {
  try {
    const db = req.db;
    const courseRef = db.collection('courses').doc(req.params.id);
    const courseDoc = await courseRef.get();

    if (!courseDoc.exists) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    await courseRef.delete();

    res.json({ message: 'تم حذف الكورس بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// ===== إدارة الطلاب =====

// جلب جميع الطلاب
router.get('/students', async (req, res) => {
  try {
    const db = req.db;
    const studentsSnapshot = await db.collection('users')
      .where('role', '==', 'student')
      .orderBy('createdAt', 'desc')
      .get();

    const students = studentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
      lastLogin: doc.data().lastLogin?.toDate?.() || null
    }));

    res.json(students);
  } catch (error) {
    console.error('خطأ في جلب الطلاب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// جلب طالب محدد
router.get('/students/:id', async (req, res) => {
  try {
    const db = req.db;
    const studentDoc = await db.collection('users').doc(req.params.id).get();

    if (!studentDoc.exists) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    const student = {
      id: studentDoc.id,
      ...studentDoc.data(),
      createdAt: studentDoc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: studentDoc.data().updatedAt?.toDate?.() || new Date(),
      lastLogin: studentDoc.data().lastLogin?.toDate?.() || null
    };

    res.json(student);
  } catch (error) {
    console.error('خطأ في جلب الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث بيانات طالب
router.put('/students/:id', async (req, res) => {
  try {
    const db = req.db;
    const studentRef = db.collection('users').doc(req.params.id);
    const studentDoc = await studentRef.get();

    if (!studentDoc.exists) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    const updateData = {
      ...req.body,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await studentRef.update(updateData);

    res.json({ message: 'تم تحديث بيانات الطالب بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تفعيل/إلغاء تفعيل طالب
router.patch('/students/:id/status', async (req, res) => {
  try {
    const db = req.db;
    const { isActive } = req.body;
    const studentRef = db.collection('users').doc(req.params.id);
    const studentDoc = await studentRef.get();

    if (!studentDoc.exists) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    await studentRef.update({
      isActive: Boolean(isActive),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    res.json({
      message: isActive ? 'تم تفعيل الطالب بنجاح' : 'تم إلغاء تفعيل الطالب بنجاح'
    });
  } catch (error) {
    console.error('خطأ في تغيير حالة الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// حذف طالب
router.delete('/students/:id', async (req, res) => {
  try {
    const db = req.db;
    const studentRef = db.collection('users').doc(req.params.id);
    const studentDoc = await studentRef.get();

    if (!studentDoc.exists) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    await studentRef.delete();

    res.json({ message: 'تم حذف الطالب بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
