{"name": "alaa-courses-functions", "version": "1.0.0", "description": "Firebase Functions for Alaa Courses Platform", "main": "index.js", "scripts": {"build": "echo 'Build complete'", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "firebase-admin": "^11.10.1", "firebase-functions": "^4.4.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "pdfkit": "^0.13.0", "twilio": "^4.19.0", "uuid": "^9.0.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}