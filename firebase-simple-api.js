// Simple API for Firebase hosting without Functions
// This will be served as static files

const API_BASE = 'https://marketwise-academy-qhizq.web.app';

// Mock data for demonstration
const mockData = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123!',
    name: 'ع<PERSON><PERSON><PERSON> عبد الحميد',
    role: 'admin'
  },
  students: [
    {
      studentCode: '123456',
      name: '<PERSON>حم<PERSON> محمد علي',
      isActive: true,
      enrolledCourses: [],
      progress: {}
    },
    {
      studentCode: '789012',
      name: 'فاطمة علي حسن',
      isActive: true,
      enrolledCourses: [],
      progress: {}
    }
  ],
  courses: [
    {
      id: 'course1',
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي من الصفر',
      instructor: 'علاء عبد الحميد',
      price: 299,
      originalPrice: 499,
      discount: 40,
      isActive: true,
      level: 'مبتدئ',
      language: 'العربية',
      duration: '8 ساعات',
      totalVideos: 24,
      category: 'التسويق الرقمي',
      thumbnail: '/images/course1.jpg',
      rating: 4.8,
      reviewsCount: 156
    }
  ],
  categories: [
    {
      id: 'cat1',
      name: 'التسويق الرقمي',
      description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
      icon: 'digital-marketing',
      color: '#2196F3',
      isActive: true
    },
    {
      id: 'cat2',
      name: 'وسائل التواصل الاجتماعي',
      description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
      icon: 'social-media',
      color: '#4CAF50',
      isActive: true
    }
  ]
};

// API endpoints simulation
const API = {
  // Health check
  health: () => ({
    status: 'success',
    message: 'الخادم يعمل بنجاح',
    timestamp: new Date().toISOString(),
    environment: 'firebase-hosting'
  }),

  // Test endpoint
  test: () => ({
    message: 'اختبار API ناجح',
    server: 'منصة كورسات علاء عبد الحميد',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  }),

  // Auth endpoints
  auth: {
    adminLogin: (email, password) => {
      if (email === mockData.admin.email && password === mockData.admin.password) {
        return {
          success: true,
          user: {
            id: 'admin1',
            name: mockData.admin.name,
            email: mockData.admin.email,
            role: mockData.admin.role
          },
          token: 'mock-jwt-token-' + Date.now()
        };
      }
      return { success: false, message: 'بيانات دخول خاطئة' };
    },

    studentLogin: (studentCode) => {
      const student = mockData.students.find(s => s.studentCode === studentCode);
      if (student) {
        return {
          success: true,
          user: {
            id: 'student' + studentCode,
            name: student.name,
            studentCode: student.studentCode,
            role: 'student'
          },
          token: 'mock-jwt-token-' + Date.now()
        };
      }
      return { success: false, message: 'كود الطالب غير صحيح' };
    }
  },

  // Admin endpoints
  admin: {
    dashboard: () => ({
      totalStudents: mockData.students.length,
      totalCourses: mockData.courses.length,
      totalCategories: mockData.categories.length,
      activeStudents: mockData.students.filter(s => s.isActive).length,
      recentActivity: [
        { type: 'student_login', message: 'دخول الطالب أحمد محمد', time: '2024-01-15 10:30' },
        { type: 'course_complete', message: 'إكمال كورس التسويق الرقمي', time: '2024-01-15 09:15' }
      ]
    }),

    students: () => mockData.students,
    courses: () => mockData.courses,
    categories: () => mockData.categories
  },

  // Student endpoints
  student: {
    courses: () => mockData.courses.filter(c => c.isActive),
    progress: (studentId) => ({
      enrolledCourses: [],
      completedCourses: [],
      totalWatchTime: 0,
      certificates: []
    })
  }
};

// Export for use in frontend
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { API, mockData };
}

// Global API for browser
if (typeof window !== 'undefined') {
  window.AlaaCoursesAPI = API;
  window.mockData = mockData;
  
  console.log('🎓 منصة كورسات علاء عبد الحميد');
  console.log('📱 الموقع منشور على Firebase!');
  console.log('🔗 الرابط:', window.location.origin);
  console.log('👨‍💼 بيانات المدير: <EMAIL> / Admin123!');
  console.log('👨‍🎓 أكواد الطلاب: 123456, 789012');
}
