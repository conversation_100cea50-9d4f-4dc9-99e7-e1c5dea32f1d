import {
  collection,
  doc,
  setDoc,
  addDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';

// إنشاء بيانات أولية للمدير
export const seedAdminData = async () => {
  try {
    console.log('بدء إنشاء البيانات الأولية...');

    // إنشاء بيانات الطلاب
    await createStudentsData();

    // إنشاء بيانات الكورسات
    await createCoursesData();

    // إنشاء بيانات الأسئلة الشائعة
    await createFAQsData();

    // إنشاء بيانات الشهادات
    await createCertificatesData();

    // إنشاء بيانات المحادثات
    await createConversationsData();

    // إنشاء بيانات النشاطات
    await createActivitiesData();

    // إنشاء ملف شخصي للمدير
    await createAdminProfile();

    console.log('تم إنشاء جميع البيانات الأولية بنجاح!');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات الأولية:', error);
    throw error;
  }
};

// إنشاء بيانات الطلاب
const createStudentsData = async () => {
  const studentsData = [
    {
      name: 'أحمد محمد علي',
      email: '<EMAIL>',
      phone: '0501234567',
      studentCode: '123456',
      isActive: true,
      enrolledCourses: [
        {
          courseId: 'course1',
          courseName: 'أساسيات التسويق الرقمي',
          progress: 75,
          enrolledAt: new Date('2024-01-15')
        }
      ],
      completedCourses: [],
      certificates: [],
      totalWatchTime: 1200, // بالدقائق
      createdAt: serverTimestamp(),
      lastLogin: new Date('2024-01-20')
    },
    {
      name: 'فاطمة أحمد حسن',
      email: '<EMAIL>',
      phone: '0507654321',
      studentCode: '789012',
      isActive: true,
      enrolledCourses: [
        {
          courseId: 'course2',
          courseName: 'إدارة وسائل التواصل الاجتماعي',
          progress: 90,
          enrolledAt: new Date('2024-01-10')
        }
      ],
      completedCourses: [
        {
          courseId: 'course1',
          courseName: 'أساسيات التسويق الرقمي',
          completedAt: new Date('2024-01-18')
        }
      ],
      certificates: ['cert1'],
      totalWatchTime: 1800,
      createdAt: serverTimestamp(),
      lastLogin: new Date('2024-01-19')
    },
    {
      name: 'محمد علي حسين',
      email: '<EMAIL>',
      phone: '0509876543',
      studentCode: '345678',
      isActive: true,
      enrolledCourses: [
        {
          courseId: 'course3',
          courseName: 'إدارة المشاريع الرقمية',
          progress: 45,
          enrolledAt: new Date('2024-01-12')
        }
      ],
      completedCourses: [],
      certificates: [],
      totalWatchTime: 900,
      createdAt: serverTimestamp(),
      lastLogin: new Date('2024-01-21')
    },
    {
      name: 'سارة خالد محمد',
      email: '<EMAIL>',
      phone: '0502468135',
      studentCode: '901234',
      isActive: false,
      enrolledCourses: [],
      completedCourses: [],
      certificates: [],
      totalWatchTime: 0,
      createdAt: serverTimestamp(),
      lastLogin: null
    }
  ];

  const batch = writeBatch(db);

  studentsData.forEach((student, index) => {
    // إنشاء الطالب في مجموعة students
    const studentRef = doc(collection(db, 'students'));
    batch.set(studentRef, student);

    // إنشاء ملف شخصي في مجموعة profiles
    const profileRef = doc(collection(db, 'profiles'));
    batch.set(profileRef, {
      name: student.name,
      email: student.email,
      phone: student.phone,
      studentCode: student.studentCode,
      role: 'student',
      isActive: student.isActive,
      createdAt: student.createdAt,
      lastLogin: student.lastLogin,
      studentId: studentRef.id
    });
  });

  await batch.commit();
  console.log('تم إنشاء بيانات الطلاب والملفات الشخصية');
};

// إنشاء بيانات الكورسات
const createCoursesData = async () => {
  const coursesData = [
    {
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي والاستراتيجيات الحديثة',
      category: 'تسويق',
      level: 'مبتدئ',
      duration: 120, // بالدقائق
      price: 299,
      isActive: true,
      enrolledStudents: 2,
      completedStudents: 1,
      totalViews: 156,
      averageRating: 4.5,
      ratings: [
        { studentId: 'student1', rating: 5, comment: 'كورس ممتاز' },
        { studentId: 'student2', rating: 4, comment: 'مفيد جداً' }
      ],
      lessons: [
        {
          id: 'lesson1',
          title: 'مقدمة في التسويق الرقمي',
          duration: 30,
          videoUrl: 'https://example.com/video1'
        },
        {
          id: 'lesson2',
          title: 'استراتيجيات التسويق',
          duration: 45,
          videoUrl: 'https://example.com/video2'
        }
      ],
      createdAt: serverTimestamp()
    },
    {
      title: 'إدارة وسائل التواصل الاجتماعي',
      description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية',
      category: 'تسويق',
      level: 'متوسط',
      duration: 180,
      price: 399,
      isActive: true,
      enrolledStudents: 1,
      completedStudents: 0,
      totalViews: 89,
      averageRating: 4.8,
      ratings: [
        { studentId: 'student2', rating: 5, comment: 'محتوى رائع' }
      ],
      lessons: [
        {
          id: 'lesson1',
          title: 'أساسيات وسائل التواصل',
          duration: 40,
          videoUrl: 'https://example.com/video3'
        }
      ],
      createdAt: serverTimestamp()
    },
    {
      title: 'إدارة المشاريع الرقمية',
      description: 'تعلم أساسيات إدارة المشاريع الرقمية والأدوات المستخدمة',
      category: 'إدارة',
      level: 'متقدم',
      duration: 240,
      price: 499,
      isActive: true,
      enrolledStudents: 1,
      completedStudents: 0,
      totalViews: 45,
      averageRating: 0,
      ratings: [],
      lessons: [
        {
          id: 'lesson1',
          title: 'مقدمة في إدارة المشاريع',
          duration: 50,
          videoUrl: 'https://example.com/video4'
        }
      ],
      createdAt: serverTimestamp()
    }
  ];

  const batch = writeBatch(db);

  coursesData.forEach((course) => {
    const courseRef = doc(collection(db, 'courses'));
    batch.set(courseRef, course);
  });

  await batch.commit();
  console.log('تم إنشاء بيانات الكورسات');
};

// إنشاء بيانات الأسئلة الشائعة
const createFAQsData = async () => {
  const faqsData = [
    {
      question: 'كيف يمكنني التسجيل في المنصة؟',
      answer: 'يمكنك التسجيل من خلال الحصول على كود التسجيل من المدير ثم إدخاله في صفحة تسجيل الدخول.',
      category: 'التسجيل والحساب',
      priority: 1,
      isActive: true,
      createdAt: serverTimestamp()
    },
    {
      question: 'كيف أحصل على شهادة إتمام الكورس؟',
      answer: 'بعد إكمال جميع فيديوهات الكورس بنسبة 100%، ستحصل تلقائياً على شهادة إتمام.',
      category: 'الشهادات',
      priority: 2,
      isActive: true,
      createdAt: serverTimestamp()
    },
    {
      question: 'هل يمكنني مشاهدة الكورسات على الهاتف؟',
      answer: 'نعم، المنصة متوافقة مع جميع الأجهزة ويمكنك مشاهدة الكورسات على الهاتف والتابلت.',
      category: 'الدعم التقني',
      priority: 3,
      isActive: true,
      createdAt: serverTimestamp()
    },
    {
      question: 'ماذا أفعل إذا نسيت كود الطالب؟',
      answer: 'يمكنك التواصل مع المدير لاستعادة كود الطالب الخاص بك.',
      category: 'التسجيل والحساب',
      priority: 4,
      isActive: true,
      createdAt: serverTimestamp()
    },
    {
      question: 'هل هناك مدة صلاحية للكورسات؟',
      answer: 'لا، يمكنك الوصول للكورسات المسجل بها مدى الحياة.',
      category: 'الكورسات والمحتوى',
      priority: 5,
      isActive: true,
      createdAt: serverTimestamp()
    }
  ];

  const batch = writeBatch(db);

  faqsData.forEach((faq) => {
    const faqRef = doc(collection(db, 'faqs'));
    batch.set(faqRef, faq);
  });

  await batch.commit();
  console.log('تم إنشاء بيانات الأسئلة الشائعة');
};

// إنشاء بيانات الشهادات
const createCertificatesData = async () => {
  const certificatesData = [
    {
      studentId: 'student2',
      studentName: 'فاطمة أحمد حسن',
      courseId: 'course1',
      courseName: 'أساسيات التسويق الرقمي',
      certificateNumber: 'CERT-20240118001',
      issuedAt: serverTimestamp(),
      isValid: true,
      completionDate: new Date('2024-01-18'),
      grade: 'ممتاز',
      score: 95
    },
    {
      studentId: 'student1',
      studentName: 'أحمد محمد علي',
      courseId: 'course2',
      courseName: 'إدارة وسائل التواصل الاجتماعي',
      certificateNumber: 'CERT-20240120001',
      issuedAt: serverTimestamp(),
      isValid: true,
      completionDate: new Date('2024-01-20'),
      grade: 'جيد جداً',
      score: 88
    }
  ];

  const batch = writeBatch(db);

  certificatesData.forEach((certificate) => {
    const certRef = doc(collection(db, 'certificates'));
    batch.set(certRef, certificate);
  });

  await batch.commit();
  console.log('تم إنشاء بيانات الشهادات');
};

// إنشاء بيانات المحادثات
const createConversationsData = async () => {
  const conversationsData = [
    {
      studentId: 'student1',
      studentName: 'أحمد محمد علي',
      lastMessage: 'شكراً لك على المساعدة',
      lastMessageTime: serverTimestamp(),
      lastSenderId: 'student1',
      unreadCount: 0,
      isStarred: false,
      isArchived: false
    },
    {
      studentId: 'student2',
      studentName: 'فاطمة أحمد حسن',
      lastMessage: 'متى سيتم إضافة الكورس الجديد؟',
      lastMessageTime: serverTimestamp(),
      lastSenderId: 'student2',
      unreadCount: 1,
      isStarred: true,
      isArchived: false
    },
    {
      studentId: 'student3',
      studentName: 'محمد علي حسين',
      lastMessage: 'لا أستطيع تشغيل الفيديو',
      lastMessageTime: serverTimestamp(),
      lastSenderId: 'student3',
      unreadCount: 2,
      isStarred: false,
      isArchived: false
    }
  ];

  const batch = writeBatch(db);

  conversationsData.forEach((conversation) => {
    const convRef = doc(collection(db, 'conversations'));
    batch.set(convRef, conversation);
  });

  await batch.commit();
  console.log('تم إنشاء بيانات المحادثات');
};

// إنشاء بيانات النشاطات
const createActivitiesData = async () => {
  const activitiesData = [
    {
      student: 'أحمد محمد علي',
      action: 'أكمل كورس التسويق الرقمي',
      type: 'completion',
      timestamp: serverTimestamp()
    },
    {
      student: 'فاطمة أحمد حسن',
      action: 'بدأت كورس إدارة المشاريع',
      type: 'enrollment',
      timestamp: serverTimestamp()
    },
    {
      student: 'محمد علي حسين',
      action: 'حصل على شهادة إتمام',
      type: 'certificate',
      timestamp: serverTimestamp()
    },
    {
      student: 'سارة خالد محمد',
      action: 'انضمت للمنصة',
      type: 'registration',
      timestamp: serverTimestamp()
    },
    {
      student: 'أحمد محمد علي',
      action: 'بدأ مشاهدة درس جديد',
      type: 'lesson_start',
      timestamp: serverTimestamp()
    }
  ];

  const batch = writeBatch(db);

  activitiesData.forEach((activity) => {
    const activityRef = doc(collection(db, 'activities'));
    batch.set(activityRef, activity);
  });

  await batch.commit();
  console.log('تم إنشاء بيانات النشاطات');
};

// إنشاء ملف شخصي للمدير
const createAdminProfile = async () => {
  try {
    // التحقق من وجود ملف المدير
    const profilesRef = collection(db, 'profiles');
    const adminQuery = query(profilesRef, where('role', '==', 'admin'));
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      // إنشاء ملف المدير
      const adminProfileRef = doc(collection(db, 'profiles'));
      await setDoc(adminProfileRef, {
        name: 'علاء عبد الحميد',
        email: 'ALAA <EMAIL>',
        phone: '0506747770',
        role: 'admin',
        isActive: true,
        createdAt: serverTimestamp(),
        lastLogin: null,
        title: 'مدير أكاديمية SKILLS WORLD',
        bio: 'مدير ومؤسس أكاديمية SKILLS WORLD للتدريب والتطوير'
      });

      console.log('تم إنشاء ملف المدير');
    } else {
      console.log('ملف المدير موجود بالفعل');
    }
  } catch (error) {
    console.error('خطأ في إنشاء ملف المدير:', error);
  }
};