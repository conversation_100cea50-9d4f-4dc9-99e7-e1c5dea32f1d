# 🐘 دليل إعداد Supabase للتكامل مع Firebase

## الخطوة 1: إنشاء مشروع Supabase

1. **زيارة Supabase**
   - اذهب إلى: https://supabase.com
   - سجل دخول أو أنشئ حساب جديد

2. **إنشاء مشروع جديد**
   - اضغط "New Project"
   - اسم المشروع: `skills-world-academy`
   - كلمة مرور قاعدة البيانات: (اختر كلمة مرور قوية)
   - المنطقة: اختر الأقرب لك

## الخطوة 2: إعداد قاعدة البيانات

### 1. تنفيذ SQL Schema
انسخ والصق المحتوى من `database/supabase-schema.sql` في SQL Editor:

```sql
-- انسخ محتوى ملف database/supabase-schema.sql هنا
```

### 2. تنفيذ SQL Functions
انسخ والصق المحتوى من `database/supabase-functions.sql`:

```sql
-- انسخ محتوى ملف database/supabase-functions.sql هنا
```

## الخطوة 3: الحصول على مفاتيح API

1. **في Supabase Dashboard**
   - اذهب إلى Settings > API
   - انسخ:
     - Project URL
     - anon public key

## الخطوة 4: تحديث متغيرات البيئة

أضف المتغيرات التالية في ملف `.env`:

```env
# Supabase Configuration
REACT_APP_SUPABASE_URL=https://your-project-id.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your-anon-key-here
```

## الخطوة 5: إعادة النشر

```bash
# بناء المشروع مع المتغيرات الجديدة
npm run build

# النشر
firebase deploy --only hosting
```

## الخطوة 6: اختبار التكامل

1. **زيارة الموقع**: https://marketwise-academy-qhizq.web.app
2. **تسجيل دخول المدير**
3. **الذهاب إلى**: لوحة التحكم > اختبار النظام
4. **تشغيل الاختبارات** للتأكد من عمل التكامل

## 🔧 إعدادات إضافية

### تفعيل Real-time
في Supabase Dashboard:
1. اذهب إلى Database > Replication
2. فعّل Real-time للجداول المطلوبة

### إعداد Row Level Security
تم تضمينها في schema.sql، تأكد من تفعيلها.

## 📞 الدعم

إذا واجهت أي مشاكل:
- **المطور**: علاء عبد الحميد
- **البريد**: ALAA <EMAIL>
- **الهاتف**: 0506747770

---

**بعد إكمال هذه الخطوات، ستحصل على نظام متكامل بالكامل مع Firebase + Supabase!**
