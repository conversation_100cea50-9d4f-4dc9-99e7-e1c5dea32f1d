/**
 * اختبار الاتصال البسيط - SKILLS WORLD ACADEMY
 * Simple Connection Test for Firebase + Supabase
 */

import { supabase } from '../supabase/config';
import { auth } from '../firebase/config';

/**
 * اختبار اتصال Supabase
 */
export const testSupabaseConnection = async () => {
  try {
    console.log('🔄 اختبار اتصال Supabase...');
    
    // اختبار بسيط للاتصال
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ خطأ في اتصال Supabase:', error.message);
      return {
        success: false,
        error: error.message,
        details: 'فشل في الاتصال مع قاعدة بيانات Supabase'
      };
    }

    console.log('✅ اتصال Supabase ناجح');
    console.log('📊 البيانات المستلمة:', data);
    
    return {
      success: true,
      data: data,
      message: 'تم الاتصال بنجاح مع Supabase'
    };

  } catch (error) {
    console.error('❌ خطأ في اختبار Supabase:', error);
    return {
      success: false,
      error: error.message,
      details: 'خطأ في شبكة الاتصال أو إعدادات Supabase'
    };
  }
};

/**
 * اختبار اتصال Firebase
 */
export const testFirebaseConnection = async () => {
  try {
    console.log('🔄 اختبار اتصال Firebase...');
    
    // التحقق من حالة المصادقة
    const currentUser = auth.currentUser;
    
    return {
      success: true,
      user: currentUser,
      message: currentUser ? 'مستخدم مسجل دخول' : 'لا يوجد مستخدم مسجل دخول'
    };

  } catch (error) {
    console.error('❌ خطأ في اختبار Firebase:', error);
    return {
      success: false,
      error: error.message,
      details: 'خطأ في اتصال Firebase'
    };
  }
};

/**
 * اختبار شامل للاتصالات
 */
export const runConnectionTests = async () => {
  console.log('🚀 بدء اختبار الاتصالات الشامل...');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: []
  };

  // اختبار Supabase
  const supabaseTest = await testSupabaseConnection();
  results.tests.push({
    name: 'Supabase Connection',
    ...supabaseTest
  });

  // اختبار Firebase
  const firebaseTest = await testFirebaseConnection();
  results.tests.push({
    name: 'Firebase Connection',
    ...firebaseTest
  });

  // حساب النتيجة الإجمالية
  const successfulTests = results.tests.filter(test => test.success).length;
  const totalTests = results.tests.length;
  const successRate = (successfulTests / totalTests) * 100;

  results.summary = {
    total: totalTests,
    successful: successfulTests,
    failed: totalTests - successfulTests,
    successRate: successRate.toFixed(1) + '%'
  };

  console.log('📊 نتائج اختبار الاتصالات:', results.summary);
  
  return results;
};

/**
 * اختبار بيانات محددة من Supabase
 */
export const testSupabaseData = async () => {
  try {
    console.log('🔄 اختبار بيانات Supabase...');
    
    const tests = [];

    // اختبار جدول الإعدادات
    const { data: settings, error: settingsError } = await supabase
      .from('settings')
      .select('*')
      .limit(5);

    tests.push({
      table: 'settings',
      success: !settingsError,
      count: settings?.length || 0,
      error: settingsError?.message
    });

    // اختبار جدول الكورسات
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('*')
      .limit(5);

    tests.push({
      table: 'courses',
      success: !coursesError,
      count: courses?.length || 0,
      error: coursesError?.message
    });

    // اختبار جدول المستخدمين
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(5);

    tests.push({
      table: 'users',
      success: !usersError,
      count: users?.length || 0,
      error: usersError?.message
    });

    // اختبار جدول الأسئلة الشائعة
    const { data: faqs, error: faqsError } = await supabase
      .from('faqs')
      .select('*')
      .limit(5);

    tests.push({
      table: 'faqs',
      success: !faqsError,
      count: faqs?.length || 0,
      error: faqsError?.message
    });

    console.log('📊 نتائج اختبار البيانات:', tests);
    
    return {
      success: true,
      tests: tests,
      summary: {
        totalTables: tests.length,
        successfulTables: tests.filter(t => t.success).length,
        totalRecords: tests.reduce((sum, t) => sum + t.count, 0)
      }
    };

  } catch (error) {
    console.error('❌ خطأ في اختبار البيانات:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * إصلاح مشاكل الاتصال الشائعة
 */
export const fixCommonIssues = async () => {
  console.log('🔧 محاولة إصلاح مشاكل الاتصال...');
  
  const fixes = [];

  try {
    // إعادة تهيئة اتصال Supabase
    const connectionTest = await testSupabaseConnection();
    if (connectionTest.success) {
      fixes.push('✅ اتصال Supabase يعمل بشكل صحيح');
    } else {
      fixes.push('❌ مشكلة في اتصال Supabase: ' + connectionTest.error);
    }

    // التحقق من متغيرات البيئة
    const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
    const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || supabaseUrl.includes('your-project')) {
      fixes.push('⚠️ متغير REACT_APP_SUPABASE_URL غير صحيح');
    } else {
      fixes.push('✅ متغير REACT_APP_SUPABASE_URL صحيح');
    }

    if (!supabaseKey || supabaseKey.includes('your-anon-key')) {
      fixes.push('⚠️ متغير REACT_APP_SUPABASE_ANON_KEY غير صحيح');
    } else {
      fixes.push('✅ متغير REACT_APP_SUPABASE_ANON_KEY صحيح');
    }

  } catch (error) {
    fixes.push('❌ خطأ في عملية الإصلاح: ' + error.message);
  }

  console.log('🔧 نتائج الإصلاح:', fixes);
  return fixes;
};

// تشغيل اختبار سريع عند تحميل الملف
if (typeof window !== 'undefined') {
  // تشغيل اختبار بسيط بعد 2 ثانية من تحميل الصفحة
  setTimeout(() => {
    testSupabaseConnection().then(result => {
      if (result.success) {
        console.log('🎉 اتصال Supabase جاهز!');
      } else {
        console.warn('⚠️ مشكلة في اتصال Supabase:', result.error);
      }
    });
  }, 2000);
}

export default {
  testSupabaseConnection,
  testFirebaseConnection,
  runConnectionTests,
  testSupabaseData,
  fixCommonIssues
};
