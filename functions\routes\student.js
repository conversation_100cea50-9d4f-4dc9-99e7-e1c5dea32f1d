const express = require('express');
const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');

const router = express.Router();

// Middleware للتحقق من صلاحيات الطالب
const studentAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد توكن، الوصول مرفوض' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'alaa_courses_secret_key');
    const userDoc = await req.db.collection('users').doc(decoded.id).get();
    
    if (!userDoc.exists) {
      return res.status(401).json({ message: 'التوكن غير صالح' });
    }

    const user = { id: userDoc.id, ...userDoc.data() };
    
    if (user.role !== 'student') {
      return res.status(403).json({ message: 'ليس لديك صلاحية للوصول' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: 'التوكن غير صالح' });
  }
};

// جميع المسارات تتطلب صلاحيات الطالب
router.use(studentAuth);

// الحصول على الكورسات المتاحة
router.get('/courses', async (req, res) => {
  try {
    const { category, search, page = 1, limit = 12 } = req.query;
    const db = req.db;

    let query = db.collection('courses').where('isPublished', '==', true);
    
    if (category) {
      query = query.where('category', '==', category);
    }

    const coursesSnapshot = await query.orderBy('createdAt', 'desc').get();
    
    let courses = [];
    coursesSnapshot.forEach(doc => {
      const data = doc.data();
      if (!search || data.title.toLowerCase().includes(search.toLowerCase()) || 
          data.description.toLowerCase().includes(search.toLowerCase())) {
        courses.push({
          id: doc.id,
          ...data
        });
      }
    });

    // إضافة معلومات التسجيل والتقدم للطالب
    const coursesWithProgress = await Promise.all(courses.map(async (course) => {
      const progressSnapshot = await db.collection('progress')
        .where('student', '==', req.user.id)
        .where('course', '==', course.id)
        .limit(1)
        .get();

      const progress = progressSnapshot.empty ? null : progressSnapshot.docs[0].data();

      return {
        ...course,
        isEnrolled: !!progress,
        progress: progress ? progress.overallProgress : 0,
        isCompleted: progress ? progress.isCompleted : false
      };
    }));

    const total = coursesWithProgress.length;
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedCourses = coursesWithProgress.slice(startIndex, endIndex);

    res.json({
      courses: paginatedCourses,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total
      }
    });
  } catch (error) {
    console.error('خطأ في الحصول على الكورسات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على تفاصيل كورس محدد
router.get('/courses/:id', async (req, res) => {
  try {
    const db = req.db;
    const courseDoc = await db.collection('courses').doc(req.params.id).get();

    if (!courseDoc.exists) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    const course = { id: courseDoc.id, ...courseDoc.data() };

    if (!course.isPublished) {
      return res.status(404).json({ message: 'الكورس غير متاح' });
    }

    // التحقق من التسجيل والتقدم
    const progressSnapshot = await db.collection('progress')
      .where('student', '==', req.user.id)
      .where('course', '==', course.id)
      .limit(1)
      .get();

    const progress = progressSnapshot.empty ? null : progressSnapshot.docs[0].data();

    const courseData = {
      ...course,
      isEnrolled: !!progress,
      progress: progress ? progress.overallProgress : 0,
      isCompleted: progress ? progress.isCompleted : false,
      currentLesson: progress ? progress.currentLesson : null,
      lessonsProgress: progress ? progress.lessonsProgress : []
    };

    res.json({ course: courseData });
  } catch (error) {
    console.error('خطأ في الحصول على تفاصيل الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// التسجيل في كورس
router.post('/courses/:id/enroll', async (req, res) => {
  try {
    const db = req.db;
    const courseDoc = await db.collection('courses').doc(req.params.id).get();

    if (!courseDoc.exists) {
      return res.status(404).json({ message: 'الكورس غير موجود' });
    }

    const course = { id: courseDoc.id, ...courseDoc.data() };

    if (!course.isPublished) {
      return res.status(404).json({ message: 'الكورس غير متاح' });
    }

    // التحقق من التسجيل المسبق
    const existingProgressSnapshot = await db.collection('progress')
      .where('student', '==', req.user.id)
      .where('course', '==', course.id)
      .limit(1)
      .get();

    if (!existingProgressSnapshot.empty) {
      return res.status(400).json({ message: 'أنت مسجل في هذا الكورس بالفعل' });
    }

    // إنشاء سجل التقدم
    const lessonsProgress = course.lessons ? course.lessons.map(lesson => ({
      lessonId: lesson.id || lesson._id,
      watchedDuration: 0,
      isCompleted: false,
      lastWatchedAt: null
    })) : [];

    const progressRef = db.collection('progress').doc();
    await progressRef.set({
      student: req.user.id,
      course: course.id,
      lessonsProgress,
      overallProgress: 0,
      isCompleted: false,
      lastAccessedAt: admin.firestore.FieldValue.serverTimestamp(),
      totalWatchTime: 0,
      currentLesson: null,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // تحديث عدد المسجلين في الكورس
    await db.collection('courses').doc(course.id).update({
      enrollmentCount: admin.firestore.FieldValue.increment(1)
    });

    res.json({
      message: 'تم التسجيل في الكورس بنجاح',
      progress: {
        overallProgress: 0,
        isCompleted: false
      }
    });
  } catch (error) {
    console.error('خطأ في التسجيل في الكورس:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الكورسات المسجل فيها الطالب
router.get('/my-courses', async (req, res) => {
  try {
    const db = req.db;
    
    const progressSnapshot = await db.collection('progress')
      .where('student', '==', req.user.id)
      .orderBy('lastAccessedAt', 'desc')
      .get();

    const courses = [];
    
    for (const progressDoc of progressSnapshot.docs) {
      const progressData = progressDoc.data();
      const courseDoc = await db.collection('courses').doc(progressData.course).get();
      
      if (courseDoc.exists) {
        const courseData = courseDoc.data();
        courses.push({
          id: courseDoc.id,
          ...courseData,
          progress: progressData.overallProgress,
          isCompleted: progressData.isCompleted,
          lastAccessedAt: progressData.lastAccessedAt,
          currentLesson: progressData.currentLesson,
          totalWatchTime: progressData.totalWatchTime
        });
      }
    }

    res.json({ courses });
  } catch (error) {
    console.error('خطأ في الحصول على كورساتي:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الشهادات
router.get('/certificates', async (req, res) => {
  try {
    const db = req.db;
    
    const certificatesSnapshot = await db.collection('certificates')
      .where('student', '==', req.user.id)
      .orderBy('issueDate', 'desc')
      .get();

    const certificates = [];
    certificatesSnapshot.forEach(doc => {
      certificates.push({
        id: doc.id,
        ...doc.data()
      });
    });

    res.json({ certificates });
  } catch (error) {
    console.error('خطأ في الحصول على الشهادات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الأقسام
router.get('/categories', async (req, res) => {
  try {
    const db = req.db;
    
    const categoriesSnapshot = await db.collection('categories')
      .where('isActive', '==', true)
      .orderBy('order', 'asc')
      .get();

    const categories = [];
    categoriesSnapshot.forEach(doc => {
      categories.push({
        id: doc.id,
        ...doc.data()
      });
    });

    res.json({ categories });
  } catch (error) {
    console.error('خطأ في الحصول على الأقسام:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
