import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tabs,
  Tab,
  Container,
  InputAdornment,
  IconButton,
  Fade,
  Slide,
  CircularProgress,
  Alert,
  Snackbar,
  LinearProgress,
  Tooltip
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  School,
  Login as LoginIcon,
  AutoAwesome,
  Language,
  Phone,
  Email,
  MenuBook,
  ImportContacts,
  Star,
  Edit,
  Calculate,
  Computer,
  Book,
  Laptop,
  Functions,
  LibraryBooks,
  Lock,
  Person,
  VpnKey,
  CheckCircle,
  Error as ErrorIcon,
  Warning
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import SmartAssistant from './SmartAssistant';

const Login = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { language, changeLanguage, t, isRTL } = useLanguage();

  // Enhanced state management for better UX
  const [loginProgress, setLoginProgress] = useState(0);
  const [errors, setErrors] = useState({});
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [formValidation, setFormValidation] = useState({
    adminEmail: false,
    adminPassword: false,
    studentCode: false
  });

  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });

  // Student login form
  const [studentForm, setStudentForm] = useState({
    code: ''
  });

  const { loginAdmin, loginStudent } = useAuth();

  // Enhanced validation functions
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password) => {
    return password.length >= 6;
  };

  const validateStudentCode = (code) => {
    return code.length === 6 && /^\d+$/.test(code);
  };

  // Show notification
  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  // Close notification
  const closeNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Enhanced form validation
  useEffect(() => {
    setFormValidation({
      adminEmail: validateEmail(adminForm.email),
      adminPassword: validatePassword(adminForm.password),
      studentCode: validateStudentCode(studentForm.code)
    });
  }, [adminForm.email, adminForm.password, studentForm.code]);

  // استخدام نظام الترجمة الجديد من LanguageContext

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const toggleLanguage = () => {
    changeLanguage(language === 'ar' ? 'en' : 'ar');
  };

  const handleAdminSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateEmail(adminForm.email)) {
      setErrors({ email: 'يرجى إدخال بريد إلكتروني صحيح' });
      showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
      return;
    }

    if (!validatePassword(adminForm.password)) {
      setErrors({ password: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' });
      showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
      return;
    }

    setLoading(true);
    setLoginProgress(0);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setLoginProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    try {
      const result = await loginAdmin(adminForm.email, adminForm.password);

      setLoginProgress(100);

      if (result.success) {
        showNotification('تم تسجيل الدخول بنجاح!', 'success');
      } else {
        setErrors({ general: result.message || 'فشل في تسجيل الدخول' });
        showNotification(result.message || 'فشل في تسجيل الدخول', 'error');
      }
    } catch (error) {
      setErrors({ general: 'حدث خطأ غير متوقع' });
      showNotification('حدث خطأ غير متوقع', 'error');
    } finally {
      clearInterval(progressInterval);
      setLoading(false);
      setTimeout(() => setLoginProgress(0), 1000);
    }
  };

  const handleStudentSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateStudentCode(studentForm.code)) {
      setErrors({ code: 'يرجى إدخال كود مكون من 6 أرقام' });
      showNotification('يرجى إدخال كود مكون من 6 أرقام', 'error');
      return;
    }

    setLoading(true);
    setLoginProgress(0);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setLoginProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    try {
      const result = await loginStudent(studentForm.code);

      setLoginProgress(100);

      if (result.success) {
        showNotification('تم تسجيل الدخول بنجاح!', 'success');
      } else {
        setErrors({ general: result.message || 'كود التسجيل غير صحيح' });
        showNotification(result.message || 'كود التسجيل غير صحيح', 'error');
      }
    } catch (error) {
      setErrors({ general: 'حدث خطأ غير متوقع' });
      showNotification('حدث خطأ غير متوقع', 'error');
    } finally {
      clearInterval(progressInterval);
      setLoading(false);
      setTimeout(() => setLoginProgress(0), 1000);
    }
  };

  const handleAdminChange = (field) => (e) => {
    const value = e.target.value;

    setAdminForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field-specific errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }

    // Clear general errors
    if (errors.general) {
      setErrors(prev => ({
        ...prev,
        general: undefined
      }));
    }
  };

  const handleStudentChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setStudentForm({ code: value });

    // Clear field-specific errors when user starts typing
    if (errors.code) {
      setErrors(prev => ({
        ...prev,
        code: undefined
      }));
    }

    // Clear general errors
    if (errors.general) {
      setErrors(prev => ({
        ...prev,
        general: undefined
      }));
    }
  };

  return (
    <Box
      className="login-background"
      dir={language === 'ar' ? 'rtl' : 'ltr'}
      sx={{
        minHeight: '100vh',
        backgroundColor: '#ffffff',
        position: 'relative',
        overflow: 'hidden',

      }}
    >

      {/* Header */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255,255,255,0.3)',
          boxShadow: '0 4px 20px rgba(0,0,255,0.3)'
        }}
      >
        <Container maxWidth="lg">
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              py: { xs: 1, sm: 1.5 },
              px: { xs: 1, sm: 2 }
            }}
          >
            {/* Logo and Brand */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <MenuBook sx={{
                fontSize: { xs: '1.5rem', sm: '2rem' },
                color: '#FFD700',
                filter: 'drop-shadow(0 0 6px rgba(255, 215, 0, 0.6))',
                transform: 'rotate(-5deg)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'rotate(0deg) scale(1.1)',
                  filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.8))'
                }
              }} />
              <Box>
                <Typography
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                    fontWeight: 700,
                    color: '#FFD700',
                    fontFamily: '"Roboto", "Arial", sans-serif',
                    letterSpacing: '1px',
                    lineHeight: 1,
                    whiteSpace: 'nowrap',
                    textShadow: '0 2px 6px rgba(0,0,0,0.5)'
                  }}
                >
                  SKILLS WORLD ACADEMY
                </Typography>
                <Typography
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.9rem' },
                    color: '#FFD700',
                    fontWeight: 500,
                    letterSpacing: '0.5px',
                    mt: 0.2,
                    textShadow: '0 2px 4px rgba(0,0,0,0.4)'
                  }}
                >
                  ALAA ABD HAMIED
                </Typography>
              </Box>
            </Box>

            {/* Contact Info */}
            <Box sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              gap: 3
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Phone sx={{ fontSize: '1rem', color: '#FFD700' }} />
                <Typography sx={{
                  fontSize: '0.9rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  0506747770
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Email sx={{ fontSize: '1rem', color: '#FFD700' }} />
                <Typography sx={{
                  fontSize: '0.9rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  <EMAIL>
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  cursor: 'pointer',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'rgba(255,255,255,0.2)',
                    transform: 'translateY(-1px)'
                  }
                }}
                onClick={toggleLanguage}
              >
                <Language sx={{ fontSize: '1rem', color: '#FFD700' }} />
                <Typography sx={{
                  fontSize: '0.9rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  userSelect: 'none',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  {language === 'ar' ? 'العربية | EN' : 'Arabic | عربي'}
                </Typography>
              </Box>
            </Box>

            {/* Mobile Contact & Language */}
            <Box sx={{
              display: { xs: 'flex', md: 'none' },
              alignItems: 'center',
              gap: { xs: 0.5, sm: 1 }
            }}>
              {/* Language Toggle for Mobile */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.3,
                  cursor: 'pointer',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'rgba(255,255,255,0.2)'
                  }
                }}
                onClick={toggleLanguage}
              >
                <Language sx={{ fontSize: '1rem', color: '#FFD700' }} />
                <Typography sx={{
                  fontSize: '0.75rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  userSelect: 'none',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  {language === 'ar' ? 'EN' : 'عر'}
                </Typography>
              </Box>

              {/* Contact Icons */}
              <Phone sx={{ fontSize: { xs: '1rem', sm: '1.2rem' }, color: '#FFD700' }} />
              <Email sx={{ fontSize: { xs: '1rem', sm: '1.2rem' }, color: '#FFD700' }} />
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Main Content with top padding for fixed header */}
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: { xs: 1, sm: 2, md: 3, lg: 4 },
          paddingTop: { xs: '70px', sm: '80px', md: '90px', lg: '100px' },
          paddingBottom: { xs: 2, sm: 3, md: 4 },
          position: 'relative'
        }}
      >
        {/* Educational Icons Behind Login Card */}
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '120%', sm: '140%', md: '160%', lg: '180%' },
            height: { xs: '120%', sm: '140%', md: '160%', lg: '180%' },
            pointerEvents: 'none',
            zIndex: 0,
            opacity: 0.15,
            overflow: 'hidden'
          }}
        >
          {/* Left Side Educational Icons */}
          <Edit sx={{
            position: 'absolute',
            top: '15%',
            left: '5%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: 'rgba(0, 0, 0, 0.25)',
            transform: 'rotate(-15deg)',
            animation: 'marketingFloat 8s ease-in-out infinite'
          }} />

          <Calculate sx={{
            position: 'absolute',
            top: '35%',
            left: '8%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: 'rgba(0, 0, 0, 0.20)',
            transform: 'rotate(20deg)',
            animation: 'marketingFloat 10s ease-in-out infinite reverse'
          }} />

          <LibraryBooks sx={{
            position: 'absolute',
            top: '60%',
            left: '10%',
            fontSize: { xs: '3.5rem', sm: '4.5rem', md: '5.5rem' },
            color: 'rgba(0, 0, 0, 0.22)',
            transform: 'rotate(-25deg)',
            animation: 'marketingFloat 12s ease-in-out infinite'
          }} />

          <Computer sx={{
            position: 'absolute',
            bottom: '15%',
            left: '12%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: 'rgba(0, 0, 0, 0.18)',
            transform: 'rotate(30deg)',
            animation: 'marketingFloat 9s ease-in-out infinite reverse'
          }} />

          {/* Right Side Educational Icons */}
          <Book sx={{
            position: 'absolute',
            top: '20%',
            right: '8%',
            fontSize: { xs: '3.5rem', sm: '4.5rem', md: '5.5rem' },
            color: 'rgba(0, 0, 0, 0.20)',
            transform: 'rotate(25deg)',
            animation: 'marketingFloat 11s ease-in-out infinite'
          }} />

          <Laptop sx={{
            position: 'absolute',
            top: '45%',
            right: '5%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: 'rgba(0, 0, 0, 0.22)',
            transform: 'rotate(-20deg)',
            animation: 'marketingFloat 7s ease-in-out infinite reverse'
          }} />

          <Functions sx={{
            position: 'absolute',
            bottom: '25%',
            right: '10%',
            fontSize: { xs: '2.8rem', sm: '3.8rem', md: '4.8rem' },
            color: 'rgba(0, 0, 0, 0.18)',
            transform: 'rotate(35deg)',
            animation: 'marketingFloat 13s ease-in-out infinite'
          }} />

          <Edit sx={{
            position: 'absolute',
            bottom: '10%',
            right: '15%',
            fontSize: { xs: '3.2rem', sm: '4.2rem', md: '5.2rem' },
            color: 'rgba(0, 0, 0, 0.20)',
            transform: 'rotate(-30deg)',
            animation: 'marketingFloat 6s ease-in-out infinite reverse'
          }} />

          {/* Center Educational Icons */}
          <MenuBook sx={{
            position: 'absolute',
            top: '25%',
            left: '35%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: 'rgba(0, 0, 0, 0.15)',
            transform: 'rotate(45deg)',
            animation: 'marketingFloat 14s ease-in-out infinite'
          }} />

          <ImportContacts sx={{
            position: 'absolute',
            top: '55%',
            right: '35%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: 'rgba(0, 0, 0, 0.18)',
            transform: 'rotate(-40deg)',
            animation: 'marketingFloat 8.5s ease-in-out infinite reverse'
          }} />

          <School sx={{
            position: 'absolute',
            bottom: '35%',
            left: '25%',
            fontSize: { xs: '2.8rem', sm: '3.8rem', md: '4.8rem' },
            color: 'rgba(0, 0, 0, 0.16)',
            transform: 'rotate(50deg)',
            animation: 'marketingFloat 10.5s ease-in-out infinite'
          }} />

          <Calculate sx={{
            position: 'absolute',
            top: '70%',
            right: '40%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: 'rgba(0, 0, 0, 0.14)',
            transform: 'rotate(-35deg)',
            animation: 'marketingFloat 9.5s ease-in-out infinite reverse'
          }} />

          {/* Additional Educational Icons */}
          <Edit sx={{
            position: 'absolute',
            top: '30%',
            right: '45%',
            fontSize: { xs: '2rem', sm: '3rem', md: '4rem' },
            color: 'rgba(0, 0, 0, 0.12)',
            transform: 'rotate(15deg)',
            animation: 'marketingFloat 15s ease-in-out infinite'
          }} />

          <Book sx={{
            position: 'absolute',
            top: '10%',
            left: '30%',
            fontSize: { xs: '2.2rem', sm: '3.2rem', md: '4.2rem' },
            color: 'rgba(0, 0, 0, 0.14)',
            transform: 'rotate(-45deg)',
            animation: 'marketingFloat 11.5s ease-in-out infinite reverse'
          }} />

          <Computer sx={{
            position: 'absolute',
            top: '65%',
            left: '40%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: 'rgba(0, 0, 0, 0.13)',
            transform: 'rotate(60deg)',
            animation: 'marketingFloat 7.5s ease-in-out infinite'
          }} />

          {/* Scattered Small Stars */}
          <Star sx={{
            position: 'absolute',
            top: '12%',
            left: '15%',
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' },
            color: '#dee2e6',
            transform: 'rotate(-60deg)',
            animation: 'marketingFloat 5s ease-in-out infinite'
          }} />

          <Star sx={{
            position: 'absolute',
            top: '80%',
            left: '20%',
            fontSize: { xs: '1.2rem', sm: '1.7rem', md: '2.2rem' },
            color: '#e9ecef',
            transform: 'rotate(75deg)',
            animation: 'marketingFloat 6.5s ease-in-out infinite reverse'
          }} />

          <Star sx={{
            position: 'absolute',
            top: '18%',
            right: '20%',
            fontSize: { xs: '1.3rem', sm: '1.8rem', md: '2.3rem' },
            color: '#f1f3f4',
            transform: 'rotate(-75deg)',
            animation: 'marketingFloat 4.5s ease-in-out infinite'
          }} />

          <Star sx={{
            position: 'absolute',
            bottom: '20%',
            right: '25%',
            fontSize: { xs: '1.4rem', sm: '1.9rem', md: '2.4rem' },
            color: '#f8f9fa',
            transform: 'rotate(90deg)',
            animation: 'marketingFloat 5.5s ease-in-out infinite reverse'
          }} />
        </Box>
      <Container
        maxWidth="sm"
        sx={{
          width: '100%',
          maxWidth: { xs: '95%', sm: '480px', md: '520px', lg: '580px' },
          mx: 'auto',
          px: { xs: 1, sm: 2, md: 3 }
        }}
      >
        <Fade in timeout={1000}>
          <Card
            sx={{
              borderRadius: { xs: 2, sm: 3, md: 4, lg: 5 },
              boxShadow: {
                xs: '0 8px 20px rgba(0, 0, 255, 0.2), 0 0 0 1px rgba(65, 105, 225, 0.3)',
                sm: '0 15px 30px rgba(0, 0, 255, 0.25), 0 0 0 1px rgba(65, 105, 225, 0.4)',
                md: '0 20px 40px rgba(0, 0, 255, 0.3), 0 0 0 1px rgba(65, 105, 225, 0.4)',
                lg: '0 25px 50px rgba(0, 0, 255, 0.35), 0 0 0 1px rgba(65, 105, 225, 0.5)'
              },
              overflow: 'hidden',
              background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
              backdropFilter: 'none',
              border: '1px solid rgba(65, 105, 225, 0.4)',
              position: 'relative',
              zIndex: 10,
              transition: 'all 0.3s ease-in-out',
              width: '100%',
              maxWidth: '100%',
              // Extra small screens (phones in portrait)
              '@media (max-width: 360px)': {
                borderRadius: 1,
                mx: 0.5,
                boxShadow: '0 4px 12px rgba(0, 0, 255, 0.15)'
              },
              // Large screens
              '@media (min-width: 1200px)': {
                maxWidth: '600px'
              }
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 50%, #0000FF 100%)',
                color: 'white',
                textAlign: 'center',
                py: { xs: 2, sm: 3, md: 4 },
                px: { xs: 2, sm: 3, md: 4 },
                position: 'relative',
                borderBottom: '1px solid rgba(255,255,255,0.2)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
                  pointerEvents: 'none'
                }
              }}
            >
              {/* Logo and Title Container */}
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: 'center',
                justifyContent: 'center',
                mb: { xs: 1, sm: 2 },
                gap: { xs: 1, sm: 2 },
                width: '100%',
                px: { xs: 1, sm: 2 }
              }}>
                {/* Academy Icon */}
                <Box sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  '&:hover .main-book': {
                    transform: 'rotate(-2deg) scale(1.05)',
                    filter: 'drop-shadow(0 0 15px rgba(255, 215, 0, 0.8))'
                  },
                  '&:hover .sparkle-stars': {
                    animation: 'sparkle 1s ease-in-out infinite'
                  }
                }}>
                  <ImportContacts
                    className="main-book"
                    sx={{
                      fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
                      color: '#FFD700',
                      filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.6))',
                      animation: 'bookFloat 3s ease-in-out infinite alternate',
                      transform: 'rotate(-8deg)',
                      transition: 'all 0.4s ease'
                    }}
                  />
                  <AutoAwesome
                    className="sparkle-stars"
                    sx={{
                      fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                      color: '#FFD700',
                      position: 'absolute',
                      top: '20%',
                      right: '15%',
                      filter: 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.4))',
                      animation: 'sparkle 2s ease-in-out infinite alternate',
                      transition: 'all 0.3s ease'
                    }}
                  />
                  <AutoAwesome
                    className="sparkle-stars"
                    sx={{
                      fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },
                      color: '#FFD700',
                      position: 'absolute',
                      bottom: '25%',
                      left: '20%',
                      filter: 'drop-shadow(0 0 3px rgba(255, 215, 0, 0.3))',
                      animation: 'sparkle 2.5s ease-in-out infinite alternate-reverse',
                      transition: 'all 0.3s ease'
                    }}
                  />
                </Box>

                {/* Academy Title */}
                <Box sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  overflow: 'visible'
                }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      color: '#FFD700',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                      letterSpacing: { xs: '0.5px', sm: '1px', md: '1.5px' },
                      fontFamily: '"Roboto", "Arial", sans-serif',
                      fontSize: { xs: '1.1rem', sm: '1.5rem', md: '1.9rem' },
                      textAlign: 'center',
                      lineHeight: 1.2,
                      whiteSpace: 'nowrap',
                      display: 'inline-block',
                      minWidth: 'max-content'
                    }}
                  >
                    SKILLS WORLD ACADEMY
                  </Typography>
                </Box>
              </Box>

              {/* Instructor Name */}
              <Box sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                mb: { xs: 0.5, sm: 1 }
              }}>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 500,
                    color: '#FFD700',
                    letterSpacing: { xs: '1px', sm: '1.2px', md: '1.5px' },
                    fontFamily: '"Roboto", "Arial", sans-serif',
                    textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                    textAlign: 'center',
                    whiteSpace: 'nowrap',
                    display: 'inline-block',
                    minWidth: 'max-content'
                  }}
                >
                  ALAA ABD HAMIED
                </Typography>
              </Box>

              <Typography
                variant="h6"
                sx={{
                  opacity: 0.9,
                  color: 'rgba(255,255,255,0.8)',
                  fontWeight: 300,
                  letterSpacing: { xs: '0.5px', sm: '0.8px', md: '1px' },
                  fontSize: { xs: '0.85rem', sm: '0.95rem', md: '1rem' },
                  textAlign: 'center',
                  px: { xs: 1, sm: 2 },
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {t.platformDesc}
              </Typography>

              {/* CSS Animations for book, sparkle and background effects */}
              <style>
                {`
                  @keyframes bookFloat {
                    0% {
                      transform: rotate(-8deg) translateY(0px);
                      filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.6));
                    }
                    50% {
                      transform: rotate(-5deg) translateY(-8px);
                      filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
                    }
                    100% {
                      transform: rotate(-8deg) translateY(0px);
                      filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.6));
                    }
                  }

                  @keyframes sparkle {
                    0% {
                      transform: rotate(0deg) scale(1);
                      opacity: 0.7;
                    }
                    50% {
                      transform: rotate(180deg) scale(1.2);
                      opacity: 1;
                    }
                    100% {
                      transform: rotate(360deg) scale(1);
                      opacity: 0.7;
                    }
                  }

                  @keyframes float {
                    0% {
                      transform: translateY(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.03;
                    }
                    50% {
                      transform: translateY(-20px) rotate(calc(var(--rotation, 0deg) + 5deg));
                      opacity: 0.05;
                    }
                    100% {
                      transform: translateY(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.03;
                    }
                  }

                  @keyframes marketingFloat {
                    0% {
                      transform: translateY(0px) translateX(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.08;
                    }
                    25% {
                      transform: translateY(-15px) translateX(5px) rotate(calc(var(--rotation, 0deg) + 3deg));
                      opacity: 0.12;
                    }
                    50% {
                      transform: translateY(-25px) translateX(0px) rotate(calc(var(--rotation, 0deg) + 5deg));
                      opacity: 0.15;
                    }
                    75% {
                      transform: translateY(-15px) translateX(-5px) rotate(calc(var(--rotation, 0deg) + 3deg));
                      opacity: 0.12;
                    }
                    100% {
                      transform: translateY(0px) translateX(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.08;
                    }
                  }

                  @keyframes pageFlip {
                    0% { transform: rotateY(0deg); }
                    50% { transform: rotateY(15deg); }
                    100% { transform: rotateY(0deg); }
                  }

                  /* Responsive adjustments for background icons */
                  @media (max-width: 768px) {
                    .marketing-bg-icon {
                      font-size: 2rem !important;
                      opacity: 0.02 !important;
                    }
                  }

                  @media (max-width: 480px) {
                    .marketing-bg-icon {
                      font-size: 1.5rem !important;
                      opacity: 0.015 !important;
                    }
                  }
                `}
              </style>
            </Box>

            <CardContent sx={{ p: 0, background: '#000000' }}>
              {/* Tabs */}
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  borderBottom: '1px solid rgba(65, 105, 225, 0.4)',
                  background: 'linear-gradient(90deg, #0000FF 0%, #4169E1 50%, #0000FF 100%)',
                  '& .MuiTab-root': {
                    py: 2,
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: 'rgba(255,255,255,0.8)',
                    textTransform: 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      color: 'white',
                      background: 'rgba(255,255,255,0.1)'
                    },
                    '&.Mui-selected': {
                      color: 'white',
                      background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                      borderBottom: '2px solid #FFD700'
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#FFD700',
                    height: '3px',
                    boxShadow: '0 0 8px rgba(255, 215, 0, 0.5)'
                  }
                }}
              >
                <Tab
                  icon={<AdminPanelSettings sx={{
                    color: tabValue === 0 ? '#FFD700' : 'rgba(255, 215, 0, 0.7)',
                    filter: tabValue === 0 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))' : 'none'
                  }} />}
                  label={t.admin}
                  iconPosition="start"
                />
                <Tab
                  icon={<School sx={{
                    color: tabValue === 1 ? '#FFD700' : 'rgba(255, 215, 0, 0.7)',
                    filter: tabValue === 1 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))' : 'none'
                  }} />}
                  label={t.student}
                  iconPosition="start"
                />
              </Tabs>

              <Box sx={{
                p: { xs: 1.5, sm: 2.5, md: 3.5, lg: 4 },
                background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                minHeight: { xs: '320px', sm: '360px', md: '400px', lg: '420px' }
              }}>
                {/* Admin Login */}
                {tabValue === 0 && (
                  <Slide direction="right" in={tabValue === 0} timeout={300}>
                    <Box
                      component="form"
                      onSubmit={handleAdminSubmit}
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: '100%'
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: '#0000FF',
                          fontWeight: 600,
                          letterSpacing: { xs: '0.5px', sm: '1px' },
                          fontSize: { xs: '1.1rem', sm: '1.25rem' }
                        }}
                      >
                        {t.adminLogin}
                      </Typography>
                      
                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <TextField
                          fullWidth
                          label={t.email}
                          type="email"
                          value={adminForm.email}
                          onChange={handleAdminChange('email')}
                          required
                          size="small"
                          sx={{
                            mb: { xs: 2, sm: 2.5, md: 3 },
                            maxWidth: { xs: '100%', sm: '380px', md: '400px' },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(65, 105, 225, 0.08)',
                            border: '1px solid rgba(0, 0, 255, 0.4)',
                            borderRadius: { xs: '10px', sm: '12px', md: '14px' },
                            color: '#1f2937',
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                            fontWeight: 500,
                            minHeight: { xs: '42px', sm: '46px', md: '50px' },
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              border: '1px solid rgba(0, 0, 255, 0.6)',
                              backgroundColor: 'rgba(65, 105, 225, 0.12)',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 255, 0.15)'
                            },
                            '&.Mui-focused': {
                              border: '2px solid #0000FF',
                              backgroundColor: 'rgba(65, 105, 225, 0.15)',
                              boxShadow: '0 0 0 3px rgba(0, 0, 255, 0.2), 0 4px 16px rgba(0, 0, 255, 0.2)',
                              transform: 'translateY(-2px)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: '#374151',
                            fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
                            fontWeight: 600,
                            '&.Mui-focused': {
                              color: '#0000FF',
                              fontWeight: 700
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          },
                          '& .MuiInputBase-input': {
                            padding: { xs: '10px 8px', sm: '12px 10px', md: '14px 12px' },
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' }
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Email sx={{
                                color: '#FFD700',
                                fontSize: { xs: '1.2rem', sm: '1.3rem', md: '1.4rem' },
                                filter: 'drop-shadow(0 0 6px rgba(255, 215, 0, 0.4))',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  transform: 'scale(1.1)',
                                  filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.6))'
                                }
                              }} />
                            </InputAdornment>
                          )
                        }}
                        error={!!errors.email}
                        helperText={errors.email}
                        />
                      </Box>

                      {/* Email validation indicator */}
                      {adminForm.email && (
                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 1 }}>
                          <Box sx={{ maxWidth: { xs: '100%', sm: '380px', md: '400px' }, width: '100%' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {formValidation.adminEmail ? (
                                <CheckCircle sx={{ color: 'success.main', fontSize: '1rem' }} />
                              ) : (
                                <ErrorIcon sx={{ color: 'error.main', fontSize: '1rem' }} />
                              )}
                              <Typography
                                variant="caption"
                                sx={{
                                  color: formValidation.adminEmail ? 'success.main' : 'error.main',
                                  fontSize: '0.75rem',
                                  fontWeight: 500
                                }}
                              >
                                {formValidation.adminEmail ?
                                  (language === 'ar' ? 'بريد إلكتروني صحيح' : 'Valid email') :
                                  (language === 'ar' ? 'بريد إلكتروني غير صحيح' : 'Invalid email')
                                }
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      )}

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <TextField
                          fullWidth
                          label={t.password}
                          type={showPassword ? 'text' : 'password'}
                          value={adminForm.password}
                          onChange={handleAdminChange('password')}
                          required
                          size="small"
                          sx={{
                            mb: 4,
                            maxWidth: { xs: '100%', sm: '380px', md: '400px' },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(65, 105, 225, 0.08)',
                            border: '1px solid rgba(0, 0, 255, 0.4)',
                            borderRadius: { xs: '10px', sm: '12px', md: '14px' },
                            color: '#1f2937',
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                            fontWeight: 500,
                            minHeight: { xs: '42px', sm: '46px', md: '50px' },
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              border: '1px solid rgba(0, 0, 255, 0.6)',
                              backgroundColor: 'rgba(65, 105, 225, 0.12)',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 255, 0.15)'
                            },
                            '&.Mui-focused': {
                              border: '2px solid #0000FF',
                              backgroundColor: 'rgba(65, 105, 225, 0.15)',
                              boxShadow: '0 0 0 3px rgba(0, 0, 255, 0.2), 0 4px 16px rgba(0, 0, 255, 0.2)',
                              transform: 'translateY(-2px)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: '#374151',
                            fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
                            fontWeight: 600,
                            '&.Mui-focused': {
                              color: '#0000FF',
                              fontWeight: 700
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          },
                          '& .MuiInputBase-input': {
                            padding: { xs: '10px 8px', sm: '12px 10px', md: '14px 12px' },
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' }
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Lock sx={{
                                color: '#FFD700',
                                fontSize: { xs: '1.2rem', sm: '1.3rem', md: '1.4rem' },
                                filter: 'drop-shadow(0 0 6px rgba(255, 215, 0, 0.4))',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  transform: 'scale(1.1)',
                                  filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.6))'
                                }
                              }} />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                                size="small"
                                sx={{
                                  color: '#374151',
                                  padding: '6px',
                                  '&:hover': {
                                    color: '#0000FF',
                                    background: 'rgba(65, 105, 225, 0.1)',
                                    transform: 'scale(1.1)'
                                  }
                                }}
                              >
                                {showPassword ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                        error={!!errors.password}
                        helperText={errors.password}
                        />
                      </Box>

                      {/* Password validation indicator */}
                      {adminForm.password && (
                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 2 }}>
                          <Box sx={{ maxWidth: { xs: '100%', sm: '380px', md: '400px' }, width: '100%' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {formValidation.adminPassword ? (
                                <CheckCircle sx={{ color: 'success.main', fontSize: '1rem' }} />
                              ) : (
                                <ErrorIcon sx={{ color: 'error.main', fontSize: '1rem' }} />
                              )}
                              <Typography
                                variant="caption"
                                sx={{
                                  color: formValidation.adminPassword ? 'success.main' : 'error.main',
                                  fontSize: '0.75rem',
                                  fontWeight: 500
                                }}
                              >
                                {formValidation.adminPassword ?
                                  (language === 'ar' ? 'كلمة مرور قوية' : 'Strong password') :
                                  (language === 'ar' ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' : 'Password must be at least 6 characters')
                                }
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      )}

                      {/* General error message */}
                      {errors.general && (
                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 2 }}>
                          <Alert
                            severity="error"
                            sx={{
                              maxWidth: { xs: '100%', sm: '380px', md: '400px' },
                              width: '100%',
                              borderRadius: 2
                            }}
                          >
                            {errors.general}
                          </Alert>
                        </Box>
                      )}

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <Button
                          fullWidth
                          type="submit"
                          variant="contained"
                          size="large"
                          disabled={loading}
                          startIcon={loading ? <CircularProgress size={18} sx={{ color: '#FFD700' }} /> : <LoginIcon sx={{ color: '#FFD700', fontSize: '1.2rem' }} />}
                          sx={{
                            py: { xs: 1.3, sm: 1.5, md: 1.7 },
                            px: { xs: 2, sm: 3, md: 4 },
                            fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                            fontWeight: 700,
                            background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 50%, #6366F1 100%)',
                            color: '#ffffff',
                            border: '2px solid rgba(0, 0, 255, 0.3)',
                            borderRadius: { xs: '12px', sm: '14px', md: '16px' },
                            textTransform: 'none',
                            boxShadow: '0 6px 20px rgba(0, 0, 255, 0.25), 0 2px 8px rgba(0, 0, 255, 0.15)',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            minHeight: { xs: '48px', sm: '52px', md: '56px' },
                            maxWidth: { xs: '100%', sm: '380px', md: '400px' },
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: '-100%',
                            width: '100%',
                            height: '100%',
                            background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
                            transition: 'left 0.5s ease'
                          },
                          '&:hover': {
                            background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 50%, #3B82F6 100%)',
                            transform: 'translateY(-3px)',
                            boxShadow: '0 8px 25px rgba(0, 0, 255, 0.35), 0 4px 12px rgba(0, 0, 255, 0.2)',
                            '&::before': {
                              left: '100%'
                            }
                          },
                          '&:active': {
                            transform: 'translateY(-1px)',
                            boxShadow: '0 4px 15px rgba(0, 0, 255, 0.25)'
                          },
                          '&:disabled': {
                            background: 'rgba(0, 0, 255, 0.3)',
                            color: 'rgba(255,255,255,0.5)',
                            transform: 'none',
                            boxShadow: 'none'
                          },
                          // Touch-friendly for mobile
                          '@media (hover: none)': {
                            '&:hover': {
                              transform: 'none',
                              background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                            }
                          }
                        }}
                        >
                          {loading ? t.loggingIn : t.login}
                        </Button>
                      </Box>

                      {/* رابط نسيت كلمة المرور */}
                      <Box sx={{ textAlign: 'center', mt: 2 }}>
                        <Button
                          variant="text"
                          size="small"
                          onClick={() => window.location.href = '/forgot-password'}
                          sx={{
                            color: '#0000FF',
                            textTransform: 'none',
                            fontSize: { xs: '0.85rem', sm: '0.9rem' },
                            fontWeight: 500,
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 255, 0.04)',
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          {language === 'ar' ? 'نسيت كلمة المرور؟' : 'Forgot Password?'}
                        </Button>
                      </Box>
                    </Box>
                  </Slide>
                )}

                {/* Student Login */}
                {tabValue === 1 && (
                  <Slide direction="left" in={tabValue === 1} timeout={300}>
                    <Box
                      component="form"
                      onSubmit={handleStudentSubmit}
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: '100%'
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: '#0000FF',
                          fontWeight: 600,
                          letterSpacing: { xs: '0.5px', sm: '1px' },
                          fontSize: { xs: '1.1rem', sm: '1.25rem' }
                        }}
                      >
                        {t.studentLogin}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: '#374151',
                          fontSize: { xs: '0.85rem', sm: '0.95rem' },
                          px: { xs: 1, sm: 0 },
                          fontWeight: 500
                        }}
                      >
                        {t.studentCodeDesc}
                      </Typography>
                      
                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <TextField
                          fullWidth
                          label={t.studentCode}
                          value={studentForm.code}
                          onChange={handleStudentChange}
                          required
                          size="medium"
                          inputProps={{
                            maxLength: 6,
                            style: {
                              textAlign: 'center',
                              fontSize: window.innerWidth < 600 ? '1.2rem' : '1.4rem',
                              letterSpacing: window.innerWidth < 600 ? '0.2rem' : '0.4rem',
                              color: '#1f2937',
                              fontWeight: 700
                            }
                          }}
                          sx={{
                            mb: { xs: 3, sm: 4 },
                            maxWidth: { xs: '100%', sm: '320px', md: '350px' },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(65, 105, 225, 0.08)',
                            border: '1px solid rgba(0, 0, 255, 0.4)',
                            borderRadius: { xs: '12px', sm: '14px', md: '16px' },
                            color: '#1f2937',
                            minHeight: { xs: '52px', sm: '58px', md: '64px' },
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              border: '1px solid rgba(0, 0, 255, 0.6)',
                              backgroundColor: 'rgba(65, 105, 225, 0.12)',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 6px 16px rgba(0, 0, 255, 0.15)'
                            },
                            '&.Mui-focused': {
                              border: '2px solid #0000FF',
                              backgroundColor: 'rgba(65, 105, 225, 0.15)',
                              boxShadow: '0 0 0 4px rgba(0, 0, 255, 0.2), 0 6px 20px rgba(0, 0, 255, 0.2)',
                              transform: 'translateY(-2px)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: '#374151',
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                            fontWeight: 600,
                            '&.Mui-focused': {
                              color: '#0000FF',
                              fontWeight: 700
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Person sx={{
                                color: '#FFD700',
                                fontSize: { xs: '1.3rem', sm: '1.4rem', md: '1.5rem' },
                                filter: 'drop-shadow(0 0 6px rgba(255, 215, 0, 0.4))',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  transform: 'scale(1.1)',
                                  filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.6))'
                                }
                              }} />
                            </InputAdornment>
                          )
                        }}
                        error={!!errors.code}
                        helperText={errors.code}
                        />
                      </Box>

                      {/* Student code validation indicator */}
                      {studentForm.code && (
                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 2 }}>
                          <Box sx={{ maxWidth: { xs: '100%', sm: '320px', md: '350px' }, width: '100%' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {formValidation.studentCode ? (
                                <CheckCircle sx={{ color: 'success.main', fontSize: '1rem' }} />
                              ) : (
                                <ErrorIcon sx={{ color: 'error.main', fontSize: '1rem' }} />
                              )}
                              <Typography
                                variant="caption"
                                sx={{
                                  color: formValidation.studentCode ? 'success.main' : 'error.main',
                                  fontSize: '0.75rem',
                                  fontWeight: 500
                                }}
                              >
                                {formValidation.studentCode ?
                                  (language === 'ar' ? 'كود صحيح' : 'Valid code') :
                                  (language === 'ar' ? `${studentForm.code.length}/6 أرقام` : `${studentForm.code.length}/6 digits`)
                                }
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      )}

                      {/* General error message for student */}
                      {errors.general && (
                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 2 }}>
                          <Alert
                            severity="error"
                            sx={{
                              maxWidth: { xs: '100%', sm: '320px', md: '350px' },
                              width: '100%',
                              borderRadius: 2
                            }}
                          >
                            {errors.general}
                          </Alert>
                        </Box>
                      )}

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <Button
                          fullWidth
                          type="submit"
                          variant="contained"
                          size="large"
                          disabled={loading || studentForm.code.length !== 6}
                          startIcon={loading ? <CircularProgress size={18} sx={{ color: '#FFD700' }} /> : <LoginIcon sx={{ color: '#FFD700', fontSize: '1.2rem' }} />}
                          sx={{
                            py: { xs: 1.3, sm: 1.5, md: 1.7 },
                            px: { xs: 2, sm: 3, md: 4 },
                            fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                            fontWeight: 700,
                            background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 50%, #6366F1 100%)',
                            color: '#ffffff',
                            border: '2px solid rgba(0, 0, 255, 0.3)',
                            borderRadius: { xs: '12px', sm: '14px', md: '16px' },
                            textTransform: 'none',
                            boxShadow: '0 6px 20px rgba(0, 0, 255, 0.25), 0 2px 8px rgba(0, 0, 255, 0.15)',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            minHeight: { xs: '48px', sm: '52px', md: '56px' },
                            maxWidth: { xs: '100%', sm: '320px', md: '350px' },
                          mx: 'auto',
                          display: 'block',
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: '-100%',
                            width: '100%',
                            height: '100%',
                            background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
                            transition: 'left 0.5s ease'
                          },
                          '&:hover': {
                            background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 50%, #3B82F6 100%)',
                            transform: 'translateY(-3px)',
                            boxShadow: '0 8px 25px rgba(0, 0, 255, 0.35), 0 4px 12px rgba(0, 0, 255, 0.2)',
                            '&::before': {
                              left: '100%'
                            }
                          },
                          '&:active': {
                            transform: 'translateY(-1px)',
                            boxShadow: '0 4px 15px rgba(0, 0, 255, 0.25)'
                          },
                          '&:disabled': {
                            background: 'rgba(0, 0, 255, 0.2)',
                            color: 'rgba(255,255,255,0.4)',
                            transform: 'none',
                            boxShadow: 'none',
                            '&::before': {
                              display: 'none'
                            }
                          },
                          // Touch-friendly for mobile
                          '@media (hover: none)': {
                            '&:hover': {
                              transform: 'none',
                              background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                            }
                          }
                        }}
                        >
                          {loading ? t.loggingIn : t.login}
                        </Button>
                      </Box>
                    </Box>
                  </Slide>
                )}
              </Box>
            </CardContent>
          </Card>
        </Fade>
      </Container>
      </Box>

      {/* شريط التقدم المحسن */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 9999,
            background: 'rgba(0, 0, 255, 0.1)',
            backdropFilter: 'blur(10px)'
          }}
        >
          <LinearProgress
            variant="determinate"
            value={loginProgress}
            sx={{
              height: 4,
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #0000FF, #4169E1, #FFD700)',
                transition: 'transform 0.3s ease'
              }
            }}
          />
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              py: 1,
              px: 2
            }}
          >
            <CircularProgress size={16} sx={{ color: '#0000FF', mr: 1 }} />
            <Typography
              variant="body2"
              sx={{
                color: '#0000FF',
                fontWeight: 600,
                fontSize: '0.85rem'
              }}
            >
              {language === 'ar' ? 'جاري تسجيل الدخول...' : 'Logging in...'}
            </Typography>
          </Box>
        </Box>
      )}

      {/* الإشعارات المحسنة */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={closeNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ mt: 8 }}
      >
        <Alert
          onClose={closeNotification}
          severity={notification.severity}
          variant="filled"
          sx={{
            width: '100%',
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
            '& .MuiAlert-icon': {
              fontSize: '1.5rem'
            },
            '& .MuiAlert-message': {
              fontSize: '1rem',
              fontWeight: 600
            }
          }}
          icon={
            notification.severity === 'success' ? <CheckCircle /> :
            notification.severity === 'error' ? <ErrorIcon /> :
            notification.severity === 'warning' ? <Warning /> : undefined
          }
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* المساعد الذكي */}
      <SmartAssistant language={language} />
    </Box>
  );
};

export default Login;
