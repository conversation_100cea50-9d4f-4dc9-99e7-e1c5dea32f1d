import React, { useState, useEffect } from 'react';
import {
  Box,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  Slide,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  SmartToy,
  Close,
  Send,
  Psychology,
  Help,
  Info,
  School,
  AdminPanelSettings,
  VolumeUp,
  VolumeOff
} from '@mui/icons-material';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const SmartAssistant = ({ language = 'ar' }) => {
  const [open, setOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(false);

  // الترجمات
  const translations = {
    ar: {
      assistantTitle: 'المساعد الذكي - علاء عبد الحميد',
      placeholder: 'اكتب سؤالك هنا...',
      send: 'إرسال',
      close: 'إغلاق',
      typing: 'المساعد يكتب...',
      welcomeMessage: 'مرحباً! أنا المساعد الذكي لمنصة SKILLS WORLD ACADEMY. كيف يمكنني مساعدتك اليوم؟',
      quickHelp: 'مساعدة سريعة',
      loginHelp: 'مساعدة تسجيل الدخول',
      aboutPlatform: 'عن المنصة',
      contactInfo: 'معلومات التواصل',
      suggestions: [
        'كيف أسجل دخول كمدير؟',
        'كيف أسجل دخول كطالب؟',
        'نسيت كلمة المرور',
        'ما هي الكورسات المتاحة؟',
        'كيف أتواصل مع المدرب؟'
      ]
    },
    en: {
      assistantTitle: 'Smart Assistant - Alaa Abd Hamied',
      placeholder: 'Type your question here...',
      send: 'Send',
      close: 'Close',
      typing: 'Assistant is typing...',
      welcomeMessage: 'Hello! I am the smart assistant for SKILLS WORLD ACADEMY platform. How can I help you today?',
      quickHelp: 'Quick Help',
      loginHelp: 'Login Help',
      aboutPlatform: 'About Platform',
      contactInfo: 'Contact Info',
      suggestions: [
        'How to login as admin?',
        'How to login as student?',
        'Forgot password',
        'What courses are available?',
        'How to contact the trainer?'
      ]
    }
  };

  const t = translations[language];

  // الردود الذكية
  const smartResponses = {
    ar: {
      adminLogin: 'لتسجيل الدخول كمدير:\n1. اختر تبويب "المدير"\n2. أدخل البريد الإلكتروني\n3. أدخل كلمة المرور\n4. اضغط "دخول"\n\nإذا واجهت مشكلة، تأكد من صحة البيانات أو تواصل مع الدعم الفني.',
      studentLogin: 'لتسجيل الدخول كطالب:\n1. اختر تبويب "الطالب"\n2. أدخل الكود المكون من 6 أرقام\n3. اضغط "دخول"\n\nإذا لم تحصل على الكود، تواصل مع المدرب علاء عبد الحميد.',
      forgotPassword: 'إذا نسيت كلمة المرور:\n• للمدراء: تواصل مع الدعم الفني\n• للطلاب: استخدم الكود المرسل إليك\n\nللمساعدة: 0506747770',
      courses: 'منصة SKILLS WORLD ACADEMY تقدم:\n• كورسات التسويق الرقمي\n• كورسات ريادة الأعمال\n• كورسات التطوير المهني\n• ورش عملية متخصصة\n\nللاستفسار عن الكورسات المتاحة، تواصل معنا.',
      contact: 'معلومات التواصل:\n📞 الهاتف: 0506747770\n📧 البريد: <EMAIL>\n👨‍🏫 المدرب: علاء عبد الحميد\n🌐 المنصة: SKILLS WORLD ACADEMY',
      platform: 'منصة SKILLS WORLD ACADEMY:\n• منصة تعليمية متطورة\n• كورسات عملية ومتخصصة\n• شهادات معتمدة\n• متابعة شخصية من المدرب\n• محتوى عربي عالي الجودة',
      default: 'شكراً لسؤالك! يمكنني مساعدتك في:\n• تسجيل الدخول\n• معلومات الكورسات\n• التواصل مع المدرب\n• استخدام المنصة\n\nاختر من الاقتراحات أو اكتب سؤالك.'
    },
    en: {
      adminLogin: 'To login as admin:\n1. Select "Admin" tab\n2. Enter email address\n3. Enter password\n4. Click "Login"\n\nIf you face issues, check your credentials or contact technical support.',
      studentLogin: 'To login as student:\n1. Select "Student" tab\n2. Enter the 6-digit code\n3. Click "Login"\n\nIf you don\'t have the code, contact trainer Alaa Abd Hamied.',
      forgotPassword: 'If you forgot password:\n• For admins: Contact technical support\n• For students: Use the code sent to you\n\nFor help: 0506747770',
      courses: 'SKILLS WORLD ACADEMY offers:\n• Digital Marketing courses\n• Entrepreneurship courses\n• Professional Development courses\n• Specialized workshops\n\nFor course inquiries, contact us.',
      contact: 'Contact Information:\n📞 Phone: 0506747770\n📧 Email: <EMAIL>\n👨‍🏫 Trainer: Alaa Abd Hamied\n🌐 Platform: SKILLS WORLD ACADEMY',
      platform: 'SKILLS WORLD ACADEMY platform:\n• Advanced learning platform\n• Practical specialized courses\n• Certified certificates\n• Personal follow-up from trainer\n• High-quality Arabic content',
      default: 'Thank you for your question! I can help you with:\n• Login process\n• Course information\n• Contacting the trainer\n• Using the platform\n\nChoose from suggestions or type your question.'
    }
  };

  // تحليل الرسالة وإرجاع الرد المناسب
  const analyzeMessage = (message) => {
    const lowerMessage = message.toLowerCase();
    const responses = smartResponses[language];

    if (lowerMessage.includes('مدير') || lowerMessage.includes('admin')) {
      return responses.adminLogin;
    }
    if (lowerMessage.includes('طالب') || lowerMessage.includes('student')) {
      return responses.studentLogin;
    }
    if (lowerMessage.includes('نسيت') || lowerMessage.includes('forgot') || lowerMessage.includes('password')) {
      return responses.forgotPassword;
    }
    if (lowerMessage.includes('كورس') || lowerMessage.includes('course')) {
      return responses.courses;
    }
    if (lowerMessage.includes('تواصل') || lowerMessage.includes('contact')) {
      return responses.contact;
    }
    if (lowerMessage.includes('منصة') || lowerMessage.includes('platform')) {
      return responses.platform;
    }
    
    return responses.default;
  };

  // إضافة رسالة ترحيب عند فتح المساعد
  useEffect(() => {
    if (open && messages.length === 0) {
      setMessages([{
        id: 1,
        text: t.welcomeMessage,
        sender: 'assistant',
        timestamp: new Date()
      }]);
    }
  }, [open, messages.length, t.welcomeMessage]);

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // محاكاة وقت الاستجابة
    setTimeout(() => {
      const response = analyzeMessage(inputMessage);
      const assistantMessage = {
        id: Date.now() + 1,
        text: response,
        sender: 'assistant',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);

      // تشغيل الصوت إذا كان مفعلاً
      if (voiceEnabled) {
        speakText(response);
      }
    }, 1500);
  };

  const handleSuggestionClick = (suggestion) => {
    setInputMessage(suggestion);
  };

  const speakText = (text) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = language === 'ar' ? 'ar-SA' : 'en-US';
      utterance.rate = 0.8;
      window.speechSynthesis.speak(utterance);
    }
  };

  return (
    <>
      {/* زر المساعد العائم */}
      <Tooltip title={language === 'ar' ? 'المساعد الذكي' : 'Smart Assistant'}>
        <Fab
          color="primary"
          onClick={() => setOpen(true)}
          sx={{
            position: 'fixed',
            bottom: { xs: 16, sm: 24 },
            right: { xs: 16, sm: 24 },
            background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
            color: '#000',
            boxShadow: '0 4px 20px rgba(255, 215, 0, 0.4)',
            '&:hover': {
              background: 'linear-gradient(135deg, #FFA500 0%, #FFD700 100%)',
              transform: 'scale(1.1)',
              boxShadow: '0 6px 25px rgba(255, 215, 0, 0.6)'
            },
            transition: 'all 0.3s ease',
            zIndex: 1000
          }}
        >
          <SmartToy sx={{ fontSize: '1.5rem' }} />
        </Fab>
      </Tooltip>

      {/* نافذة المساعد */}
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        TransitionComponent={Transition}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
            border: '1px solid rgba(255, 215, 0, 0.3)',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
          }
        }}
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Psychology sx={{ color: '#FFD700', fontSize: '1.5rem' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {t.assistantTitle}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              onClick={() => setVoiceEnabled(!voiceEnabled)}
              sx={{ color: voiceEnabled ? '#FFD700' : 'rgba(255,255,255,0.7)' }}
            >
              {voiceEnabled ? <VolumeUp /> : <VolumeOff />}
            </IconButton>
            <IconButton onClick={() => setOpen(false)} sx={{ color: 'white' }}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0, height: '400px', display: 'flex', flexDirection: 'column' }}>
          {/* منطقة الرسائل */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
            <List>
              {messages.map((message) => (
                <ListItem
                  key={message.id}
                  sx={{
                    flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    mb: 1
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        bgcolor: message.sender === 'user' ? '#0000FF' : '#FFD700',
                        color: message.sender === 'user' ? 'white' : '#000'
                      }}
                    >
                      {message.sender === 'user' ? 
                        <School /> : 
                        <Psychology />
                      }
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box
                        sx={{
                          bgcolor: message.sender === 'user' ? '#0000FF' : '#f5f5f5',
                          color: message.sender === 'user' ? 'white' : '#333',
                          p: 2,
                          borderRadius: 2,
                          maxWidth: '80%',
                          whiteSpace: 'pre-line'
                        }}
                      >
                        {message.text}
                      </Box>
                    }
                  />
                </ListItem>
              ))}
              
              {isTyping && (
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: '#FFD700', color: '#000' }}>
                      <Psychology />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 2 }}>
                        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                          {t.typing}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              )}
            </List>
          </Box>

          {/* الاقتراحات السريعة */}
          <Box sx={{ p: 2, borderTop: '1px solid #eee' }}>
            <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
              {t.quickHelp}:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {t.suggestions.map((suggestion, index) => (
                <Chip
                  key={index}
                  label={suggestion}
                  onClick={() => handleSuggestionClick(suggestion)}
                  sx={{
                    bgcolor: 'rgba(255, 215, 0, 0.1)',
                    color: '#0000FF',
                    border: '1px solid rgba(255, 215, 0, 0.3)',
                    '&:hover': {
                      bgcolor: 'rgba(255, 215, 0, 0.2)'
                    }
                  }}
                />
              ))}
            </Box>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 2, borderTop: '1px solid #eee' }}>
          <Box sx={{ display: 'flex', width: '100%', gap: 1 }}>
            <TextField
              fullWidth
              placeholder={t.placeholder}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&.Mui-focused fieldset': {
                    borderColor: '#FFD700'
                  }
                }
              }}
            />
            <Button
              variant="contained"
              onClick={handleSendMessage}
              disabled={!inputMessage.trim()}
              sx={{
                background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
                color: '#000',
                minWidth: 'auto',
                px: 2,
                '&:hover': {
                  background: 'linear-gradient(135deg, #FFA500 0%, #FFD700 100%)'
                }
              }}
            >
              <Send />
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SmartAssistant;
