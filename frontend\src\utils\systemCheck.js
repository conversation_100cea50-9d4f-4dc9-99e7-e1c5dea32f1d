import { collection, getDocs, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * فحص شامل للنظام
 */
export const systemCheck = async () => {
  console.log('🔍 بدء فحص النظام الشامل...');
  
  const results = {
    firebase: false,
    database: false,
    admin: false,
    students: false,
    courses: false,
    errors: []
  };

  try {
    // 1. فحص الاتصال بـ Firebase
    console.log('📡 فحص الاتصال بـ Firebase...');
    try {
      await getDocs(collection(db, 'test'));
      results.firebase = true;
      console.log('✅ Firebase متصل بنجاح');
    } catch (error) {
      results.errors.push(`Firebase: ${error.message}`);
      console.error('❌ خطأ في الاتصال بـ Firebase:', error.message);
    }

    // 2. فحص قاعدة البيانات
    console.log('🗄️ فحص قاعدة البيانات...');
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      results.database = true;
      console.log(`✅ قاعدة البيانات تعمل - ${usersSnapshot.size} مستخدم`);
    } catch (error) {
      results.errors.push(`Database: ${error.message}`);
      console.error('❌ خطأ في قاعدة البيانات:', error.message);
    }

    // 3. فحص وجود المدير
    console.log('👨‍💼 فحص حساب المدير...');
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      const admins = usersSnapshot.docs.filter(doc => doc.data().role === 'admin');
      
      if (admins.length > 0) {
        results.admin = true;
        console.log(`✅ تم العثور على ${admins.length} مدير`);
        admins.forEach(admin => {
          const data = admin.data();
          console.log(`   - ${data.name} (${data.email})`);
        });
      } else {
        results.errors.push('لا يوجد حساب مدير');
        console.log('⚠️ لا يوجد حساب مدير');
      }
    } catch (error) {
      results.errors.push(`Admin check: ${error.message}`);
      console.error('❌ خطأ في فحص المدير:', error.message);
    }

    // 4. فحص الطلاب
    console.log('👨‍🎓 فحص الطلاب...');
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      const students = usersSnapshot.docs.filter(doc => doc.data().role === 'student');
      
      if (students.length > 0) {
        results.students = true;
        console.log(`✅ تم العثور على ${students.length} طالب`);
        students.forEach(student => {
          const data = student.data();
          console.log(`   - ${data.name} (كود: ${data.studentCode})`);
        });
      } else {
        results.errors.push('لا يوجد طلاب');
        console.log('⚠️ لا يوجد طلاب');
      }
    } catch (error) {
      results.errors.push(`Students check: ${error.message}`);
      console.error('❌ خطأ في فحص الطلاب:', error.message);
    }

    // 5. فحص الكورسات
    console.log('📚 فحص الكورسات...');
    try {
      const coursesSnapshot = await getDocs(collection(db, 'courses'));
      
      if (coursesSnapshot.size > 0) {
        results.courses = true;
        console.log(`✅ تم العثور على ${coursesSnapshot.size} كورس`);
        coursesSnapshot.docs.forEach(course => {
          const data = course.data();
          console.log(`   - ${data.title}`);
        });
      } else {
        results.errors.push('لا يوجد كورسات');
        console.log('⚠️ لا يوجد كورسات');
      }
    } catch (error) {
      results.errors.push(`Courses check: ${error.message}`);
      console.error('❌ خطأ في فحص الكورسات:', error.message);
    }

    // 6. تقرير النتائج
    console.log('\n📊 تقرير فحص النظام:');
    console.log('='.repeat(40));
    console.log(`Firebase: ${results.firebase ? '✅' : '❌'}`);
    console.log(`قاعدة البيانات: ${results.database ? '✅' : '❌'}`);
    console.log(`المدير: ${results.admin ? '✅' : '❌'}`);
    console.log(`الطلاب: ${results.students ? '✅' : '❌'}`);
    console.log(`الكورسات: ${results.courses ? '✅' : '❌'}`);
    
    if (results.errors.length > 0) {
      console.log('\n❌ الأخطاء المكتشفة:');
      results.errors.forEach(error => console.log(`   - ${error}`));
    }

    const allGood = results.firebase && results.database && results.admin;
    
    if (allGood) {
      console.log('\n🎉 النظام يعمل بشكل صحيح!');
    } else {
      console.log('\n⚠️ النظام يحتاج إلى إعداد. شغّل: quickSetup()');
    }

    return results;

  } catch (error) {
    console.error('❌ خطأ عام في فحص النظام:', error);
    results.errors.push(`System error: ${error.message}`);
    return results;
  }
};

/**
 * فحص متطلبات النظام
 */
export const checkRequirements = () => {
  console.log('🔧 فحص متطلبات النظام...');
  
  const requirements = {
    node: false,
    firebase: false,
    env: false,
    dependencies: false
  };

  // فحص Node.js
  if (typeof window !== 'undefined') {
    requirements.node = true;
    console.log('✅ Node.js متوفر');
  }

  // فحص Firebase
  try {
    if (db) {
      requirements.firebase = true;
      console.log('✅ Firebase مُعدّ');
    }
  } catch (error) {
    console.log('❌ Firebase غير مُعدّ:', error.message);
  }

  // فحص متغيرات البيئة
  const requiredEnvVars = [
    'REACT_APP_FIREBASE_API_KEY',
    'REACT_APP_FIREBASE_PROJECT_ID',
    'REACT_APP_FIREBASE_AUTH_DOMAIN'
  ];

  const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingEnvVars.length === 0) {
    requirements.env = true;
    console.log('✅ متغيرات البيئة مُعدّة');
  } else {
    console.log('❌ متغيرات البيئة المفقودة:', missingEnvVars);
  }

  // فحص التبعيات الأساسية
  try {
    require('react');
    require('@mui/material');
    require('firebase/firestore');
    requirements.dependencies = true;
    console.log('✅ التبعيات متوفرة');
  } catch (error) {
    console.log('❌ تبعيات مفقودة:', error.message);
  }

  console.log('\n📋 ملخص المتطلبات:');
  console.log(`Node.js: ${requirements.node ? '✅' : '❌'}`);
  console.log(`Firebase: ${requirements.firebase ? '✅' : '❌'}`);
  console.log(`متغيرات البيئة: ${requirements.env ? '✅' : '❌'}`);
  console.log(`التبعيات: ${requirements.dependencies ? '✅' : '❌'}`);

  return requirements;
};

/**
 * إصلاح سريع للمشاكل الشائعة
 */
export const quickFix = async () => {
  console.log('🔧 بدء الإصلاح السريع...');
  
  try {
    // فحص النظام أولاً
    const systemStatus = await systemCheck();
    
    if (!systemStatus.admin || !systemStatus.students) {
      console.log('🚀 تشغيل الإعداد السريع...');
      const { quickSetup } = await import('./quickSetup');
      await quickSetup();
    } else {
      console.log('✅ النظام يعمل بشكل صحيح');
    }
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح السريع:', error);
  }
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.systemCheck = systemCheck;
window.checkRequirements = checkRequirements;
window.quickFix = quickFix;

export default {
  systemCheck,
  checkRequirements,
  quickFix
};
