/**
 * خدمة رفع الملفات الشاملة - SKILLS WORLD ACADEMY
 * Comprehensive File Upload Service
 * يدعم رفع الفيديوهات والملفات PDF مع التكامل مع Firebase Storage و Supabase Storage
 */

import { storage } from '../firebase/config';
import { supabase } from '../supabase/config';
import {
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  listAll
} from 'firebase/storage';
import toast from 'react-hot-toast';
import { storageService } from './storageService';

/**
 * فئات الملفات المدعومة
 */
export const SUPPORTED_FILE_TYPES = {
  VIDEO: {
    types: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'],
    maxSize: 500 * 1024 * 1024, // 500MB
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']
  },
  PDF: {
    types: ['application/pdf'],
    maxSize: 50 * 1024 * 1024, // 50MB
    extensions: ['.pdf']
  },
  IMAGE: {
    types: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 10 * 1024 * 1024, // 10MB
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
  }
};

/**
 * فئة خدمة رفع الملفات
 */
export class FileUploadService {
  constructor() {
    this.uploadProgress = {};
    this.activeUploads = new Map();
  }

  /**
   * التحقق من صحة الملف
   */
  validateFile(file, fileType) {
    const supportedType = SUPPORTED_FILE_TYPES[fileType];
    
    if (!supportedType) {
      throw new Error('نوع الملف غير مدعوم');
    }

    // التحقق من نوع الملف
    if (!supportedType.types.includes(file.type)) {
      throw new Error(`نوع الملف غير مدعوم. الأنواع المدعومة: ${supportedType.extensions.join(', ')}`);
    }

    // التحقق من حجم الملف
    if (file.size > supportedType.maxSize) {
      const maxSizeMB = Math.round(supportedType.maxSize / (1024 * 1024));
      throw new Error(`حجم الملف كبير جداً. الحد الأقصى: ${maxSizeMB}MB`);
    }

    return true;
  }

  /**
   * توليد اسم ملف فريد
   */
  generateUniqueFileName(originalName, prefix = '') {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.substring(originalName.lastIndexOf('.'));
    return `${prefix}${timestamp}_${randomString}${extension}`;
  }

  /**
   * رفع ملف إلى Firebase Storage
   */
  async uploadToFirebase(file, path, onProgress = null) {
    try {
      const fileName = this.generateUniqueFileName(file.name);
      const fullPath = `${path}/${fileName}`;
      const storageRef = ref(storage, fullPath);

      const uploadTask = uploadBytesResumable(storageRef, file);
      
      // حفظ مرجع المهمة للإلغاء المحتمل
      this.activeUploads.set(fullPath, uploadTask);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            this.uploadProgress[fullPath] = progress;
            
            if (onProgress) {
              onProgress(progress, snapshot);
            }
          },
          (error) => {
            this.activeUploads.delete(fullPath);
            console.error('خطأ في رفع الملف:', error);
            reject(error);
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              this.activeUploads.delete(fullPath);
              
              resolve({
                url: downloadURL,
                path: fullPath,
                fileName: fileName,
                size: file.size,
                type: file.type,
                storage: 'firebase'
              });
            } catch (error) {
              reject(error);
            }
          }
        );
      });
    } catch (error) {
      console.error('خطأ في رفع الملف إلى Firebase:', error);
      throw error;
    }
  }

  /**
   * رفع ملف إلى Supabase Storage
   */
  async uploadToSupabase(file, bucket, path, onProgress = null) {
    try {
      const fileName = this.generateUniqueFileName(file.name);
      const fullPath = `${path}/${fileName}`;

      // محاكاة تقدم الرفع للـ Supabase
      if (onProgress) {
        const progressInterval = setInterval(() => {
          const currentProgress = this.uploadProgress[fullPath] || 0;
          if (currentProgress < 90) {
            this.uploadProgress[fullPath] = currentProgress + 10;
            onProgress(this.uploadProgress[fullPath]);
          } else {
            clearInterval(progressInterval);
          }
        }, 200);
      }

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fullPath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        throw error;
      }

      // الحصول على الرابط العام
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(fullPath);

      if (onProgress) {
        this.uploadProgress[fullPath] = 100;
        onProgress(100);
      }

      return {
        url: urlData.publicUrl,
        path: fullPath,
        fileName: fileName,
        size: file.size,
        type: file.type,
        storage: 'supabase',
        bucket: bucket
      };
    } catch (error) {
      console.error('خطأ في رفع الملف إلى Supabase:', error);
      throw error;
    }
  }

  /**
   * رفع فيديو للكورس
   */
  async uploadCourseVideo(file, courseId, onProgress = null) {
    try {
      // التحقق من صحة الملف
      this.validateFile(file, 'VIDEO');

      // استخدام خدمة التخزين المتقدمة
      const result = await storageService.uploadWithBackup(
        file,
        'videos',
        courseId,
        onProgress
      );

      return result;
    } catch (error) {
      toast.error(`فشل في رفع الفيديو: ${error.message}`);
      throw error;
    }
  }

  /**
   * رفع ملف PDF للكورس
   */
  async uploadCoursePDF(file, courseId, onProgress = null) {
    try {
      // التحقق من صحة الملف
      this.validateFile(file, 'PDF');

      // استخدام خدمة التخزين المتقدمة
      const result = await storageService.uploadWithBackup(
        file,
        'documents',
        courseId,
        onProgress
      );

      return result;
    } catch (error) {
      toast.error(`فشل في رفع ملف PDF: ${error.message}`);
      throw error;
    }
  }

  /**
   * إلغاء رفع الملف
   */
  cancelUpload(filePath) {
    const uploadTask = this.activeUploads.get(filePath);
    if (uploadTask) {
      uploadTask.cancel();
      this.activeUploads.delete(filePath);
      delete this.uploadProgress[filePath];
      return true;
    }
    return false;
  }

  /**
   * حذف ملف من Firebase Storage
   */
  async deleteFromFirebase(filePath) {
    try {
      const fileRef = ref(storage, filePath);
      await deleteObject(fileRef);
      return true;
    } catch (error) {
      console.error('خطأ في حذف الملف من Firebase:', error);
      throw error;
    }
  }

  /**
   * حذف ملف من Supabase Storage
   */
  async deleteFromSupabase(bucket, filePath) {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) {
        throw error;
      }
      return true;
    } catch (error) {
      console.error('خطأ في حذف الملف من Supabase:', error);
      throw error;
    }
  }

  /**
   * الحصول على تقدم الرفع
   */
  getUploadProgress(filePath) {
    return this.uploadProgress[filePath] || 0;
  }

  /**
   * تنظيف الذاكرة
   */
  cleanup() {
    this.uploadProgress = {};
    this.activeUploads.clear();
  }
}

// إنشاء مثيل واحد للخدمة
export const fileUploadService = new FileUploadService();

export default fileUploadService;
