import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  TextField,
  IconButton,
  Badge,
  Divider,
  Card,
  CardContent,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Send,
  Search,
  MoreVert,
  AttachFile,
  EmojiEmotions,
  Person,
  Group,
  Message,
  Archive,
  Delete,
  Reply,
  Forward,
  Star,
  StarBorder,
  WorkspacePremium
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { chatManagementService } from '../../firebase/adminServices';
import toast from 'react-hot-toast';

const ChatSystem = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [broadcastDialog, setBroadcastDialog] = useState(false);
  const [broadcastMessage, setBroadcastMessage] = useState('');
  const messagesEndRef = useRef(null);

  useEffect(() => {
    loadConversations();
  }, []);

  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.id);
    }
  }, [selectedConversation]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadConversations = async () => {
    try {
      setLoading(true);
      const conversationsData = await chatManagementService.getAllConversations();
      setConversations(conversationsData);
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast.error('فشل في تحميل المحادثات');
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async (conversationId) => {
    try {
      const messagesData = await chatManagementService.getMessages(conversationId);
      setMessages(messagesData);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('فشل في تحميل الرسائل');
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;

    try {
      const messageData = {
        senderId: 'admin',
        senderName: 'المدير',
        message: newMessage.trim(),
        isFromAdmin: true
      };

      await chatManagementService.sendMessage(selectedConversation.id, messageData);

      // Add message to local state
      const message = {
        id: Date.now().toString(),
        ...messageData,
        timestamp: new Date(),
        isRead: false
      };

      setMessages(prev => [...prev, message]);
      setNewMessage('');

      // Update conversation last message
      setConversations(prev => prev.map(conv =>
        conv.id === selectedConversation.id
          ? { ...conv, lastMessage: newMessage.trim(), lastMessageTime: new Date() }
          : conv
      ));

      toast.success('تم إرسال الرسالة');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('فشل في إرسال الرسالة');
    }
  };

  const sendBroadcastMessage = async () => {
    if (!broadcastMessage.trim()) {
      toast.error('يرجى كتابة الرسالة');
      return;
    }

    try {
      setLoading(true);

      const messageData = {
        senderId: 'admin',
        senderName: 'المدير',
        message: broadcastMessage.trim(),
        isFromAdmin: true
      };

      await chatManagementService.sendBroadcastMessage(messageData);

      setBroadcastDialog(false);
      setBroadcastMessage('');
      toast.success('تم إرسال الرسالة لجميع الطلاب');
    } catch (error) {
      console.error('Error sending broadcast:', error);
      toast.error('فشل في إرسال الرسالة الجماعية');
    } finally {
      setLoading(false);
    }
  };

  const toggleStarConversation = (conversationId) => {
    setConversations(prev => prev.map(conv =>
      conv.id === conversationId
        ? { ...conv, isStarred: !conv.isStarred }
        : conv
    ));
  };

  const archiveConversation = (conversationId) => {
    setConversations(prev => prev.map(conv =>
      conv.id === conversationId
        ? { ...conv, isArchived: true }
        : conv
    ));
    if (selectedConversation?.id === conversationId) {
      setSelectedConversation(null);
      setMessages([]);
    }
    toast.success('تم أرشفة المحادثة');
  };

  const filteredConversations = conversations.filter(conv =>
    !conv.isArchived &&
    conv.studentName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'منذ قليل';
    } else if (diffInHours < 24) {
      return `منذ ${Math.floor(diffInHours)} ساعة`;
    } else {
      return date.toLocaleDateString('ar-SA');
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          نظام التواصل
        </Typography>
        <Button
          variant="contained"
          startIcon={<Group />}
          onClick={() => setBroadcastDialog(true)}
          sx={{ bgcolor: '#0000FF' }}
        >
          رسالة جماعية
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ height: 'calc(100vh - 200px)' }}>
        {/* Conversations List */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <TextField
                fullWidth
                placeholder="البحث في المحادثات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
                size="small"
              />
            </Box>

            <List sx={{ flexGrow: 1, overflow: 'auto', p: 0 }}>
              {filteredConversations.map((conversation) => (
                <ListItem
                  key={conversation.id}
                  button
                  selected={selectedConversation?.id === conversation.id}
                  onClick={() => setSelectedConversation(conversation)}
                  sx={{
                    borderBottom: 1,
                    borderColor: 'divider',
                    '&.Mui-selected': {
                      bgcolor: 'primary.light',
                      '&:hover': {
                        bgcolor: 'primary.light'
                      }
                    }
                  }}
                >
                  <ListItemAvatar>
                    <Badge badgeContent={conversation.unreadCount} color="error">
                      <Avatar sx={{ bgcolor: '#0000FF' }}>
                        {conversation.studentName.charAt(0)}
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          {conversation.studentName}
                        </Typography>
                        {conversation.isStarred && (
                          <Star sx={{ fontSize: 16, color: 'warning.main' }} />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {conversation.lastMessage}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTime(conversation.lastMessageTime)}
                        </Typography>
                      </Box>
                    }
                  />
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleStarConversation(conversation.id);
                    }}
                  >
                    {conversation.isStarred ? <Star color="warning" /> : <StarBorder />}
                  </IconButton>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Chat Area */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {selectedConversation ? (
              <>
                {/* Chat Header */}
                <Box sx={{
                  p: 2,
                  borderBottom: 1,
                  borderColor: 'divider',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: '#0000FF' }}>
                      {selectedConversation.studentName.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="h6">
                        {selectedConversation.studentName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        متصل الآن
                      </Typography>
                    </Box>
                  </Box>

                  <Box>
                    <IconButton onClick={() => archiveConversation(selectedConversation.id)}>
                      <Archive />
                    </IconButton>
                    <IconButton>
                      <MoreVert />
                    </IconButton>
                  </Box>
                </Box>

                {/* Messages */}
                <Box sx={{
                  flexGrow: 1,
                  overflow: 'auto',
                  p: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1
                }}>
                  {messages.map((message) => (
                    <Box
                      key={message.id}
                      sx={{
                        display: 'flex',
                        justifyContent: message.isFromAdmin ? 'flex-end' : 'flex-start',
                        mb: 1
                      }}
                    >
                      <Paper
                        sx={{
                          p: 2,
                          maxWidth: '70%',
                          bgcolor: message.isFromAdmin ? '#0000FF' : 'grey.100',
                          color: message.isFromAdmin ? 'white' : 'text.primary'
                        }}
                      >
                        <Typography variant="body2">
                          {message.message}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            display: 'block',
                            mt: 0.5,
                            opacity: 0.8
                          }}
                        >
                          {formatTime(message.timestamp)}
                        </Typography>
                      </Paper>
                    </Box>
                  ))}
                  <div ref={messagesEndRef} />
                </Box>

                {/* Message Input */}
                <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                    <TextField
                      fullWidth
                      multiline
                      maxRows={3}
                      placeholder="اكتب رسالتك..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          sendMessage();
                        }
                      }}
                    />
                    <IconButton>
                      <AttachFile />
                    </IconButton>
                    <IconButton>
                      <EmojiEmotions />
                    </IconButton>
                    <IconButton
                      color="primary"
                      onClick={sendMessage}
                      disabled={!newMessage.trim()}
                    >
                      <Send />
                    </IconButton>
                  </Box>
                </Box>
              </>
            ) : (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                flexDirection: 'column',
                gap: 2
              }}>
                <Message sx={{ fontSize: 64, color: 'text.secondary' }} />
                <Typography variant="h6" color="text.secondary">
                  اختر محادثة لبدء التواصل
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Broadcast Message Dialog */}
      <Dialog
        open={broadcastDialog}
        onClose={() => setBroadcastDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>إرسال رسالة جماعية</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              سيتم إرسال هذه الرسالة لجميع الطلاب المسجلين في المنصة
            </Alert>
            <TextField
              fullWidth
              multiline
              rows={4}
              placeholder="اكتب الرسالة الجماعية..."
              value={broadcastMessage}
              onChange={(e) => setBroadcastMessage(e.target.value)}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBroadcastDialog(false)}>
            إلغاء
          </Button>
          <Button
            onClick={sendBroadcastMessage}
            variant="contained"
            disabled={loading || !broadcastMessage.trim()}
          >
            {loading ? <CircularProgress size={20} /> : 'إرسال للجميع'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChatSystem;