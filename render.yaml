services:
  - type: web
    name: alaa-courses-backend
    env: node
    region: frankfurt # أو singapore للشرق الأوسط
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGODB_URI
        sync: false # ستضعها يدوياً في Dashboard
      - key: JWT_SECRET
        generateValue: true
      - key: PORT
        value: 5000
      - key: FRONTEND_URL
        sync: false # ستحددها بعد النشر
    healthCheckPath: /api/health
    
databases:
  - name: alaa-courses-db
    databaseName: alaa-courses
    user: alaa-admin
    region: frankfurt
