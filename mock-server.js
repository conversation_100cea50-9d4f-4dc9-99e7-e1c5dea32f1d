const http = require('http');
const url = require('url');

const PORT = 5000;

// بيانات تجريبية
const mockData = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123!',
    name: 'عل<PERSON><PERSON> عبد الحميد'
  },
  students: [
    {
      _id: 'student1',
      studentCode: '123456',
      name: 'أحم<PERSON> محمد',
      isActive: true,
      enrolledCourses: ['course1']
    }
  ],
  courses: [
    {
      _id: 'course1',
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي من الصفر',
      instructor: 'علاء عبد الحميد',
      isActive: true,
      videos: [
        { id: 1, title: 'مقدمة في التسويق الرقمي' },
        { id: 2, title: 'استراتيجيات التسويق' },
        { id: 3, title: 'وسائل التواصل الاجتماعي' }
      ]
    },
    {
      _id: 'course2',
      title: 'إدارة وسائل التواصل الاجتماعي',
      description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي',
      instructor: 'علاء عبد الحميد',
      isActive: true,
      videos: [
        { id: 1, title: 'مقدمة في وسائل التواصل' },
        { id: 2, title: 'استراتيجية المحتوى' }
      ]
    }
  ]
};

const server = http.createServer((req, res) => {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  
  res.setHeader('Content-Type', 'application/json');

  // Routes
  if (path === '/api/test') {
    res.writeHead(200);
    res.end(JSON.stringify({ message: 'الخادم يعمل!' }));
    return;
  }

  if (path === '/api/admin/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { email, password } = JSON.parse(body);
        console.log('🔐 طلب تسجيل دخول مدير:', { email });

        if (email === mockData.admin.email && password === mockData.admin.password) {
          res.writeHead(200);
          res.end(JSON.stringify({
            token: 'fake-admin-token',
            user: {
              _id: 'admin1',
              name: mockData.admin.name,
              email: mockData.admin.email,
              role: 'admin'
            }
          }));
        } else {
          res.writeHead(401);
          res.end(JSON.stringify({ message: 'بيانات تسجيل الدخول غير صحيحة' }));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ message: 'خطأ في البيانات' }));
      }
    });
    return;
  }

  if (path === '/api/student/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { code } = JSON.parse(body);
        console.log('🎓 طلب تسجيل دخول طالب:', { code });

        const student = mockData.students.find(s => s.studentCode === code);
        if (student) {
          res.writeHead(200);
          res.end(JSON.stringify({
            token: 'fake-student-token',
            user: {
              _id: student._id,
              name: student.name,
              studentCode: student.studentCode,
              role: 'student'
            }
          }));
        } else {
          res.writeHead(401);
          res.end(JSON.stringify({ message: 'كود الطالب غير صحيح' }));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ message: 'خطأ في البيانات' }));
      }
    });
    return;
  }

  if (path === '/api/admin/courses') {
    console.log('📚 طلب جلب الكورسات من المدير');
    res.writeHead(200);
    res.end(JSON.stringify(mockData.courses));
    return;
  }

  if (path === '/api/admin/students') {
    console.log('👥 طلب جلب الطلاب من المدير');
    res.writeHead(200);
    res.end(JSON.stringify(mockData.students));
    return;
  }

  if (path === '/api/admin/dashboard-stats') {
    console.log('📊 طلب إحصائيات لوحة التحكم');
    
    const totalVideos = mockData.courses.reduce((total, course) => total + course.videos.length, 0);
    
    res.writeHead(200);
    res.end(JSON.stringify({
      totalStudents: mockData.students.length,
      activeStudents: mockData.students.filter(s => s.isActive).length,
      totalCourses: mockData.courses.length,
      activeCourses: mockData.courses.filter(c => c.isActive).length,
      totalVideos,
      totalCertificates: 1
    }));
    return;
  }

  if (path === '/api/student/courses') {
    console.log('📚 طلب جلب كورسات الطالب');
    
    const studentCourses = mockData.courses.filter(course => 
      course._id === 'course1'
    ).map(course => ({
      ...course,
      progress: { completedVideos: 2, totalVideos: course.videos.length, progress: 67 }
    }));

    res.writeHead(200);
    res.end(JSON.stringify(studentCourses));
    return;
  }

  // 404
  res.writeHead(404);
  res.end(JSON.stringify({ message: 'الصفحة غير موجودة' }));
});

server.listen(PORT, () => {
  console.log(`🚀 الخادم البسيط يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الموقع متاح على: http://localhost:${PORT}`);
  console.log(`📱 API متاح على: http://localhost:${PORT}/api/test`);
  console.log(`👨‍💼 بيانات المدير: <EMAIL> / Admin123!`);
  console.log(`👨‍🎓 كود طالب تجريبي: 123456`);
});
