import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  ArrowBack,
  Star,
  AccessTime,
  VideoLibrary,
  School,
  Person
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import CourseContentViewer from './CourseContentViewer';
import toast from 'react-hot-toast';

const CourseViewer = ({ course, onBack }) => {
  const { user } = useAuth();
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [openVideoDialog, setOpenVideoDialog] = useState(false);
  const [courseProgress, setCourseProgress] = useState(null);
  const [isEnrolled, setIsEnrolled] = useState(false);

  useEffect(() => {
    if (course) {
      fetchCourseProgress();
    }
  }, [course]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchCourseProgress = async () => {
    try {
      // في التطبيق الحقيقي، ستجلب التقدم من الخادم
      setCourseProgress({
        completedVideos: course.completedVideos || 0,
        totalVideos: course.totalVideos || course.videos?.length || 0,
        progress: course.progress || 0
      });
    } catch (error) {
      console.error('خطأ في جلب تقدم الكورس:', error);
    }
  };

  const handleVideoClick = (video) => {
    setSelectedVideo(video);
    setOpenVideoDialog(true);
  };

  const markVideoAsCompleted = async (videoId) => {
    try {
      // في التطبيق الحقيقي، ستحدث الخادم
      console.log('تم إكمال الفيديو:', videoId);
      
      // تحديث محلي
      const updatedProgress = {
        ...courseProgress,
        completedVideos: courseProgress.completedVideos + 1
      };
      updatedProgress.progress = Math.round((updatedProgress.completedVideos / updatedProgress.totalVideos) * 100);
      setCourseProgress(updatedProgress);
      
      setOpenVideoDialog(false);
    } catch (error) {
      console.error('خطأ في تحديث التقدم:', error);
    }
  };

  if (!course) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6">لم يتم العثور على الكورس</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={onBack} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          {course.title}
        </Typography>
      </Box>

      {/* Course Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {course.description}
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  icon={<Star />} 
                  label={`${course.rating || 4.5} ⭐`} 
                  size="small" 
                  color="warning"
                />
                <Chip 
                  icon={<AccessTime />} 
                  label={course.duration || '8 ساعات'} 
                  size="small" 
                />
                <Chip 
                  icon={<VideoLibrary />} 
                  label={`${course.totalVideos || course.videos?.length || 0} فيديو`} 
                  size="small" 
                />
                <Chip 
                  label={course.level || 'مبتدئ'} 
                  size="small"
                  color={course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'}
                />
              </Box>

              <Typography variant="subtitle2" color="textSecondary">
                المدرب: {course.instructor || 'علاء عبد الحميد'}
              </Typography>
            </Box>
          </Box>

          {/* Progress */}
          {courseProgress && (
            <Box sx={{ mt: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  التقدم: {courseProgress.completedVideos}/{courseProgress.totalVideos}
                </Typography>
                <Typography variant="body2">
                  {courseProgress.progress}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={courseProgress.progress}
                sx={{ height: 8, borderRadius: 4 }}
                color={courseProgress.progress === 100 ? 'success' : 'primary'}
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Course Content */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <VideoLibrary color="primary" />
            محتوى الكورس
          </Typography>

          <Divider sx={{ mb: 2 }} />

          {/* عارض المحتوى الجديد */}
          <CourseContentViewer
            courseId={course.id}
            isEnrolled={isEnrolled}
            onVideoComplete={(videoId) => {
              // تحديث التقدم عند إكمال فيديو
              if (courseProgress) {
                const newCompleted = courseProgress.completedVideos + 1;
                const newProgress = Math.round((newCompleted / courseProgress.totalVideos) * 100);
                setCourseProgress({
                  ...courseProgress,
                  completedVideos: newCompleted,
                  progress: newProgress
                });
              }
            }}
          />

          {/* زر التسجيل في الكورس */}
          {!isEnrolled && (
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<School />}
                onClick={() => {
                  // منطق التسجيل في الكورس
                  setIsEnrolled(true);
                  toast.success('تم التسجيل في الكورس بنجاح!');
                }}
                sx={{
                  background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)'
                  }
                }}
              >
                التسجيل في الكورس
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>


    </Box>
  );
};

export default CourseViewer;
