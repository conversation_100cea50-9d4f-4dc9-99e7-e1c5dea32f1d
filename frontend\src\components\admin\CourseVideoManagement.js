import React, { useState, useEffect } from 'react';
import { useRealtimeCourses, useRealtimeCourseVideos } from '../../hooks/useRealtimeData';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Divider,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  VideoLibrary as VideoIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import courseService from '../../firebase/courseService';

const CourseVideoManagement = () => {
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openCourseDialog, setOpenCourseDialog] = useState(false);
  const [openVideoDialog, setOpenVideoDialog] = useState(false);
  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    category: '',
    level: 'مبتدئ',
    price: 0
  });
  const [videoData, setVideoData] = useState({
    title: '',
    description: '',
    videoUrl: '',
    duration: '',
    order: 1
  });

  // استخدام Real-time Updates للكورسات والفيديوهات
  const { courses: realtimeCourses, loading: coursesLoading } = useRealtimeCourses();
  const { videos: realtimeVideos, loading: videosLoading, refetch: refetchVideos } = useRealtimeCourseVideos(selectedCourse?.id);

  useEffect(() => {
    if (realtimeCourses.length > 0) {
      setCourses(realtimeCourses);
      setLoading(false);
    }
  }, [realtimeCourses]);

  useEffect(() => {
    if (selectedCourse && realtimeVideos) {
      setVideos(realtimeVideos);
    }
  }, [selectedCourse, realtimeVideos]);

  const fetchCourses = async () => {
    // لم تعد هناك حاجة لهذه الدالة مع Real-time Updates
    // ستبقى للتوافق مع الكود الموجود
    if (realtimeCourses.length === 0) {
      try {
        setLoading(true);
        const coursesData = await courseService.getAllCourses();
        setCourses(coursesData);
      } catch (error) {
        console.error('خطأ في جلب الكورسات:', error);
        toast.error('فشل في جلب الكورسات');
      } finally {
        setLoading(false);
      }
    }
  };

  const fetchCourseVideos = async (courseId) => {
    try {
      const videosData = await courseService.getCourseVideos(courseId);
      setVideos(videosData);
    } catch (error) {
      console.error('خطأ في جلب فيديوهات الكورس:', error);
      toast.error('فشل في جلب فيديوهات الكورس');
    }
  };

  const handleCreateCourse = async () => {
    try {
      if (!courseData.title.trim()) {
        toast.error('عنوان الكورس مطلوب');
        return;
      }

      const newCourse = await courseService.createCourse(courseData, 'admin');
      setCourses([newCourse, ...courses]);
      toast.success('تم إنشاء الكورس بنجاح');
      
      setOpenCourseDialog(false);
      setCourseData({ title: '', description: '', category: '', level: 'مبتدئ', price: 0 });
    } catch (error) {
      console.error('خطأ في إنشاء الكورس:', error);
      toast.error('فشل في إنشاء الكورس');
    }
  };

  const handleAddVideo = async () => {
    try {
      if (!videoData.title.trim() || !videoData.videoUrl.trim()) {
        toast.error('عنوان الفيديو ورابط الفيديو مطلوبان');
        return;
      }

      const newVideo = await courseService.addVideoToCourse(selectedCourse.id, videoData, 'admin');
      setVideos([...videos, newVideo]);
      toast.success('تم إضافة الفيديو بنجاح');
      
      setOpenVideoDialog(false);
      setVideoData({ title: '', description: '', videoUrl: '', duration: '', order: videos.length + 1 });
    } catch (error) {
      console.error('خطأ في إضافة الفيديو:', error);
      toast.error('فشل في إضافة الفيديو');
    }
  };

  const handleToggleCoursePublication = async (courseId, currentStatus) => {
    try {
      await courseService.toggleCoursePublication(courseId, !currentStatus, 'admin');
      setCourses(courses.map(c => 
        c.id === courseId ? { ...c, isPublished: !currentStatus } : c
      ));
      toast.success(`تم ${!currentStatus ? 'نشر' : 'إلغاء نشر'} الكورس`);
    } catch (error) {
      console.error('خطأ في تغيير حالة النشر:', error);
      toast.error('فشل في تغيير حالة النشر');
    }
  };

  return (
    <Box>
      <Grid container spacing={3}>
        {/* قائمة الكورسات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">الكورسات</Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setOpenCourseDialog(true)}
                >
                  إضافة كورس
                </Button>
              </Box>

              <List>
                {courses.map((course) => (
                  <ListItem
                    key={course.id}
                    button
                    selected={selectedCourse?.id === course.id}
                    onClick={() => setSelectedCourse(course)}
                  >
                    <ListItemText
                      primary={course.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {course.category} • {course.level}
                          </Typography>
                          <Box mt={1}>
                            <Chip
                              label={course.isPublished ? 'منشور' : 'غير منشور'}
                              color={course.isPublished ? 'success' : 'default'}
                              size="small"
                            />
                          </Box>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleToggleCoursePublication(course.id, course.isPublished)}
                      >
                        {course.isPublished ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>

              {courses.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    لا توجد كورسات حتى الآن
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* فيديوهات الكورس المحدد */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              {selectedCourse ? (
                <>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                      فيديوهات: {selectedCourse.title}
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<VideoIcon />}
                      onClick={() => setOpenVideoDialog(true)}
                    >
                      إضافة فيديو
                    </Button>
                  </Box>

                  <List>
                    {videos.map((video, index) => (
                      <ListItem key={video.id}>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center">
                              <PlayIcon sx={{ mr: 1, color: 'primary.main' }} />
                              {video.title}
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {video.description}
                              </Typography>
                              {video.duration && (
                                <Typography variant="caption" color="text.secondary">
                                  المدة: {video.duration}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>

                  {videos.length === 0 && (
                    <Box textAlign="center" py={4}>
                      <Typography variant="body2" color="text.secondary">
                        لا توجد فيديوهات في هذا الكورس
                      </Typography>
                    </Box>
                  )}
                </>
              ) : (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    اختر كورساً لعرض فيديوهاته
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Dialog إضافة كورس */}
      <Dialog open={openCourseDialog} onClose={() => setOpenCourseDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة كورس جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="عنوان الكورس *"
                value={courseData.title}
                onChange={(e) => setCourseData({ ...courseData, title: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="وصف الكورس"
                value={courseData.description}
                onChange={(e) => setCourseData({ ...courseData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="التصنيف"
                value={courseData.category}
                onChange={(e) => setCourseData({ ...courseData, category: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                select
                label="المستوى"
                value={courseData.level}
                onChange={(e) => setCourseData({ ...courseData, level: e.target.value })}
                SelectProps={{ native: true }}
              >
                <option value="مبتدئ">مبتدئ</option>
                <option value="متوسط">متوسط</option>
                <option value="متقدم">متقدم</option>
              </TextField>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCourseDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleCreateCourse}>
            إنشاء الكورس
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog إضافة فيديو */}
      <Dialog open={openVideoDialog} onClose={() => setOpenVideoDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة فيديو جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="عنوان الفيديو *"
                value={videoData.title}
                onChange={(e) => setVideoData({ ...videoData, title: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="رابط الفيديو *"
                value={videoData.videoUrl}
                onChange={(e) => setVideoData({ ...videoData, videoUrl: e.target.value })}
                placeholder="https://www.youtube.com/watch?v=..."
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="وصف الفيديو"
                value={videoData.description}
                onChange={(e) => setVideoData({ ...videoData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="مدة الفيديو"
                value={videoData.duration}
                onChange={(e) => setVideoData({ ...videoData, duration: e.target.value })}
                placeholder="مثال: 15:30"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                type="number"
                label="ترتيب الفيديو"
                value={videoData.order}
                onChange={(e) => setVideoData({ ...videoData, order: parseInt(e.target.value) })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenVideoDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleAddVideo}>
            إضافة الفيديو
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseVideoManagement;
