import { 
  doc, 
  setDoc, 
  addDoc,
  collection,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../firebase/config';

// إنشاء بيانات حقيقية فورية

export const createRealStudentData = async () => {
  try {
    console.log('🚀 إنشاء بيانات حقيقية للطلاب...');

    const now = new Date();

    // إنشاء تقدم حقيقي للطالب 1
    await setDoc(doc(db, 'userProgress', 'student1_course1'), {
      userId: 'student1',
      courseId: 'course1',
      completedVideos: 8,
      totalVideos: 12,
      progress: 67,
      lastWatchedVideo: 'video8',
      totalWatchTime: 240,
      isCompleted: false,
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    await setDoc(doc(db, 'userProgress', 'student1_course2'), {
      userId: 'student1',
      courseId: 'course2',
      completedVideos: 3,
      totalVideos: 10,
      progress: 30,
      lastWatchedVideo: 'video3',
      totalWatchTime: 90,
      isCompleted: false,
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // إنشاء تقدم للطالب 2
    await setDoc(doc(db, 'userProgress', 'student2_course1'), {
      userId: 'student2',
      courseId: 'course1',
      completedVideos: 5,
      totalVideos: 12,
      progress: 42,
      lastWatchedVideo: 'video5',
      totalWatchTime: 150,
      isCompleted: false,
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // إنشاء نشاطات حقيقية للطالب 1
    const activities1 = [
      {
        userId: 'student1',
        action: 'video_completed',
        details: {
          description: 'تم إكمال فيديو: مقدمة في التسويق الرقمي',
          courseId: 'course1',
          videoId: 'video8',
          courseTitle: 'أساسيات التسويق الرقمي',
          videoTitle: 'مقدمة في التسويق الرقمي'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        userId: 'student1',
        action: 'video_started',
        details: {
          description: 'بدء مشاهدة: استراتيجيات التسويق المتقدمة',
          courseId: 'course1',
          videoId: 'video9',
          courseTitle: 'أساسيات التسويق الرقمي',
          videoTitle: 'استراتيجيات التسويق المتقدمة'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString()
      },
      {
        userId: 'student1',
        action: 'course_enrolled',
        details: {
          description: 'التسجيل في دورة: إدارة وسائل التواصل الاجتماعي',
          courseId: 'course2',
          courseTitle: 'إدارة وسائل التواصل الاجتماعي'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    // إنشاء نشاطات للطالب 2
    const activities2 = [
      {
        userId: 'student2',
        action: 'login',
        details: {
          description: 'تسجيل الدخول إلى المنصة'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 30 * 60 * 1000).toISOString()
      },
      {
        userId: 'student2',
        action: 'video_completed',
        details: {
          description: 'تم إكمال فيديو: أساسيات وسائل التواصل',
          courseId: 'course1',
          videoId: 'video5',
          courseTitle: 'أساسيات التسويق الرقمي',
          videoTitle: 'أساسيات وسائل التواصل'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 3 * 60 * 60 * 1000).toISOString()
      },
      {
        userId: 'student2',
        action: 'profile_updated',
        details: {
          description: 'تحديث الملف الشخصي'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString()
      }
    ];

    // إضافة النشاطات
    const allActivities = [...activities1, ...activities2];
    for (const activity of allActivities) {
      await addDoc(collection(db, 'userActivity'), activity);
    }

    console.log('✅ تم إنشاء البيانات الحقيقية بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الحقيقية:', error);
    throw error;
  }
};

export const forceRefreshData = async (userId) => {
  try {
    console.log('🔄 إجبار تحديث البيانات...', userId);
    
    // إنشاء نشاط جديد لإجبار التحديث
    await addDoc(collection(db, 'userActivity'), {
      userId,
      action: 'data_refresh',
      details: {
        description: 'تحديث البيانات'
      },
      timestamp: serverTimestamp(),
      createdAt: new Date().toISOString()
    });

    console.log('✅ تم إجبار تحديث البيانات');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تحديث البيانات:', error);
    return false;
  }
};

export default {
  createRealStudentData,
  forceRefreshData
};
