const mongoose = require('mongoose');

const lessonProgressSchema = new mongoose.Schema({
  lessonId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  watchedDuration: {
    type: Number,
    default: 0 // بالثواني
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  lastWatchedAt: {
    type: Date,
    default: Date.now
  }
});

const progressSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  lessonsProgress: [lessonProgressSchema],
  overallProgress: {
    type: Number,
    default: 0, // نسبة مئوية من 0 إلى 100
    min: 0,
    max: 100
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  completedAt: {
    type: Date
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  totalWatchTime: {
    type: Number,
    default: 0 // إجمالي وقت المشاهدة بالثواني
  },
  currentLesson: {
    type: mongoose.Schema.Types.ObjectId // آخر درس تم مشاهدته
  }
}, {
  timestamps: true
});

// Calculate overall progress before saving
progressSchema.pre('save', function(next) {
  if (this.lessonsProgress && this.lessonsProgress.length > 0) {
    const completedLessons = this.lessonsProgress.filter(lesson => lesson.isCompleted).length;
    this.overallProgress = Math.round((completedLessons / this.lessonsProgress.length) * 100);
    
    // Check if course is completed
    if (this.overallProgress === 100 && !this.isCompleted) {
      this.isCompleted = true;
      this.completedAt = new Date();
    }
    
    // Calculate total watch time
    this.totalWatchTime = this.lessonsProgress.reduce((total, lesson) => total + lesson.watchedDuration, 0);
  }
  next();
});

// Compound index for better performance
progressSchema.index({ student: 1, course: 1 }, { unique: true });
progressSchema.index({ student: 1, lastAccessedAt: -1 });
progressSchema.index({ course: 1, isCompleted: 1 });

module.exports = mongoose.model('Progress', progressSchema);
