<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0000FF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4169E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366F1;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
    
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="4" flood-color="rgba(0,0,0,0.5)"/>
    </filter>
    
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)" rx="20"/>
  
  <!-- Decorative circles -->
  <circle cx="1100" cy="100" r="100" fill="rgba(255,215,0,0.1)"/>
  <circle cx="100" cy="530" r="75" fill="rgba(255,215,0,0.1)"/>
  <circle cx="150" cy="315" r="50" fill="rgba(255,215,0,0.1)"/>
  
  <!-- Logo circle -->
  <circle cx="600" cy="180" r="60" fill="#FFD700" stroke="rgba(255,255,255,0.3)" stroke-width="4" filter="url(#glow)"/>
  <text x="600" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#0000FF">🎓</text>
  
  <!-- Main title -->
  <text x="600" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="900" fill="url(#titleGradient)" filter="url(#shadow)">
    SKILLS WORLD ACADEMY
  </text>
  
  <!-- Arabic subtitle -->
  <text x="600" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="600" fill="white" filter="url(#shadow)">
    منصة التعلم والتطوير المهني العالمية
  </text>
  
  <!-- Description -->
  <text x="600" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="400" fill="rgba(255,255,255,0.9)" filter="url(#shadow)">
    تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية
  </text>
  
  <!-- Features -->
  <g transform="translate(300, 420)">
    <!-- Feature 1 -->
    <rect x="0" y="0" width="180" height="40" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
    <circle cx="20" cy="20" r="12" fill="#FFD700"/>
    <text x="20" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0000FF">📚</text>
    <text x="45" y="26" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="white">كورسات متنوعة</text>
    
    <!-- Feature 2 -->
    <rect x="200" y="0" width="180" height="40" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
    <circle cx="220" cy="20" r="12" fill="#FFD700"/>
    <text x="220" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0000FF">🎓</text>
    <text x="245" y="26" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="white">شهادات معتمدة</text>
    
    <!-- Feature 3 -->
    <rect x="400" y="0" width="180" height="40" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
    <circle cx="420" cy="20" r="12" fill="#FFD700"/>
    <text x="420" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0000FF">👨‍🏫</text>
    <text x="445" y="26" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="white">مدربين خبراء</text>
  </g>
  
  <!-- URL -->
  <rect x="400" y="520" width="400" height="35" rx="17" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)"/>
  <text x="600" y="542" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="500" fill="white">
    marketwise-academy-qhizq.web.app
  </text>
  
  <!-- Additional decorative elements -->
  <path d="M50 50 L150 50 L100 150 Z" fill="rgba(255,215,0,0.05)"/>
  <path d="M1050 480 L1150 480 L1100 580 Z" fill="rgba(255,215,0,0.05)"/>
</svg>
