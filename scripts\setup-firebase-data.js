#!/usr/bin/env node

// Setup Firebase data script
const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = {
  "type": "service_account",
  "project_id": "marketwise-academy-qhizq",
  "private_key_id": "dummy",
  "private_key": "-----BEGIN PRIVATE KEY-----\nDUMMY_KEY\n-----END PRIVATE KEY-----\n",
  "client_email": "<EMAIL>",
  "client_id": "dummy",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
};

// For demo purposes, we'll use a simpler approach
console.log('🔧 إعداد البيانات الافتراضية لـ Firebase...');

const defaultData = {
  // Admin user
  admin: {
    name: 'علاء عبد الحميد',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // Categories
  categories: [
    {
      name: 'التسويق الرقمي',
      description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
      icon: 'digital-marketing',
      color: '#2196F3',
      order: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'وسائل التواصل الاجتماعي',
      description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
      icon: 'social-media',
      color: '#4CAF50',
      order: 2,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'التسويق بالمحتوى',
      description: 'إنشاء وتسويق المحتوى الفعال',
      icon: 'content-marketing',
      color: '#FF9800',
      order: 3,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'التجارة الإلكترونية',
      description: 'بناء وإدارة المتاجر الإلكترونية',
      icon: 'ecommerce',
      color: '#9C27B0',
      order: 4,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],

  // Students
  students: [
    {
      name: 'أحمد محمد علي',
      studentCode: '123456',
      role: 'student',
      isActive: true,
      enrolledCourses: [],
      progress: {},
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'فاطمة علي حسن',
      studentCode: '789012',
      role: 'student',
      isActive: true,
      enrolledCourses: [],
      progress: {},
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'محمد عبدالله أحمد',
      studentCode: '345678',
      role: 'student',
      isActive: true,
      enrolledCourses: [],
      progress: {},
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],

  // Sample course
  courses: [
    {
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف مع أحدث الاستراتيجيات والأدوات',
      shortDescription: 'كورس شامل في أساسيات التسويق الرقمي',
      instructor: 'علاء عبد الحميد',
      thumbnail: '/images/digital-marketing-basics.jpg',
      level: 'مبتدئ',
      language: 'العربية',
      duration: '8 ساعات',
      totalVideos: 24,
      price: 299,
      originalPrice: 499,
      discount: 40,
      tags: ['تسويق', 'رقمي', 'مبتدئ', 'أساسيات'],
      requirements: [
        'لا توجد متطلبات مسبقة',
        'جهاز كمبيوتر أو هاتف ذكي',
        'اتصال بالإنترنت'
      ],
      whatYouWillLearn: [
        'فهم أساسيات التسويق الرقمي',
        'إنشاء استراتيجية تسويقية فعالة',
        'استخدام وسائل التواصل الاجتماعي للتسويق',
        'تحليل البيانات وقياس النتائج',
        'إنشاء حملات إعلانية ناجحة'
      ],
      lessons: [
        {
          title: 'مقدمة في التسويق الرقمي',
          description: 'تعرف على أساسيات التسويق الرقمي وأهميته',
          duration: 900, // 15 minutes
          order: 1,
          isPreview: true
        },
        {
          title: 'الفرق بين التسويق التقليدي والرقمي',
          description: 'مقارنة بين أساليب التسويق التقليدية والرقمية',
          duration: 765, // 12:45
          order: 2,
          isPreview: false
        },
        {
          title: 'أدوات التسويق الرقمي الأساسية',
          description: 'تعرف على أهم الأدوات المستخدمة في التسويق الرقمي',
          duration: 1080, // 18 minutes
          order: 3,
          isPreview: false
        }
      ],
      isActive: true,
      rating: 4.8,
      reviewsCount: 156,
      enrolledStudents: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
};

// Export data for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = defaultData;
}

// Function to setup data (will be called from frontend)
const setupFirebaseData = async () => {
  try {
    console.log('🔧 بدء إعداد البيانات الافتراضية...');
    
    // This will be implemented when Firebase is properly configured
    console.log('📊 البيانات الافتراضية جاهزة للإضافة:');
    console.log(`👨‍💼 المدير: ${defaultData.admin.name}`);
    console.log(`📂 الأقسام: ${defaultData.categories.length} قسم`);
    console.log(`👨‍🎓 الطلاب: ${defaultData.students.length} طالب`);
    console.log(`📚 الكورسات: ${defaultData.courses.length} كورس`);
    
    return defaultData;
  } catch (error) {
    console.error('❌ خطأ في إعداد البيانات:', error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  setupFirebaseData();
}

console.log('✅ سكريبت إعداد البيانات جاهز!');
console.log('📋 لتشغيل الإعداد:');
console.log('   1. تأكد من تفعيل Firestore');
console.log('   2. قم بتشغيل التطبيق');
console.log('   3. ستتم إضافة البيانات تلقائياً');
