/**
 * اختبار إصلاحات قاعدة البيانات
 * Database Schema Fixes Test Script
 */

import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase
const supabaseUrl = 'https://auwpeiicfwcysoexoogf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1d3BlaWljZndjeXNvZXhvb2dmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzA3MzAsImV4cCI6MjA2Nzc0NjczMH0.3twZ1c6M2EyBoBe66PjmwsUwvzK0nnV89Bt3yLcGBjY';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * اختبار جلب التسجيلات مع العلاقات
 */
async function testEnrollmentsQuery() {
  console.log('🧪 اختبار استعلام التسجيلات...');
  
  try {
    const { data, error } = await supabase
      .from('enrollments')
      .select(`
        *,
        student:users!enrollments_user_id_fkey(id, name, email, student_code),
        course:courses!enrollments_course_id_fkey(id, title, instructor)
      `)
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('❌ خطأ في استعلام التسجيلات:', error);
      return false;
    }

    console.log('✅ استعلام التسجيلات يعمل بنجاح');
    console.log('📊 عدد التسجيلات:', data.length);
    return true;
  } catch (error) {
    console.error('❌ خطأ في اختبار التسجيلات:', error);
    return false;
  }
}

/**
 * اختبار جلب الأسئلة الشائعة مع الأولوية
 */
async function testFAQsQuery() {
  console.log('🧪 اختبار استعلام الأسئلة الشائعة...');
  
  try {
    const { data, error } = await supabase
      .from('faqs')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('❌ خطأ في استعلام الأسئلة الشائعة:', error);
      return false;
    }

    console.log('✅ استعلام الأسئلة الشائعة يعمل بنجاح');
    console.log('📊 عدد الأسئلة:', data.length);
    
    // التحقق من وجود عمود الأولوية
    if (data.length > 0 && data[0].priority !== undefined) {
      console.log('✅ عمود الأولوية موجود ويعمل');
    }
    
    return true;
  } catch (error) {
    console.error('❌ خطأ في اختبار الأسئلة الشائعة:', error);
    return false;
  }
}

/**
 * اختبار Storage Buckets
 */
async function testStorageBuckets() {
  console.log('🧪 اختبار Storage Buckets...');
  
  try {
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('❌ خطأ في جلب Storage Buckets:', error);
      return false;
    }

    const requiredBuckets = ['course-videos', 'course-documents', 'course-images', 'certificates'];
    const existingBuckets = data.map(bucket => bucket.id);
    
    console.log('📦 Buckets الموجودة:', existingBuckets);
    
    const missingBuckets = requiredBuckets.filter(bucket => !existingBuckets.includes(bucket));
    
    if (missingBuckets.length === 0) {
      console.log('✅ جميع Storage Buckets المطلوبة موجودة');
      return true;
    } else {
      console.error('❌ Storage Buckets مفقودة:', missingBuckets);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار Storage Buckets:', error);
    return false;
  }
}

/**
 * اختبار شامل لجميع الإصلاحات
 */
async function runAllTests() {
  console.log('🚀 بدء اختبار إصلاحات قاعدة البيانات...');
  console.log('=' * 50);
  
  const results = {
    enrollments: await testEnrollmentsQuery(),
    faqs: await testFAQsQuery(),
    storage: await testStorageBuckets()
  };
  
  console.log('=' * 50);
  console.log('📋 نتائج الاختبارات:');
  console.log('- التسجيلات:', results.enrollments ? '✅ نجح' : '❌ فشل');
  console.log('- الأسئلة الشائعة:', results.faqs ? '✅ نجح' : '❌ فشل');
  console.log('- Storage Buckets:', results.storage ? '✅ نجح' : '❌ فشل');
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('🎉 جميع الاختبارات نجحت! قاعدة البيانات جاهزة للاستخدام');
  } else {
    console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه');
  }
  
  return allPassed;
}

// تشغيل الاختبارات
if (typeof window === 'undefined') {
  // Node.js environment
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
} else {
  // Browser environment
  window.testDatabaseFixes = runAllTests;
}

export { runAllTests, testEnrollmentsQuery, testFAQsQuery, testStorageBuckets };
