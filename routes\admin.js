const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Course = require('../models/Course');
const Category = require('../models/Category');
const Certificate = require('../models/Certificate');
const Progress = require('../models/Progress');
const { adminAuth } = require('../middleware/auth');

const router = express.Router();

// جميع المسارات تتطلب صلاحيات المدير
router.use(adminAuth);

// إحصائيات لوحة التحكم
router.get('/dashboard/stats', async (req, res) => {
  try {
    const totalStudents = await User.countDocuments({ role: 'student' });
    const totalCourses = await Course.countDocuments();
    const totalCategories = await Category.countDocuments();
    const totalCertificates = await Certificate.countDocuments();
    
    const activeStudents = await User.countDocuments({ 
      role: 'student', 
      lastLogin: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } 
    });

    const publishedCourses = await Course.countDocuments({ isPublished: true });
    
    res.json({
      totalStudents,
      totalCourses,
      totalCategories,
      totalCertificates,
      activeStudents,
      publishedCourses
    });
  } catch (error) {
    console.error('خطأ في الحصول على الإحصائيات:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إدارة الطلاب
// إنشاء طالب جديد
router.post('/students', [
  body('name').notEmpty().withMessage('اسم الطالب مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name } = req.body;
    
    // إنشاء كود للطالب
    const studentCode = await User.generateStudentCode();
    
    const student = new User({
      name,
      role: 'student',
      studentCode,
      createdBy: req.user.id
    });

    await student.save();

    res.status(201).json({
      message: 'تم إنشاء الطالب بنجاح',
      student: {
        id: student._id,
        name: student.name,
        studentCode: student.studentCode,
        createdAt: student.createdAt
      }
    });
  } catch (error) {
    console.error('خطأ في إنشاء الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على جميع الطلاب
router.get('/students', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const students = await User.find({ role: 'student' })
      .select('-password')
      .populate('enrolledCourses.courseId', 'title')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments({ role: 'student' });

    res.json({
      students,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('خطأ في الحصول على الطلاب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث بيانات الطالب
router.put('/students/:id', [
  body('name').optional().notEmpty().withMessage('اسم الطالب لا يمكن أن يكون فارغاً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name, isActive } = req.body;
    const student = await User.findOne({ _id: req.params.id, role: 'student' });

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    if (name) student.name = name;
    if (typeof isActive === 'boolean') student.isActive = isActive;

    await student.save();

    res.json({
      message: 'تم تحديث بيانات الطالب بنجاح',
      student: {
        id: student._id,
        name: student.name,
        studentCode: student.studentCode,
        isActive: student.isActive
      }
    });
  } catch (error) {
    console.error('خطأ في تحديث الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// حذف طالب
router.delete('/students/:id', async (req, res) => {
  try {
    const student = await User.findOne({ _id: req.params.id, role: 'student' });

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    await User.findByIdAndDelete(req.params.id);
    await Progress.deleteMany({ student: req.params.id });

    res.json({ message: 'تم حذف الطالب بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إدارة الأقسام
// إنشاء قسم جديد
router.post('/categories', [
  body('name').notEmpty().withMessage('اسم القسم مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name, description, icon, color, order } = req.body;

    const category = new Category({
      name,
      description,
      icon,
      color,
      order,
      createdBy: req.user.id
    });

    await category.save();

    res.status(201).json({
      message: 'تم إنشاء القسم بنجاح',
      category
    });
  } catch (error) {
    console.error('خطأ في إنشاء القسم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على جميع الأقسام
router.get('/categories', async (req, res) => {
  try {
    const categories = await Category.find()
      .populate('createdBy', 'name')
      .sort({ order: 1, createdAt: -1 });

    res.json({ categories });
  } catch (error) {
    console.error('خطأ في الحصول على الأقسام:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث قسم
router.put('/categories/:id', [
  body('name').optional().notEmpty().withMessage('اسم القسم لا يمكن أن يكون فارغاً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const category = await Category.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!category) {
      return res.status(404).json({ message: 'القسم غير موجود' });
    }

    res.json({
      message: 'تم تحديث القسم بنجاح',
      category
    });
  } catch (error) {
    console.error('خطأ في تحديث القسم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// حذف قسم
router.delete('/categories/:id', async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({ message: 'القسم غير موجود' });
    }

    // التحقق من وجود كورسات في هذا القسم
    const coursesCount = await Course.countDocuments({ category: req.params.id });
    if (coursesCount > 0) {
      return res.status(400).json({ 
        message: 'لا يمكن حذف القسم لأنه يحتوي على كورسات' 
      });
    }

    await Category.findByIdAndDelete(req.params.id);

    res.json({ message: 'تم حذف القسم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف القسم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
