/**
 * معالج الأخطاء الشامل للتطبيق
 */

// معالجة أخطاء Supabase
export const handleSupabaseError = (error, operation = 'عملية غير محددة') => {
  console.error(`❌ خطأ في ${operation}:`, error);
  
  // أخطاء شائعة
  const errorMessages = {
    'PGRST116': 'لا توجد بيانات مطابقة',
    'PGRST301': 'غير مصرح بالوصول',
    'PGRST204': 'لا توجد بيانات',
    '23505': 'البيانات موجودة مسبقاً',
    '23503': 'مرجع غير صحيح',
    '42501': 'غير مصرح بالعملية',
    '42P01': 'الجدول غير موجود'
  };
  
  const userMessage = errorMessages[error.code] || error.message || 'حدث خطأ غير متوقع';
  
  return {
    success: false,
    error: error,
    userMessage: userMessage,
    code: error.code,
    details: error.details
  };
};

// معالجة أخطاء Firebase
export const handleFirebaseError = (error, operation = 'عملية غير محددة') => {
  console.error(`❌ خطأ Firebase في ${operation}:`, error);
  
  const errorMessages = {
    'permission-denied': 'غير مصرح بالوصول',
    'not-found': 'البيانات غير موجودة',
    'already-exists': 'البيانات موجودة مسبقاً',
    'invalid-argument': 'بيانات غير صحيحة',
    'deadline-exceeded': 'انتهت مهلة العملية',
    'unavailable': 'الخدمة غير متاحة مؤقتاً'
  };
  
  const userMessage = errorMessages[error.code] || error.message || 'حدث خطأ غير متوقع';
  
  return {
    success: false,
    error: error,
    userMessage: userMessage,
    code: error.code
  };
};

// معالجة أخطاء الشبكة
export const handleNetworkError = (error, operation = 'عملية غير محددة') => {
  console.error(`❌ خطأ شبكة في ${operation}:`, error);
  
  if (!navigator.onLine) {
    return {
      success: false,
      error: error,
      userMessage: 'لا يوجد اتصال بالإنترنت',
      code: 'OFFLINE'
    };
  }
  
  return {
    success: false,
    error: error,
    userMessage: 'خطأ في الاتصال بالخادم',
    code: 'NETWORK_ERROR'
  };
};

// دالة معالجة شاملة
export const handleError = (error, operation = 'عملية غير محددة', context = 'general') => {
  // تسجيل الخطأ للمراقبة
  console.group(`🚨 خطأ في ${operation}`);
  console.error('السياق:', context);
  console.error('التفاصيل:', error);
  console.groupEnd();
  
  // تحديد نوع الخطأ
  if (error?.code?.startsWith('PGRST') || error?.message?.includes('supabase')) {
    return handleSupabaseError(error, operation);
  }
  
  if (error?.code?.includes('firebase') || error?.message?.includes('firestore')) {
    return handleFirebaseError(error, operation);
  }
  
  if (error?.name === 'NetworkError' || !navigator.onLine) {
    return handleNetworkError(error, operation);
  }
  
  // خطأ عام
  return {
    success: false,
    error: error,
    userMessage: error.message || 'حدث خطأ غير متوقع',
    code: 'UNKNOWN_ERROR'
  };
};

// دالة retry للعمليات الفاشلة
export const retryOperation = async (operation, maxRetries = 3, initialDelay = 1000) => {
  let lastError;
  let currentDelay = initialDelay;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 محاولة ${attempt}/${maxRetries}...`);
      const result = await operation();
      console.log(`✅ نجحت المحاولة ${attempt}`);
      return result;
    } catch (error) {
      lastError = error;
      console.warn(`❌ فشلت المحاولة ${attempt}:`, error.message);

      if (attempt < maxRetries) {
        console.log(`⏳ انتظار ${currentDelay}ms قبل المحاولة التالية...`);
        await new Promise(resolve => setTimeout(resolve, currentDelay));
        currentDelay *= 2; // زيادة التأخير تدريجياً
      }
    }
  }

  throw lastError;
};

// دالة للتحقق من حالة الاتصال
export const checkConnectionStatus = async () => {
  try {
    // اختبار بسيط للاتصال
    await fetch('https://www.google.com/favicon.ico', {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache'
    });
    return true;
  } catch (error) {
    return false;
  }
};

// معالج الأخطاء العام للتطبيق
export const setupGlobalErrorHandlers = () => {
  // معالجة الأخطاء غير المعالجة
  window.addEventListener('unhandledrejection', (event) => {
    console.error('❌ Unhandled Promise Rejection:', event.reason);
    
    // منع ظهور الخطأ في وحدة التحكم
    event.preventDefault();
    
    // تسجيل الخطأ
    const errorInfo = handleError(event.reason, 'Promise غير معالج', 'global');
    console.warn('🔧 تم معالجة الخطأ:', errorInfo.userMessage);
  });
  
  // معالجة الأخطاء العامة
  window.addEventListener('error', (event) => {
    console.error('❌ Global Error:', event.error);
    
    const errorInfo = handleError(event.error, 'خطأ عام', 'global');
    console.warn('🔧 تم معالجة الخطأ:', errorInfo.userMessage);
  });
  
  console.log('✅ تم إعداد معالجات الأخطاء العامة');
};

// تصدير الدوال
const errorHandler = {
  handleSupabaseError,
  handleFirebaseError,
  handleNetworkError,
  handleError,
  retryOperation,
  checkConnectionStatus,
  setupGlobalErrorHandlers
};

export default errorHandler;
