import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar
} from '@mui/material';
import {
  Close,
  Person,
  Timeline,
  Mouse,
  TouchApp,
  Visibility,
  Schedule,
  TrendingUp,
  School,
  PlayArrow
} from '@mui/icons-material';
import analyticsService from '../../services/analyticsService';
import interactionTracker from '../../services/interactionTracker';

const UserInteractionDetails = ({ open, onClose, userId, userName }) => {
  const [userAnalysis, setUserAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    if (open && userId) {
      fetchUserAnalysis();
    }
  }, [open, userId, timeRange]);

  const fetchUserAnalysis = async () => {
    try {
      setLoading(true);
      const analysis = await analyticsService.getUserBehaviorAnalysis(userId, timeRange);
      setUserAnalysis(analysis);
    } catch (error) {
      console.error('❌ خطأ في جلب تحليل المستخدم:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (ms) => {
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    return `${hours}س ${minutes}د`;
  };

  const getInteractionIcon = (type) => {
    const icons = {
      'click': <Mouse />,
      'scroll': <Timeline />,
      'video_interaction': <PlayArrow />,
      'course_interaction': <School />,
      'view': <Visibility />,
      'time_spent': <Schedule />
    };
    return icons[type] || <TouchApp />;
  };

  const getEngagementColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  if (!userAnalysis && !loading) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Person color="primary" />
        <Box>
          <Typography variant="h6">تحليل تفاعلات المستخدم</Typography>
          <Typography variant="body2" color="text.secondary">
            {userName} - آخر {timeRange === '7d' ? '7 أيام' : timeRange === '30d' ? '30 يوم' : 'يوم واحد'}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <LinearProgress sx={{ width: '50%' }} />
          </Box>
        ) : userAnalysis ? (
          <Grid container spacing={3}>
            {/* ملخص النشاط */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    ملخص النشاط
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="primary">
                          {userAnalysis.activitySummary?.totalSessions || 0}
                        </Typography>
                        <Typography variant="body2">الجلسات</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="success.main">
                          {userAnalysis.activitySummary?.totalInteractions || 0}
                        </Typography>
                        <Typography variant="body2">التفاعلات</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="info.main">
                          {formatDuration(userAnalysis.activitySummary?.totalTime || 0)}
                        </Typography>
                        <Typography variant="body2">الوقت الإجمالي</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color={getEngagementColor(userAnalysis.engagementScore)}>
                          {userAnalysis.engagementScore || 0}%
                        </Typography>
                        <Typography variant="body2">نقاط التفاعل</Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* نمط التعلم */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    نمط التعلم
                  </Typography>
                  {userAnalysis.learningPattern && (
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <Schedule />
                        </ListItemIcon>
                        <ListItemText
                          primary="أوقات النشاط المفضلة"
                          secondary={userAnalysis.learningPattern.preferredHours?.join(', ') || 'غير محدد'}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Timeline />
                        </ListItemIcon>
                        <ListItemText
                          primary="متوسط مدة الجلسة"
                          secondary={formatDuration(userAnalysis.learningPattern.averageSessionDuration || 0)}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <TrendingUp />
                        </ListItemIcon>
                        <ListItemText
                          primary="معدل التقدم"
                          secondary={`${userAnalysis.learningPattern.progressRate || 0}% أسبوعياً`}
                        />
                      </ListItem>
                    </List>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* المحتوى المفضل */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    المحتوى المفضل
                  </Typography>
                  {userAnalysis.preferredContent && userAnalysis.preferredContent.length > 0 ? (
                    <List dense>
                      {userAnalysis.preferredContent.slice(0, 5).map((content, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <School />
                          </ListItemIcon>
                          <ListItemText
                            primary={content.title || `كورس ${content.courseId}`}
                            secondary={`${content.interactions} تفاعل`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      لا توجد بيانات كافية
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* عادات الدراسة */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    عادات الدراسة
                  </Typography>
                  {userAnalysis.studyHabits && (
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center" p={2}>
                          <Typography variant="h6" color="primary">
                            {userAnalysis.studyHabits.mostActiveDay || 'غير محدد'}
                          </Typography>
                          <Typography variant="body2">اليوم الأكثر نشاطاً</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center" p={2}>
                          <Typography variant="h6" color="success.main">
                            {userAnalysis.studyHabits.averageStudyStreak || 0}
                          </Typography>
                          <Typography variant="body2">متوسط أيام الدراسة المتتالية</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center" p={2}>
                          <Typography variant="h6" color="info.main">
                            {userAnalysis.studyHabits.preferredContentType || 'متنوع'}
                          </Typography>
                          <Typography variant="body2">نوع المحتوى المفضل</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center" p={2}>
                          <Typography variant="h6" color="warning.main">
                            {userAnalysis.studyHabits.completionRate || 0}%
                          </Typography>
                          <Typography variant="body2">معدل الإكمال</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* تتبع التقدم */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    تتبع التقدم
                  </Typography>
                  {userAnalysis.progressTracking && userAnalysis.progressTracking.length > 0 ? (
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>الكورس</TableCell>
                            <TableCell>التقدم</TableCell>
                            <TableCell>الوقت المقضي</TableCell>
                            <TableCell>آخر نشاط</TableCell>
                            <TableCell>الحالة</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {userAnalysis.progressTracking.slice(0, 5).map((progress, index) => (
                            <TableRow key={index}>
                              <TableCell>{progress.courseTitle || `كورس ${progress.courseId}`}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <LinearProgress
                                    variant="determinate"
                                    value={progress.percentage || 0}
                                    sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                                  />
                                  <Typography variant="body2">
                                    {progress.percentage || 0}%
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>{formatDuration(progress.timeSpent || 0)}</TableCell>
                              <TableCell>
                                {progress.lastActivity ? new Date(progress.lastActivity).toLocaleDateString('ar-SA') : '-'}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={progress.status === 'completed' ? 'مكتمل' : progress.status === 'in_progress' ? 'جاري' : 'لم يبدأ'}
                                  color={progress.status === 'completed' ? 'success' : progress.status === 'in_progress' ? 'primary' : 'default'}
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      لا توجد بيانات تقدم متاحة
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* التوصيات */}
            {userAnalysis.recommendations && userAnalysis.recommendations.length > 0 && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      التوصيات
                    </Typography>
                    <List>
                      {userAnalysis.recommendations.slice(0, 3).map((recommendation, index) => (
                        <ListItem key={index}>
                          <ListItemText
                            primary={recommendation.title}
                            secondary={recommendation.description}
                          />
                          <Chip
                            label={recommendation.priority || 'متوسط'}
                            color={recommendation.priority === 'high' ? 'error' : recommendation.priority === 'medium' ? 'warning' : 'default'}
                            size="small"
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        ) : (
          <Typography variant="body1" textAlign="center" color="text.secondary">
            لا توجد بيانات متاحة لهذا المستخدم
          </Typography>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} startIcon={<Close />}>
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserInteractionDetails;
