const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware للتحقق من صحة التوكن
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد توكن، الوصول مرفوض' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      return res.status(401).json({ message: 'توكن غير صالح' });
    }

    if (!user.isActive) {
      return res.status(401).json({ message: 'الحساب غير مفعل' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('خطأ في التحقق من التوكن:', error);
    res.status(401).json({ message: 'توكن غير صالح' });
  }
};

// Middleware للتحقق من صلاحيات المدير
const adminAuth = async (req, res, next) => {
  try {
    await auth(req, res, () => {
      if (req.user.role !== 'admin') {
        return res.status(403).json({ message: 'الوصول مقتصر على المديرين فقط' });
      }
      next();
    });
  } catch (error) {
    res.status(401).json({ message: 'خطأ في التحقق من الصلاحيات' });
  }
};

// Middleware للتحقق من صلاحيات الطالب
const studentAuth = async (req, res, next) => {
  try {
    await auth(req, res, () => {
      if (req.user.role !== 'student') {
        return res.status(403).json({ message: 'الوصول مقتصر على الطلاب فقط' });
      }
      next();
    });
  } catch (error) {
    res.status(401).json({ message: 'خطأ في التحقق من الصلاحيات' });
  }
};

module.exports = { auth, adminAuth, studentAuth };
