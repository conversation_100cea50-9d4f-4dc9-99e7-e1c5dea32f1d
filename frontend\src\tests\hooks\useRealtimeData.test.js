import { renderHook, act } from '@testing-library/react';
import {
  useRealtimeCourses,
  useRealtimeStudents,
  useRealtimeCourseVideos,
  useRealtimeStudentCourses,
  useRealtimeUpdates
} from '../../hooks/useRealtimeData';

// Mock Firebase services
jest.mock('../../firebase/courseService', () => ({
  default: {
    getAllCourses: jest.fn(),
    getCourseVideos: jest.fn(),
    subscribeToCoursesUpdates: jest.fn()
  }
}));

jest.mock('../../firebase/studentService', () => ({
  default: {
    getAllStudents: jest.fn(),
    subscribeToStudentsUpdates: jest.fn()
  }
}));

jest.mock('../../firebase/databaseService', () => ({
  getStudentCourses: jest.fn()
}));

import courseService from '../../firebase/courseService';
import studentService from '../../firebase/studentService';
import { getStudentCourses } from '../../firebase/databaseService';

describe('useRealtimeCourses Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with loading state', () => {
    const { result } = renderHook(() => useRealtimeCourses());
    
    expect(result.current.loading).toBe(true);
    expect(result.current.courses).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  test('should handle successful courses subscription', async () => {
    const mockCourses = [
      { id: '1', title: 'كورس تجريبي 1', category: 'تسويق' },
      { id: '2', title: 'كورس تجريبي 2', category: 'برمجة' }
    ];

    // محاكاة الاشتراك الناجح
    courseService.subscribeToCoursesUpdates.mockImplementation((callback) => {
      setTimeout(() => callback(mockCourses), 100);
      return jest.fn(); // unsubscribe function
    });

    const { result, waitForNextUpdate } = renderHook(() => useRealtimeCourses());

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.courses).toEqual(mockCourses);
    expect(result.current.error).toBe(null);
  });

  test('should handle subscription error with fallback', async () => {
    const mockError = new Error('Subscription failed');
    const fallbackCourses = [{ id: '1', title: 'Fallback Course' }];

    // محاكاة فشل الاشتراك
    courseService.subscribeToCoursesUpdates.mockImplementation(() => {
      throw mockError;
    });

    // محاكاة البيانات الاحتياطية
    courseService.getAllCourses.mockResolvedValue(fallbackCourses);

    const { result, waitForNextUpdate } = renderHook(() => useRealtimeCourses());

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.courses).toEqual(fallbackCourses);
    expect(result.current.error).toEqual(mockError);
  });

  test('should cleanup subscription on unmount', () => {
    const mockUnsubscribe = jest.fn();
    courseService.subscribeToCoursesUpdates.mockReturnValue(mockUnsubscribe);

    const { unmount } = renderHook(() => useRealtimeCourses());

    unmount();

    expect(mockUnsubscribe).toHaveBeenCalled();
  });
});

describe('useRealtimeStudents Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with loading state', () => {
    const { result } = renderHook(() => useRealtimeStudents());
    
    expect(result.current.loading).toBe(true);
    expect(result.current.students).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  test('should handle successful students subscription', async () => {
    const mockStudents = [
      { id: '1', name: 'أحمد محمد', studentCode: 'STU001' },
      { id: '2', name: 'فاطمة علي', studentCode: 'STU002' }
    ];

    studentService.subscribeToStudentsUpdates.mockImplementation((callback) => {
      setTimeout(() => callback(mockStudents), 100);
      return jest.fn();
    });

    const { result, waitForNextUpdate } = renderHook(() => useRealtimeStudents());

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.students).toEqual(mockStudents);
    expect(result.current.error).toBe(null);
  });

  test('should handle subscription error with fallback', async () => {
    const mockError = new Error('Students subscription failed');
    const fallbackStudents = [{ id: '1', name: 'Fallback Student' }];

    studentService.subscribeToStudentsUpdates.mockImplementation(() => {
      throw mockError;
    });

    studentService.getAllStudents.mockResolvedValue(fallbackStudents);

    const { result, waitForNextUpdate } = renderHook(() => useRealtimeStudents());

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.students).toEqual(fallbackStudents);
    expect(result.current.error).toEqual(mockError);
  });
});

describe('useRealtimeCourseVideos Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should return empty state when no courseId provided', () => {
    const { result } = renderHook(() => useRealtimeCourseVideos(null));
    
    expect(result.current.loading).toBe(false);
    expect(result.current.videos).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  test('should fetch videos when courseId is provided', async () => {
    const courseId = 'course123';
    const mockVideos = [
      { id: '1', title: 'فيديو 1', duration: '10:00' },
      { id: '2', title: 'فيديو 2', duration: '15:30' }
    ];

    courseService.getCourseVideos.mockResolvedValue(mockVideos);

    const { result, waitForNextUpdate } = renderHook(() => 
      useRealtimeCourseVideos(courseId)
    );

    expect(result.current.loading).toBe(true);

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.videos).toEqual(mockVideos);
    expect(courseService.getCourseVideos).toHaveBeenCalledWith(courseId);
  });

  test('should handle video fetch error', async () => {
    const courseId = 'course123';
    const mockError = new Error('Failed to fetch videos');

    courseService.getCourseVideos.mockRejectedValue(mockError);

    const { result, waitForNextUpdate } = renderHook(() => 
      useRealtimeCourseVideos(courseId)
    );

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.videos).toEqual([]);
    expect(result.current.error).toEqual(mockError);
  });

  test('should provide refetch functionality', async () => {
    const courseId = 'course123';
    const mockVideos = [{ id: '1', title: 'فيديو 1' }];

    courseService.getCourseVideos.mockResolvedValue(mockVideos);

    const { result, waitForNextUpdate } = renderHook(() => 
      useRealtimeCourseVideos(courseId)
    );

    await waitForNextUpdate();

    // اختبار إعادة الجلب
    act(() => {
      result.current.refetch();
    });

    expect(courseService.getCourseVideos).toHaveBeenCalledTimes(2);
  });
});

describe('useRealtimeStudentCourses Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should return empty state when no studentId provided', () => {
    const { result } = renderHook(() => useRealtimeStudentCourses(null));
    
    expect(result.current.loading).toBe(false);
    expect(result.current.studentCourses).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  test('should fetch student courses when studentId is provided', async () => {
    const studentId = 'student123';
    const mockCourses = [
      { id: '1', title: 'كورس الطالب 1', progress: 50 },
      { id: '2', title: 'كورس الطالب 2', progress: 75 }
    ];

    getStudentCourses.mockResolvedValue(mockCourses);

    const { result, waitForNextUpdate } = renderHook(() => 
      useRealtimeStudentCourses(studentId)
    );

    expect(result.current.loading).toBe(true);

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.studentCourses).toEqual(mockCourses);
    expect(getStudentCourses).toHaveBeenCalledWith(studentId);
  });

  test('should handle student courses fetch error', async () => {
    const studentId = 'student123';
    const mockError = new Error('Failed to fetch student courses');

    getStudentCourses.mockRejectedValue(mockError);

    const { result, waitForNextUpdate } = renderHook(() => 
      useRealtimeStudentCourses(studentId)
    );

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.studentCourses).toEqual([]);
    expect(result.current.error).toEqual(mockError);
  });
});

describe('useRealtimeUpdates Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should combine courses and students data', async () => {
    const mockCourses = [{ id: '1', title: 'كورس 1' }];
    const mockStudents = [{ id: '1', name: 'طالب 1' }];

    courseService.subscribeToCoursesUpdates.mockImplementation((callback) => {
      setTimeout(() => callback(mockCourses), 100);
      return jest.fn();
    });

    studentService.subscribeToStudentsUpdates.mockImplementation((callback) => {
      setTimeout(() => callback(mockStudents), 100);
      return jest.fn();
    });

    const { result, waitForNextUpdate } = renderHook(() => useRealtimeUpdates());

    expect(result.current.isLoading).toBe(true);

    await waitForNextUpdate();

    expect(result.current.isLoading).toBe(false);
    expect(result.current.courses.courses).toEqual(mockCourses);
    expect(result.current.students.students).toEqual(mockStudents);
    expect(result.current.hasError).toBe(false);
  });

  test('should handle combined error states', async () => {
    const coursesError = new Error('Courses error');
    const studentsError = new Error('Students error');

    courseService.subscribeToCoursesUpdates.mockImplementation(() => {
      throw coursesError;
    });

    studentService.subscribeToStudentsUpdates.mockImplementation(() => {
      throw studentsError;
    });

    const { result, waitForNextUpdate } = renderHook(() => useRealtimeUpdates());

    await waitForNextUpdate();

    expect(result.current.hasError).toBe(true);
    expect(result.current.courses.error).toEqual(coursesError);
    expect(result.current.students.error).toEqual(studentsError);
  });
});
