import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp 
} from 'firebase/firestore';
import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged
} from 'firebase/auth';
import { db, auth } from './config';

// Collections references
const usersRef = collection(db, 'users');
const coursesRef = collection(db, 'courses');
const categoriesRef = collection(db, 'categories');
const progressRef = collection(db, 'progress');
const certificatesRef = collection(db, 'certificates');

// Auth Services
export const authServices = {
  // Admin login with email/password
  loginAdmin: async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Get user data from Firestore
      const userDoc = await getDoc(doc(usersRef, user.uid));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.role === 'admin') {
          return {
            success: true,
            user: {
              id: user.uid,
              email: user.email,
              ...userData
            }
          };
        } else {
          throw new Error('ليس لديك صلاحيات مدير');
        }
      } else {
        throw new Error('بيانات المستخدم غير موجودة');
      }
    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  },

  // Student login with code
  loginStudent: async (studentCode) => {
    try {
      // Find student by code
      const q = query(usersRef, where('studentCode', '==', studentCode), where('role', '==', 'student'));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        throw new Error('كود الطالب غير صحيح');
      }
      
      const studentDoc = querySnapshot.docs[0];
      const studentData = studentDoc.data();
      
      if (!studentData.isActive) {
        throw new Error('حساب الطالب غير مفعل');
      }
      
      return {
        success: true,
        user: {
          id: studentDoc.id,
          ...studentData
        }
      };
    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  },

  // Logout
  logout: async () => {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error) {
      return { success: false, message: error.message };
    }
  },

  // Auth state observer
  onAuthStateChanged: (callback) => {
    return onAuthStateChanged(auth, callback);
  }
};

// User Services
export const userServices = {
  // Get all users
  getUsers: async () => {
    try {
      const querySnapshot = await getDocs(usersRef);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      throw new Error('خطأ في جلب المستخدمين: ' + error.message);
    }
  },

  // Get user by ID
  getUser: async (userId) => {
    try {
      const userDoc = await getDoc(doc(usersRef, userId));
      if (userDoc.exists()) {
        return {
          id: userDoc.id,
          ...userDoc.data()
        };
      } else {
        throw new Error('المستخدم غير موجود');
      }
    } catch (error) {
      throw new Error('خطأ في جلب بيانات المستخدم: ' + error.message);
    }
  },

  // Create user
  createUser: async (userData) => {
    try {
      const docRef = await addDoc(usersRef, {
        ...userData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('خطأ في إنشاء المستخدم: ' + error.message);
    }
  },

  // Update user
  updateUser: async (userId, userData) => {
    try {
      await updateDoc(doc(usersRef, userId), {
        ...userData,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      throw new Error('خطأ في تحديث المستخدم: ' + error.message);
    }
  },

  // Delete user
  deleteUser: async (userId) => {
    try {
      await deleteDoc(doc(usersRef, userId));
      return true;
    } catch (error) {
      throw new Error('خطأ في حذف المستخدم: ' + error.message);
    }
  }
};

// Course Services
export const courseServices = {
  // Get all courses
  getCourses: async () => {
    try {
      const q = query(coursesRef, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      throw new Error('خطأ في جلب الكورسات: ' + error.message);
    }
  },

  // Get active courses
  getActiveCourses: async () => {
    try {
      const q = query(coursesRef, where('isActive', '==', true), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      throw new Error('خطأ في جلب الكورسات النشطة: ' + error.message);
    }
  },

  // Create course
  createCourse: async (courseData) => {
    try {
      const docRef = await addDoc(coursesRef, {
        ...courseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('خطأ في إنشاء الكورس: ' + error.message);
    }
  }
};

// Category Services
export const categoryServices = {
  // Get all categories
  getCategories: async () => {
    try {
      const q = query(categoriesRef, orderBy('order', 'asc'));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      throw new Error('خطأ في جلب الأقسام: ' + error.message);
    }
  },

  // Create category
  createCategory: async (categoryData) => {
    try {
      const docRef = await addDoc(categoriesRef, {
        ...categoryData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('خطأ في إنشاء القسم: ' + error.message);
    }
  }
};

// Initialize default data
export const initializeDefaultData = async () => {
  try {
    // Check if admin exists
    const adminQuery = query(usersRef, where('role', '==', 'admin'), limit(1));
    const adminSnapshot = await getDocs(adminQuery);
    
    if (adminSnapshot.empty) {
      // Create admin user
      await addDoc(usersRef, {
        name: 'علاء عبد الحميد',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Create default categories
      const categories = [
        {
          name: 'التسويق الرقمي',
          description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
          icon: 'digital-marketing',
          color: '#2196F3',
          order: 1,
          isActive: true
        },
        {
          name: 'وسائل التواصل الاجتماعي',
          description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
          icon: 'social-media',
          color: '#4CAF50',
          order: 2,
          isActive: true
        }
      ];

      for (const category of categories) {
        await addDoc(categoriesRef, {
          ...category,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

      // Create sample students
      const students = [
        {
          name: 'أحمد محمد علي',
          studentCode: '123456',
          role: 'student',
          isActive: true
        },
        {
          name: 'فاطمة علي حسن',
          studentCode: '789012',
          role: 'student',
          isActive: true
        }
      ];

      for (const student of students) {
        await addDoc(usersRef, {
          ...student,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

      console.log('✅ تم إنشاء البيانات الافتراضية');
    }
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الافتراضية:', error);
  }
};
