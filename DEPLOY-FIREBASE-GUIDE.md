# 🔥 دليل النشر على Firebase - منصة كورسات علاء عبد الحميد

## 📋 الخطوات المطلوبة

### 1. إعداد Firebase Console

1. **اذهب إلى Firebase Console:**
   - https://console.firebase.google.com
   - سجل دخول بحساب Google

2. **إنشاء مشروع جديد:**
   - اضغط "Create a project"
   - اسم المشروع: `alaa-courses`
   - اختر المنطقة: `us-central1` أو الأقرب لك
   - فعل Google Analytics (اختياري)

3. **إعداد Firestore Database:**
   - اذهب إلى "Firestore Database"
   - اضغط "Create database"
   - اختر "Start in test mode"
   - اختر المنطقة: `us-central1`

4. **إعداد Authentication:**
   - اذهب إلى "Authentication"
   - اضغط "Get started"
   - في تبويب "Sign-in method"
   - فعل "Email/Password"

5. **إعداد Storage:**
   - اذهب إلى "Storage"
   - اضغط "Get started"
   - اختر "Start in test mode"

### 2. تثبيت Firebase CLI

```bash
# تثبيت Firebase CLI عالمياً
npm install -g firebase-tools

# تسجيل الدخول
firebase login
```

### 3. إعداد المشروع محلياً

```bash
# في مجلد المشروع
firebase init

# اختر الخدمات التالية:
# ✅ Firestore: Configure security rules and indexes files
# ✅ Functions: Configure a Cloud Functions directory
# ✅ Hosting: Configure files for Firebase Hosting
```

### 4. إعدادات التهيئة

عند تشغيل `firebase init`:

**Firestore:**
- استخدم الملف الموجود: `firestore.rules`
- استخدم الملف الموجود: `firestore.indexes.json`

**Functions:**
- اختر JavaScript
- استخدم ESLint: No
- Install dependencies: Yes

**Hosting:**
- Public directory: `frontend/build`
- Single-page app: Yes
- Set up automatic builds: No
- Overwrite index.html: No

### 5. تحديث إعدادات Firebase

**في ملف `functions/index.js`:**
```javascript
// تأكد من أن الملف موجود ومحدث
```

**في ملف `firebase.json`:**
```json
{
  "hosting": {
    "public": "frontend/build",
    "rewrites": [
      {
        "source": "/api/**",
        "function": "api"
      },
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

### 6. بناء المشروع

```bash
# تثبيت المكتبات
npm install

# تثبيت مكتبات Functions
cd functions
npm install
cd ..

# بناء الواجهة الأمامية
cd frontend
npm install
npm run build
cd ..
```

### 7. النشر

```bash
# النشر الكامل
firebase deploy

# أو النشر المنفصل
firebase deploy --only hosting
firebase deploy --only functions
firebase deploy --only firestore
```

### 8. الحصول على إعدادات Firebase

بعد إنشاء المشروع، احصل على إعدادات Firebase:

1. اذهب إلى Project Settings
2. في تبويب "General"
3. انزل إلى "Your apps"
4. اضغط "Add app" واختر "Web"
5. أدخل اسم التطبيق: `alaa-courses-web`
6. انسخ Firebase config

### 9. تحديث إعدادات الواجهة الأمامية

**إنشاء ملف `.env` في مجلد `frontend`:**
```env
REACT_APP_FIREBASE_API_KEY=your-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_API_URL=https://your-project-id.web.app/api
```

### 10. إعداد قواعد Firestore

**في ملف `firestore.rules`:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true; // للتطوير فقط
    }
  }
}
```

### 11. اختبار النشر

بعد النشر:
1. اذهب إلى الرابط المعطى
2. جرب تسجيل الدخول:
   - المدير: `<EMAIL>` / `Admin123!`
   - الطلاب: `123456`, `789012`

## 🚀 أوامر النشر السريع

```bash
# بناء ونشر كامل
npm run build && firebase deploy

# نشر الواجهة فقط
cd frontend && npm run build && cd .. && firebase deploy --only hosting

# نشر Functions فقط
firebase deploy --only functions

# مراقبة logs
firebase functions:log
```

## 🔧 استكشاف الأخطاء

### خطأ في Authentication:
```bash
firebase login --reauth
```

### خطأ في البناء:
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules frontend/node_modules functions/node_modules
npm install
cd frontend && npm install && cd ..
cd functions && npm install && cd ..
```

### خطأ في Functions:
```bash
# تحقق من logs
firebase functions:log --only api
```

## 📱 بعد النشر الناجح

**الروابط:**
- الموقع: `https://your-project-id.web.app`
- Firebase Console: `https://console.firebase.google.com/project/your-project-id`

**بيانات الدخول:**
- المدير: `<EMAIL>` / `Admin123!`
- الطلاب: `123456`, `789012`

## 🎉 تهانينا!

منصة كورسات علاء عبد الحميد أصبحت متاحة على الإنترنت!

---

**ملاحظة:** تأكد من تحديث قواعد Firestore للإنتاج قبل الاستخدام الفعلي.
