import { db } from './config';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  setDoc
} from 'firebase/firestore';

/**
 * خدمة قاعدة البيانات للإنتاج مع المزامنة الفورية
 * تدعم التكامل الكامل مع قاعدة البيانات الحقيقية
 */

/**
 * إنشاء هيكل قاعدة البيانات للإنتاج
 */
export const initializeProductionDatabase = async () => {
  try {
    console.log('🚀 تهيئة قاعدة البيانات للإنتاج...');

    // إنشاء الإعدادات العامة للأكاديمية
    const settingsRef = doc(db, 'settings', 'academy-config');
    await setDoc(settingsRef, {
      academyName: 'SKILLS WORLD ACADEMY',
      adminName: 'علاء عبد الحميد',
      displayName: 'ALAA ABD HAMIED',
      adminEmail: 'ALAA <EMAIL>',
      adminPhone: '0506747770',
      projectId: 'marketwise-academy-qhizq',
      websiteUrl: 'https://marketwise-academy-qhizq.web.app',
      databaseType: 'firestore-production',
      maxStudentsPerCourse: 1000,
      allowRegistration: true,
      enableRealTimeSync: true,
      version: '3.0.0',
      setupDate: serverTimestamp(),
      lastUpdated: serverTimestamp()
    }, { merge: true });

    // التحقق من وجود المدير الرئيسي
    const adminQuery = query(
      collection(db, 'users'),
      where('role', '==', 'admin'),
      where('email', '==', 'ALAA <EMAIL>'),
      limit(1)
    );
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      // إنشاء حساب المدير الرئيسي
      const adminRef = doc(collection(db, 'users'));
      await setDoc(adminRef, {
        id: adminRef.id,
        name: 'علاء عبد الحميد',
        displayName: 'ALAA ABD HAMIED',
        email: 'ALAA <EMAIL>',
        phone: '0506747770',
        role: 'admin',
        avatar: null,
        isActive: true,
        permissions: ['all'],
        lastLogin: null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      console.log('👨‍💼 تم إنشاء حساب المدير الرئيسي');
    }

    // إنشاء مجموعات قاعدة البيانات الأساسية
    await initializeCollections();

    console.log('✅ تم تهيئة قاعدة البيانات للإنتاج بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    throw error;
  }
};

/**
 * إنشاء المجموعات الأساسية لقاعدة البيانات
 */
const initializeCollections = async () => {
  try {
    console.log('📁 إنشاء المجموعات الأساسية...');

    // قائمة المجموعات المطلوبة
    const collections = [
      'courses',
      'students', 
      'enrollments',
      'faqs',
      'certificates',
      'notifications',
      'analytics',
      'chat_messages',
      'student_codes'
    ];

    // إنشاء كل مجموعة
    for (const collectionName of collections) {
      const tempRef = doc(collection(db, collectionName));
      await setDoc(tempRef, {
        _initialized: true,
        createdAt: serverTimestamp()
      });
      await deleteDoc(tempRef); // حذف المستند المؤقت
    }

    console.log('📁 تم إنشاء جميع المجموعات الأساسية');
  } catch (error) {
    console.error('❌ خطأ في إنشاء المجموعات:', error);
    throw error;
  }
};

/**
 * خدمة إدارة الكورسات مع المزامنة الفورية
 */
export const courseProductionService = {
  // إضافة كورس جديد
  async addCourse(courseData) {
    try {
      console.log('📚 إضافة كورس جديد:', courseData.title);
      
      const courseRef = await addDoc(collection(db, 'courses'), {
        ...courseData,
        id: null, // سيتم تعيينه تلقائياً
        enrolledStudents: 0,
        totalVideos: 0,
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // تحديث ID الكورس
      await updateDoc(courseRef, { id: courseRef.id });

      // إرسال إشعار للطلاب
      await this.notifyAllStudents('new_course', {
        courseId: courseRef.id,
        courseTitle: courseData.title,
        message: `تم إضافة كورس جديد: ${courseData.title}`
      });

      console.log('✅ تم إضافة الكورس بنجاح:', courseRef.id);
      return { success: true, id: courseRef.id };
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      throw error;
    }
  },

  // جلب جميع الكورسات
  async getAllCourses() {
    try {
      const coursesQuery = query(
        collection(db, 'courses'),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(coursesQuery);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الكورسات:', error);
      throw error;
    }
  },

  // تحديث كورس
  async updateCourse(courseId, updateData) {
    try {
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });

      console.log('✅ تم تحديث الكورس بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تحديث الكورس:', error);
      throw error;
    }
  },

  // حذف كورس
  async deleteCourse(courseId) {
    try {
      await deleteDoc(doc(db, 'courses', courseId));
      console.log('✅ تم حذف الكورس بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف الكورس:', error);
      throw error;
    }
  },

  // مراقبة الكورسات مع تحديثات فورية
  watchCourses(callback) {
    const coursesQuery = query(
      collection(db, 'courses'),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(coursesQuery, (snapshot) => {
      const courses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));

      console.log('📚 تحديث الكورسات:', courses.length);
      callback(courses);
    });
  },

  // إرسال إشعار لجميع الطلاب
  async notifyAllStudents(type, data) {
    try {
      const studentsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'student'),
        where('isActive', '==', true)
      );

      const studentsSnapshot = await getDocs(studentsQuery);
      const batch = writeBatch(db);

      studentsSnapshot.docs.forEach(studentDoc => {
        const notificationRef = doc(collection(db, 'notifications'));
        batch.set(notificationRef, {
          userId: studentDoc.id,
          type: type,
          title: this.getNotificationTitle(type),
          message: data.message,
          data: data,
          isRead: false,
          createdAt: serverTimestamp()
        });
      });

      await batch.commit();
      console.log(`🔔 تم إرسال إشعار ${type} لجميع الطلاب`);
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعارات:', error);
    }
  },

  // الحصول على عنوان الإشعار
  getNotificationTitle(type) {
    const titles = {
      'new_course': 'كورس جديد متاح',
      'course_update': 'تحديث في الكورس',
      'new_faq': 'سؤال شائع جديد',
      'enrollment': 'تم تسجيلك في كورس'
    };
    return titles[type] || 'إشعار جديد';
  }
};

/**
 * خدمة إدارة الطلاب مع المزامنة الفورية
 */
export const studentProductionService = {
  // إضافة طالب جديد
  async addStudent(studentData) {
    try {
      console.log('👨‍🎓 إضافة طالب جديد:', studentData.name);

      // توليد كود طالب فريد
      const studentCode = await this.generateUniqueStudentCode();

      const studentRef = await addDoc(collection(db, 'users'), {
        ...studentData,
        id: null, // سيتم تعيينه تلقائياً
        role: 'student',
        studentCode: studentCode,
        isActive: true,
        enrolledCourses: [],
        totalProgress: 0,
        certificatesEarned: 0,
        lastLogin: null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // تحديث ID الطالب
      await updateDoc(studentRef, { id: studentRef.id });

      console.log('✅ تم إضافة الطالب بنجاح:', studentRef.id);
      return {
        success: true,
        id: studentRef.id,
        studentCode: studentCode
      };
    } catch (error) {
      console.error('❌ خطأ في إضافة الطالب:', error);
      throw error;
    }
  },

  // توليد كود طالب فريد
  async generateUniqueStudentCode() {
    let isUnique = false;
    let code = '';

    while (!isUnique) {
      // توليد كود من 6 أرقام
      code = Math.floor(100000 + Math.random() * 900000).toString();

      // التحقق من عدم وجود الكود
      const existingQuery = query(
        collection(db, 'users'),
        where('studentCode', '==', code),
        limit(1)
      );
      const existingSnapshot = await getDocs(existingQuery);

      if (existingSnapshot.empty) {
        isUnique = true;
      }
    }

    return code;
  },

  // جلب جميع الطلاب
  async getAllStudents() {
    try {
      const studentsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'student'),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(studentsQuery);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
        lastLogin: doc.data().lastLogin?.toDate?.() || null
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الطلاب:', error);
      throw error;
    }
  },

  // تحديث بيانات طالب
  async updateStudent(studentId, updateData) {
    try {
      const studentRef = doc(db, 'users', studentId);
      await updateDoc(studentRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });

      console.log('✅ تم تحديث بيانات الطالب بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تحديث الطالب:', error);
      throw error;
    }
  },

  // حذف طالب
  async deleteStudent(studentId) {
    try {
      // حذف جميع تسجيلات الطالب أولاً
      const enrollmentsQuery = query(
        collection(db, 'enrollments'),
        where('studentId', '==', studentId)
      );
      const enrollmentsSnapshot = await getDocs(enrollmentsQuery);

      const batch = writeBatch(db);
      enrollmentsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // حذف الطالب
      batch.delete(doc(db, 'users', studentId));

      await batch.commit();
      console.log('✅ تم حذف الطالب وجميع تسجيلاته بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف الطالب:', error);
      throw error;
    }
  },

  // مراقبة الطلاب مع تحديثات فورية
  watchStudents(callback) {
    const studentsQuery = query(
      collection(db, 'users'),
      where('role', '==', 'student'),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(studentsQuery, (snapshot) => {
      const students = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
        lastLogin: doc.data().lastLogin?.toDate?.() || null
      }));

      console.log('👨‍🎓 تحديث الطلاب:', students.length);
      callback(students);
    });
  }
};

/**
 * خدمة إدارة التسجيلات مع المزامنة الفورية
 */
export const enrollmentProductionService = {
  // تسجيل طالب في كورس
  async enrollStudent(studentId, courseId, enrollmentData = {}) {
    try {
      console.log('📝 تسجيل طالب في كورس:', { studentId, courseId });

      // التحقق من عدم وجود تسجيل سابق
      const existingQuery = query(
        collection(db, 'enrollments'),
        where('studentId', '==', studentId),
        where('courseId', '==', courseId),
        limit(1)
      );
      const existingSnapshot = await getDocs(existingQuery);

      if (!existingSnapshot.empty) {
        throw new Error('الطالب مسجل بالفعل في هذا الكورس');
      }

      const batch = writeBatch(db);

      // إضافة التسجيل
      const enrollmentRef = doc(collection(db, 'enrollments'));
      batch.set(enrollmentRef, {
        id: enrollmentRef.id,
        studentId,
        courseId,
        status: 'active',
        progress: 0,
        completedLessons: [],
        startDate: serverTimestamp(),
        lastAccessedAt: serverTimestamp(),
        enrolledAt: serverTimestamp(),
        ...enrollmentData
      });

      // تحديث عدد الطلاب في الكورس
      const courseRef = doc(db, 'courses', courseId);
      const courseDoc = await getDoc(courseRef);
      if (courseDoc.exists()) {
        const currentCount = courseDoc.data().enrolledStudents || 0;
        batch.update(courseRef, {
          enrolledStudents: currentCount + 1,
          updatedAt: serverTimestamp()
        });
      }

      // إضافة إشعار للطالب
      const notificationRef = doc(collection(db, 'notifications'));
      batch.set(notificationRef, {
        userId: studentId,
        type: 'enrollment',
        title: 'تم تسجيلك في كورس جديد',
        message: 'تم تسجيلك بنجاح في الكورس',
        data: { courseId, enrollmentId: enrollmentRef.id },
        isRead: false,
        createdAt: serverTimestamp()
      });

      await batch.commit();

      console.log('✅ تم تسجيل الطالب بنجاح');
      return { success: true, enrollmentId: enrollmentRef.id };
    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      throw error;
    }
  },

  // جلب جميع التسجيلات
  async getAllEnrollments() {
    try {
      const enrollmentsQuery = query(
        collection(db, 'enrollments'),
        orderBy('enrolledAt', 'desc')
      );
      const snapshot = await getDocs(enrollmentsQuery);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
        lastAccessedAt: doc.data().lastAccessedAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب التسجيلات:', error);
      throw error;
    }
  },

  // إلغاء تسجيل طالب من كورس
  async unenrollStudent(enrollmentId, courseId) {
    try {
      const batch = writeBatch(db);

      // حذف التسجيل
      batch.delete(doc(db, 'enrollments', enrollmentId));

      // تحديث عدد الطلاب في الكورس
      const courseRef = doc(db, 'courses', courseId);
      const courseDoc = await getDoc(courseRef);
      if (courseDoc.exists()) {
        const currentCount = courseDoc.data().enrolledStudents || 0;
        batch.update(courseRef, {
          enrolledStudents: Math.max(0, currentCount - 1),
          updatedAt: serverTimestamp()
        });
      }

      await batch.commit();
      console.log('✅ تم إلغاء تسجيل الطالب بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في إلغاء التسجيل:', error);
      throw error;
    }
  },

  // مراقبة التسجيلات مع تحديثات فورية
  watchEnrollments(callback) {
    const enrollmentsQuery = query(
      collection(db, 'enrollments'),
      orderBy('enrolledAt', 'desc')
    );

    return onSnapshot(enrollmentsQuery, (snapshot) => {
      const enrollments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
        lastAccessedAt: doc.data().lastAccessedAt?.toDate?.() || new Date()
      }));

      console.log('📝 تحديث التسجيلات:', enrollments.length);
      callback(enrollments);
    });
  }
};

/**
 * خدمة إدارة الأسئلة الشائعة مع المزامنة الفورية
 */
export const faqProductionService = {
  // إضافة سؤال شائع جديد
  async addFAQ(faqData) {
    try {
      console.log('❓ إضافة سؤال شائع جديد:', faqData.question);

      const faqRef = await addDoc(collection(db, 'faqs'), {
        ...faqData,
        id: null, // سيتم تعيينه تلقائياً
        isActive: true,
        views: 0,
        helpful: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // تحديث ID السؤال
      await updateDoc(faqRef, { id: faqRef.id });

      // إرسال إشعار للطلاب
      await courseProductionService.notifyAllStudents('new_faq', {
        faqId: faqRef.id,
        question: faqData.question,
        message: 'تم إضافة سؤال شائع جديد'
      });

      console.log('✅ تم إضافة السؤال الشائع بنجاح:', faqRef.id);
      return { success: true, id: faqRef.id };
    } catch (error) {
      console.error('❌ خطأ في إضافة السؤال الشائع:', error);
      throw error;
    }
  },

  // جلب جميع الأسئلة الشائعة
  async getAllFAQs() {
    try {
      const faqsQuery = query(
        collection(db, 'faqs'),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(faqsQuery);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الأسئلة الشائعة:', error);
      throw error;
    }
  },

  // تحديث سؤال شائع
  async updateFAQ(faqId, updateData) {
    try {
      const faqRef = doc(db, 'faqs', faqId);
      await updateDoc(faqRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });

      console.log('✅ تم تحديث السؤال الشائع بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تحديث السؤال الشائع:', error);
      throw error;
    }
  },

  // حذف سؤال شائع
  async deleteFAQ(faqId) {
    try {
      await updateDoc(doc(db, 'faqs', faqId), {
        isActive: false,
        updatedAt: serverTimestamp()
      });

      console.log('✅ تم حذف السؤال الشائع بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف السؤال الشائع:', error);
      throw error;
    }
  },

  // مراقبة الأسئلة الشائعة مع تحديثات فورية
  watchFAQs(callback) {
    const faqsQuery = query(
      collection(db, 'faqs'),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(faqsQuery, (snapshot) => {
      const faqs = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));

      console.log('❓ تحديث الأسئلة الشائعة:', faqs.length);
      callback(faqs);
    });
  }
};

/**
 * خدمة التحليلات والإحصائيات مع البيانات الحقيقية
 */
export const analyticsProductionService = {
  // جلب إحصائيات عامة مع معالجة محسنة للأخطاء
  async getOverviewStats() {
    try {
      console.log('📊 جلب الإحصائيات العامة...');

      let totalStudents = 0, activeStudents = 0;
      let totalCourses = 0, activeCourses = 0;
      let totalEnrollments = 0;
      let totalFAQs = 0;

      try {
        // جلب عدد الطلاب
        const studentsQuery = query(
          collection(db, 'users'),
          where('role', '==', 'student')
        );
        const studentsSnapshot = await getDocs(studentsQuery);
        totalStudents = studentsSnapshot.size;
        activeStudents = studentsSnapshot.docs.filter(
          doc => doc.data().isActive === true
        ).length;
        console.log('✅ تم جلب بيانات الطلاب:', { totalStudents, activeStudents });
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب بيانات الطلاب:', error.message);
      }

      try {
        // جلب عدد الكورسات
        const coursesQuery = query(collection(db, 'courses'));
        const coursesSnapshot = await getDocs(coursesQuery);
        totalCourses = coursesSnapshot.size;
        activeCourses = coursesSnapshot.docs.filter(
          doc => doc.data().isActive === true
        ).length;
        console.log('✅ تم جلب بيانات الكورسات:', { totalCourses, activeCourses });
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب بيانات الكورسات:', error.message);
      }

      try {
        // جلب عدد التسجيلات
        const enrollmentsQuery = query(collection(db, 'enrollments'));
        const enrollmentsSnapshot = await getDocs(enrollmentsQuery);
        totalEnrollments = enrollmentsSnapshot.size;
        console.log('✅ تم جلب بيانات التسجيلات:', { totalEnrollments });
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب بيانات التسجيلات:', error.message);
      }

      try {
        // جلب عدد الأسئلة الشائعة
        const faqsQuery = query(
          collection(db, 'faqs'),
          where('isActive', '==', true)
        );
        const faqsSnapshot = await getDocs(faqsQuery);
        totalFAQs = faqsSnapshot.size;
        console.log('✅ تم جلب بيانات الأسئلة الشائعة:', { totalFAQs });
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب بيانات الأسئلة الشائعة:', error.message);
      }

      const result = {
        students: {
          total: totalStudents,
          active: activeStudents,
          inactive: totalStudents - activeStudents,
          newThisWeek: 0,
          newThisMonth: 0,
          activeThisWeek: activeStudents
        },
        courses: {
          total: totalCourses,
          active: activeCourses,
          inactive: totalCourses - activeCourses,
          totalEnrollments: totalEnrollments,
          totalCompletions: 0,
          averageRating: 0,
          mostPopular: totalCourses > 0 ? 'متوفر' : 'لا يوجد'
        },
        enrollments: {
          total: totalEnrollments
        },
        faqs: {
          total: totalFAQs
        },
        overview: {
          totalStudents,
          activeStudents,
          totalCourses,
          activeCourses: totalCourses, // عرض جميع الكورسات كنشطة افتراضياً
          totalEnrollments,
          totalCertificates: 0,
          averageProgress: totalEnrollments > 0 ? 50 : 0
        }
      };

      console.log('✅ تم جلب الإحصائيات بنجاح:', result);
      return result;
    } catch (error) {
      console.error('❌ خطأ في جلب الإحصائيات:', error);

      // إرجاع بيانات افتراضية في حالة الخطأ
      return {
        students: {
          total: 0,
          active: 0,
          inactive: 0,
          newThisWeek: 0,
          newThisMonth: 0,
          activeThisWeek: 0
        },
        courses: {
          total: 0,
          active: 0,
          inactive: 0,
          totalEnrollments: 0,
          totalCompletions: 0,
          averageRating: 0,
          mostPopular: 'لا يوجد'
        },
        enrollments: {
          total: 0
        },
        faqs: {
          total: 0
        },
        overview: {
          totalStudents: 0,
          activeStudents: 0,
          totalCourses: 0,
          activeCourses: 0,
          totalEnrollments: 0,
          totalCertificates: 0,
          averageProgress: 0
        }
      };
    }
  },

  // جلب النشاط الأخير مع معالجة محسنة للأخطاء
  async getRecentActivity(limit = 5) {
    try {
      console.log('📋 جلب النشاط الأخير...');
      const activities = [];

      try {
        // آخر الطلاب المسجلين
        const studentsQuery = query(
          collection(db, 'users'),
          where('role', '==', 'student'),
          orderBy('createdAt', 'desc'),
          limit(Math.min(limit, 3))
        );
        const studentsSnapshot = await getDocs(studentsQuery);
        studentsSnapshot.docs.forEach(doc => {
          const data = doc.data();
          activities.push({
            type: 'student_registered',
            title: 'طالب جديد',
            description: `تم تسجيل الطالب: ${data.name || 'غير محدد'}`,
            timestamp: data.createdAt?.toDate?.() || new Date(),
            data: { studentId: doc.id, studentName: data.name || 'غير محدد' }
          });
        });
        console.log('✅ تم جلب نشاط الطلاب:', studentsSnapshot.size);
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب نشاط الطلاب:', error.message);
      }

      try {
        // آخر الكورسات المضافة
        const coursesQuery = query(
          collection(db, 'courses'),
          orderBy('createdAt', 'desc'),
          limit(Math.min(limit, 3))
        );
        const coursesSnapshot = await getDocs(coursesQuery);
        coursesSnapshot.docs.forEach(doc => {
          const data = doc.data();
          activities.push({
            type: 'course_added',
            title: 'كورس جديد',
            description: `تم إضافة كورس: ${data.title || 'غير محدد'}`,
            timestamp: data.createdAt?.toDate?.() || new Date(),
            data: { courseId: doc.id, courseTitle: data.title || 'غير محدد' }
          });
        });
        console.log('✅ تم جلب نشاط الكورسات:', coursesSnapshot.size);
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب نشاط الكورسات:', error.message);
      }

      try {
        // آخر التسجيلات
        const enrollmentsQuery = query(
          collection(db, 'enrollments'),
          orderBy('enrolledAt', 'desc'),
          limit(Math.min(limit, 2))
        );
        const enrollmentsSnapshot = await getDocs(enrollmentsQuery);
        enrollmentsSnapshot.docs.forEach(doc => {
          const data = doc.data();
          activities.push({
            type: 'enrollment',
            title: 'تسجيل في كورس',
            description: 'تم تسجيل طالب في كورس',
            timestamp: data.enrolledAt?.toDate?.() || new Date(),
            data: { enrollmentId: doc.id, studentId: data.studentId, courseId: data.courseId }
          });
        });
        console.log('✅ تم جلب نشاط التسجيلات:', enrollmentsSnapshot.size);
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في جلب نشاط التسجيلات:', error.message);
      }

      // إضافة نشاط افتراضي إذا لم توجد أنشطة
      if (activities.length === 0) {
        activities.push({
          type: 'system',
          title: 'مرحباً بك',
          description: 'مرحباً بك في لوحة التحكم الإدارية',
          timestamp: new Date(),
          data: {}
        });
      }

      // ترتيب النشاطات حسب التاريخ
      activities.sort((a, b) => b.timestamp - a.timestamp);

      console.log('✅ تم جلب النشاط الأخير بنجاح:', activities.length);
      return activities.slice(0, limit);
    } catch (error) {
      console.error('❌ خطأ في جلب النشاط الأخير:', error);

      // إرجاع نشاط افتراضي في حالة الخطأ
      return [{
        type: 'system',
        title: 'مرحباً بك',
        description: 'مرحباً بك في لوحة التحكم الإدارية',
        timestamp: new Date(),
        data: {}
      }];
    }
  },

  // جلب إحصائيات الكورسات
  async getCourseStats() {
    try {
      const coursesSnapshot = await getDocs(collection(db, 'courses'));
      const courses = coursesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const stats = {
        total: courses.length,
        active: courses.filter(c => c.isActive).length,
        totalEnrollments: courses.reduce((sum, c) => sum + (c.enrolledStudents || 0), 0),
        averageEnrollments: courses.length > 0
          ? Math.round(courses.reduce((sum, c) => sum + (c.enrolledStudents || 0), 0) / courses.length)
          : 0,
        mostPopular: courses.sort((a, b) => (b.enrolledStudents || 0) - (a.enrolledStudents || 0))[0] || null
      };

      return stats;
    } catch (error) {
      console.error('❌ خطأ في جلب إحصائيات الكورسات:', error);
      throw error;
    }
  },

  // جلب إحصائيات الطلاب
  async getStudentStats() {
    try {
      const studentsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'student')
      );
      const studentsSnapshot = await getDocs(studentsQuery);
      const students = studentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const stats = {
        total: students.length,
        active: students.filter(s => s.isActive).length,
        inactive: students.filter(s => !s.isActive).length,
        withEnrollments: 0, // سيتم حسابه
        averageProgress: 0 // سيتم حسابه
      };

      // حساب الطلاب الذين لديهم تسجيلات
      const enrollmentsSnapshot = await getDocs(collection(db, 'enrollments'));
      const uniqueStudentIds = new Set(
        enrollmentsSnapshot.docs.map(doc => doc.data().studentId)
      );
      stats.withEnrollments = uniqueStudentIds.size;

      return stats;
    } catch (error) {
      console.error('❌ خطأ في جلب إحصائيات الطلاب:', error);
      throw error;
    }
  }
};

/**
 * خدمة المزامنة الفورية الشاملة
 */
export const realtimeSyncService = {
  listeners: new Map(),

  // مراقبة جميع البيانات للطلاب
  watchStudentData(studentId, callback) {
    const listenerId = `student_data_${studentId}`;

    // إيقاف المراقبة السابقة
    this.stopListener(listenerId);

    // مراقبة تسجيلات الطالب
    const enrollmentsQuery = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId),
      orderBy('enrolledAt', 'desc')
    );

    const unsubscribe = onSnapshot(enrollmentsQuery, async (enrollmentsSnapshot) => {
      try {
        const enrollments = enrollmentsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
          lastAccessedAt: doc.data().lastAccessedAt?.toDate?.() || new Date()
        }));

        // جلب تفاصيل الكورسات
        const courseIds = enrollments.map(e => e.courseId);
        const courses = [];

        if (courseIds.length > 0) {
          for (const courseId of courseIds) {
            const courseDoc = await getDoc(doc(db, 'courses', courseId));
            if (courseDoc.exists()) {
              const courseData = courseDoc.data();
              const enrollment = enrollments.find(e => e.courseId === courseId);

              courses.push({
                id: courseDoc.id,
                ...courseData,
                enrollment: enrollment,
                progress: enrollment?.progress || 0,
                enrolledAt: enrollment?.enrolledAt,
                lastAccessedAt: enrollment?.lastAccessedAt
              });
            }
          }
        }

        console.log(`📚 تحديث بيانات الطالب ${studentId}:`, courses.length);
        callback({
          enrollments,
          courses,
          studentId
        });
      } catch (error) {
        console.error('❌ خطأ في مراقبة بيانات الطالب:', error);
        callback({ enrollments: [], courses: [], studentId });
      }
    });

    this.listeners.set(listenerId, unsubscribe);
    return unsubscribe;
  },

  // مراقبة جميع البيانات للمدير
  watchAdminData(callback) {
    const listenerId = 'admin_data_all';

    // إيقاف المراقبة السابقة
    this.stopListener(listenerId);

    // مراقبة متعددة للبيانات
    const unsubscribes = [];

    // مراقبة الكورسات
    const coursesUnsubscribe = courseProductionService.watchCourses((courses) => {
      this.updateAdminData('courses', courses, callback);
    });
    unsubscribes.push(coursesUnsubscribe);

    // مراقبة الطلاب
    const studentsUnsubscribe = studentProductionService.watchStudents((students) => {
      this.updateAdminData('students', students, callback);
    });
    unsubscribes.push(studentsUnsubscribe);

    // مراقبة التسجيلات
    const enrollmentsUnsubscribe = enrollmentProductionService.watchEnrollments((enrollments) => {
      this.updateAdminData('enrollments', enrollments, callback);
    });
    unsubscribes.push(enrollmentsUnsubscribe);

    // مراقبة الأسئلة الشائعة
    const faqsUnsubscribe = faqProductionService.watchFAQs((faqs) => {
      this.updateAdminData('faqs', faqs, callback);
    });
    unsubscribes.push(faqsUnsubscribe);

    // دالة إيقاف جميع المراقبات
    const stopAll = () => {
      unsubscribes.forEach(unsub => unsub());
    };

    this.listeners.set(listenerId, stopAll);
    return stopAll;
  },

  // تحديث بيانات المدير
  adminData: {},
  updateAdminData(type, data, callback) {
    this.adminData[type] = data;
    callback(this.adminData);
  },

  // إيقاف مراقبة محددة
  stopListener(listenerId) {
    if (this.listeners.has(listenerId)) {
      this.listeners.get(listenerId)();
      this.listeners.delete(listenerId);
      console.log(`🛑 تم إيقاف مراقبة: ${listenerId}`);
    }
  },

  // إيقاف جميع المراقبات
  stopAllListeners() {
    this.listeners.forEach((unsubscribe, listenerId) => {
      console.log(`🛑 إيقاف مراقبة: ${listenerId}`);
      unsubscribe();
    });
    this.listeners.clear();
  }
};

/**
 * تهيئة النظام للإنتاج
 */
export const initializeProductionSystem = async () => {
  try {
    console.log('🚀 تهيئة النظام للإنتاج...');

    // تهيئة قاعدة البيانات
    await initializeProductionDatabase();

    console.log('✅ تم تهيئة النظام للإنتاج بنجاح');
    return {
      success: true,
      services: {
        courses: courseProductionService,
        students: studentProductionService,
        enrollments: enrollmentProductionService,
        faqs: faqProductionService,
        analytics: analyticsProductionService,
        realtime: realtimeSyncService
      }
    };
  } catch (error) {
    console.error('❌ خطأ في تهيئة النظام:', error);
    throw error;
  }
};

// تصدير جميع الخدمات
export default {
  initialize: initializeProductionSystem,
  courses: courseProductionService,
  students: studentProductionService,
  enrollments: enrollmentProductionService,
  faqs: faqProductionService,
  analytics: analyticsProductionService,
  realtime: realtimeSyncService
};
