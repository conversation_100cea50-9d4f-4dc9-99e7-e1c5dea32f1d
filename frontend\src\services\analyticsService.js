import { db } from '../firebase/config';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs, 
  addDoc, 
  serverTimestamp,
  onSnapshot,
  startAfter,
  endBefore
} from 'firebase/firestore';

/**
 * خدمة التحليلات المتقدمة
 * تحليل شامل لجميع تفاعلات المستخدمين
 */
class AnalyticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
  }

  /**
   * تحليل شامل للمنصة
   */
  async getPlatformAnalytics(timeRange = '7d') {
    const cacheKey = `platform_analytics_${timeRange}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const dateRange = this.getDateRange(timeRange);
      
      const analytics = {
        overview: await this.getOverviewMetrics(dateRange),
        userEngagement: await this.getUserEngagementMetrics(dateRange),
        contentPerformance: await this.getContentPerformanceMetrics(dateRange),
        technicalMetrics: await this.getTechnicalMetrics(dateRange),
        revenueMetrics: await this.getRevenueMetrics(dateRange),
        realTimeMetrics: await this.getRealTimeMetrics()
      };

      // حفظ في الكاش
      this.cache.set(cacheKey, {
        data: analytics,
        timestamp: Date.now()
      });

      return analytics;
    } catch (error) {
      console.error('❌ خطأ في جلب تحليلات المنصة:', error);
      throw error;
    }
  }

  /**
   * مقاييس عامة
   */
  async getOverviewMetrics(dateRange) {
    const [interactions, users, courses, enrollments] = await Promise.all([
      this.getInteractionsInRange(dateRange),
      this.getUsersInRange(dateRange),
      this.getCoursesInRange(dateRange),
      this.getEnrollmentsInRange(dateRange)
    ]);

    return {
      totalUsers: users.length,
      activeUsers: this.getActiveUsers(interactions),
      newUsers: users.filter(u => this.isInDateRange(u.createdAt, dateRange)).length,
      totalCourses: courses.length,
      totalEnrollments: enrollments.length,
      completionRate: this.calculateCompletionRate(enrollments),
      averageSessionDuration: this.calculateAverageSessionDuration(interactions),
      bounceRate: this.calculateBounceRate(interactions),
      retentionRate: await this.calculateRetentionRate(dateRange)
    };
  }

  /**
   * مقاييس تفاعل المستخدمين
   */
  async getUserEngagementMetrics(dateRange) {
    const interactions = await this.getInteractionsInRange(dateRange);
    
    return {
      totalInteractions: interactions.length,
      interactionsByType: this.groupInteractionsByType(interactions),
      dailyActiveUsers: this.getDailyActiveUsers(interactions),
      userJourney: this.analyzeUserJourney(interactions),
      deviceUsage: this.analyzeDeviceUsage(interactions),
      geographicDistribution: this.analyzeGeographicDistribution(interactions),
      timeBasedActivity: this.analyzeTimeBasedActivity(interactions),
      featureUsage: this.analyzeFeatureUsage(interactions)
    };
  }

  /**
   * أداء المحتوى
   */
  async getContentPerformanceMetrics(dateRange) {
    const [interactions, courses, enrollments] = await Promise.all([
      this.getInteractionsInRange(dateRange),
      this.getCoursesInRange(dateRange),
      this.getEnrollmentsInRange(dateRange)
    ]);

    const courseInteractions = interactions.filter(i => i.type === 'course_interaction');
    const videoInteractions = interactions.filter(i => i.type === 'video_interaction');

    return {
      topCourses: this.getTopCourses(courseInteractions, enrollments),
      courseCompletionRates: this.getCourseCompletionRates(enrollments),
      videoEngagement: this.analyzeVideoEngagement(videoInteractions),
      contentDropoffPoints: this.analyzeContentDropoffPoints(courseInteractions),
      searchQueries: this.analyzeSearchQueries(interactions),
      userPathways: this.analyzeUserPathways(interactions),
      contentRatings: await this.getContentRatings(dateRange)
    };
  }

  /**
   * المقاييس التقنية
   */
  async getTechnicalMetrics(dateRange) {
    const interactions = await this.getInteractionsInRange(dateRange);
    const errors = interactions.filter(i => i.type === 'error');
    const performance = interactions.filter(i => i.type === 'performance');

    return {
      errorRate: errors.length / interactions.length,
      errorsByType: this.groupErrorsByType(errors),
      performanceMetrics: this.analyzePerformanceMetrics(performance),
      browserUsage: this.analyzeBrowserUsage(interactions),
      deviceTypes: this.analyzeDeviceTypes(interactions),
      connectionSpeeds: this.analyzeConnectionSpeeds(interactions),
      loadTimes: this.analyzeLoadTimes(performance),
      apiResponseTimes: this.analyzeApiResponseTimes(performance)
    };
  }

  /**
   * مقاييس الإيرادات
   */
  async getRevenueMetrics(dateRange) {
    const payments = await this.getPaymentsInRange(dateRange);
    const enrollments = await this.getEnrollmentsInRange(dateRange);

    return {
      totalRevenue: payments.reduce((sum, p) => sum + p.amount, 0),
      revenueByPeriod: this.groupRevenueByPeriod(payments),
      averageOrderValue: this.calculateAverageOrderValue(payments),
      conversionRate: this.calculateConversionRate(enrollments, payments),
      refundRate: this.calculateRefundRate(payments),
      revenueBySource: this.groupRevenueBySource(payments),
      customerLifetimeValue: await this.calculateCustomerLifetimeValue(dateRange)
    };
  }

  /**
   * المقاييس الفورية
   */
  async getRealTimeMetrics() {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentInteractions = await this.getInteractionsInRange({
      start: last24Hours,
      end: now
    });

    return {
      currentActiveUsers: this.getCurrentActiveUsers(recentInteractions),
      liveInteractions: recentInteractions.slice(-100),
      currentPopularContent: this.getCurrentPopularContent(recentInteractions),
      realtimeErrors: recentInteractions.filter(i => i.type === 'error').slice(-10),
      currentTraffic: this.getCurrentTrafficMetrics(recentInteractions)
    };
  }

  /**
   * تحليل سلوك مستخدم محدد
   */
  async getUserBehaviorAnalysis(userId, timeRange = '30d') {
    const dateRange = this.getDateRange(timeRange);
    const userInteractions = await this.getUserInteractions(userId, dateRange);

    return {
      activitySummary: this.getUserActivitySummary(userInteractions),
      learningPattern: this.analyzeLearningPattern(userInteractions),
      engagementScore: this.calculateEngagementScore(userInteractions),
      preferredContent: this.getUserPreferredContent(userInteractions),
      studyHabits: this.analyzeStudyHabits(userInteractions),
      progressTracking: this.trackUserProgress(userInteractions),
      recommendations: await this.generateUserRecommendations(userId, userInteractions)
    };
  }

  /**
   * تحليل أداء كورس محدد
   */
  async getCourseAnalytics(courseId, timeRange = '30d') {
    const dateRange = this.getDateRange(timeRange);
    const courseInteractions = await this.getCourseInteractions(courseId, dateRange);
    const enrollments = await this.getCourseEnrollments(courseId, dateRange);

    return {
      enrollmentTrends: this.analyzeCourseEnrollmentTrends(enrollments),
      completionAnalysis: this.analyzeCourseCompletion(courseInteractions, enrollments),
      engagementMetrics: this.analyzeCourseEngagement(courseInteractions),
      dropoffPoints: this.identifyDropoffPoints(courseInteractions),
      studentFeedback: await this.getCourseStudentFeedback(courseId, dateRange),
      performanceComparison: await this.compareCoursePerformance(courseId, dateRange),
      revenueAnalysis: await this.analyzeCourseRevenue(courseId, dateRange)
    };
  }

  /**
   * تنبؤات ذكية
   */
  async generatePredictions(timeRange = '30d') {
    const historicalData = await this.getHistoricalData(timeRange);

    return {
      userGrowthPrediction: this.predictUserGrowth(historicalData),
      revenueForecast: this.predictRevenue(historicalData),
      churnPrediction: this.predictUserChurn(historicalData),
      contentDemandForecast: this.predictContentDemand(historicalData),
      seasonalTrends: this.identifySeasonalTrends(historicalData)
    };
  }

  /**
   * تقارير مخصصة
   */
  async generateCustomReport(config) {
    const {
      metrics,
      filters,
      timeRange,
      groupBy,
      format
    } = config;

    const data = await this.getFilteredData(filters, timeRange);
    const processedData = this.processDataByMetrics(data, metrics);
    const groupedData = this.groupDataBy(processedData, groupBy);

    const report = {
      metadata: {
        generatedAt: new Date().toISOString(),
        timeRange,
        filters,
        metrics
      },
      data: groupedData,
      summary: this.generateReportSummary(groupedData),
      insights: this.generateInsights(groupedData)
    };

    // حفظ التقرير
    await this.saveReport(report);

    return format === 'json' ? report : this.formatReport(report, format);
  }

  /**
   * مراقبة فورية
   */
  setupRealTimeMonitoring(callback) {
    const q = query(
      collection(db, 'user_interactions'),
      orderBy('batchTimestamp', 'desc'),
      limit(50)
    );

    return onSnapshot(q, (snapshot) => {
      const recentInteractions = [];
      snapshot.docs.forEach(doc => {
        const data = doc.data();
        recentInteractions.push(...data.interactions);
      });

      const realTimeMetrics = {
        activeUsers: this.getCurrentActiveUsers(recentInteractions),
        recentActivity: recentInteractions.slice(0, 20),
        alerts: this.checkForAlerts(recentInteractions),
        systemHealth: this.assessSystemHealth(recentInteractions)
      };

      callback(realTimeMetrics);
    });
  }

  /**
   * دوال مساعدة
   */
  getDateRange(timeRange) {
    const now = new Date();
    const ranges = {
      '1d': new Date(now.getTime() - 24 * 60 * 60 * 1000),
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      '1y': new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
    };

    return {
      start: ranges[timeRange] || ranges['7d'],
      end: now
    };
  }

  async getInteractionsInRange(dateRange) {
    const q = query(
      collection(db, 'user_interactions'),
      where('batchTimestamp', '>=', dateRange.start),
      where('batchTimestamp', '<=', dateRange.end),
      orderBy('batchTimestamp', 'desc')
    );

    const snapshot = await getDocs(q);
    const interactions = [];
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      interactions.push(...data.interactions);
    });

    return interactions;
  }

  async getUserInteractions(userId, dateRange) {
    const q = query(
      collection(db, 'user_interactions'),
      where('userId', '==', userId),
      where('batchTimestamp', '>=', dateRange.start),
      where('batchTimestamp', '<=', dateRange.end),
      orderBy('batchTimestamp', 'desc')
    );

    const snapshot = await getDocs(q);
    const interactions = [];
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      interactions.push(...data.interactions);
    });

    return interactions;
  }

  getActiveUsers(interactions) {
    const activeUserIds = new Set();
    const recentTime = Date.now() - (30 * 60 * 1000); // آخر 30 دقيقة

    interactions.forEach(interaction => {
      if (interaction.data.timestamp > recentTime && interaction.userId) {
        activeUserIds.add(interaction.userId);
      }
    });

    return activeUserIds.size;
  }

  calculateEngagementScore(interactions) {
    if (interactions.length === 0) return 0;

    const weights = {
      'click': 1,
      'scroll': 0.5,
      'video_interaction': 3,
      'course_interaction': 5,
      'search': 2,
      'time_spent': 1
    };

    let totalScore = 0;
    interactions.forEach(interaction => {
      const weight = weights[interaction.type] || 1;
      totalScore += weight;
    });

    return Math.min(100, (totalScore / interactions.length) * 10);
  }

  async saveReport(report) {
    try {
      await addDoc(collection(db, 'analytics_reports'), {
        ...report,
        createdAt: serverTimestamp()
      });
      console.log('📊 تم حفظ التقرير بنجاح');
    } catch (error) {
      console.error('❌ خطأ في حفظ التقرير:', error);
    }
  }

  checkForAlerts(interactions) {
    const alerts = [];
    
    // تحقق من معدل الأخطاء العالي
    const errors = interactions.filter(i => i.type === 'error');
    if (errors.length / interactions.length > 0.05) {
      alerts.push({
        type: 'high_error_rate',
        severity: 'high',
        message: 'معدل أخطاء عالي مكتشف',
        value: (errors.length / interactions.length * 100).toFixed(2) + '%'
      });
    }

    // تحقق من انخفاض النشاط
    const currentHourActivity = interactions.filter(i => 
      Date.now() - i.data.timestamp < 60 * 60 * 1000
    ).length;
    
    if (currentHourActivity < 10) {
      alerts.push({
        type: 'low_activity',
        severity: 'medium',
        message: 'انخفاض في النشاط',
        value: currentHourActivity
      });
    }

    return alerts;
  }
}

// إنشاء مثيل عام
const analyticsService = new AnalyticsService();

export default analyticsService;
