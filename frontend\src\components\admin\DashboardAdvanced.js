import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton
} from '@mui/material';
import {
  TrendingUp,
  People,
  School,
  VideoLibrary,
  WorkspacePremium,
  AttachMoney,
  PersonAdd,
  Payment,
  Visibility,
  MoreVert
} from '@mui/icons-material';
import { analyticsService } from '../../firebase/adminServices';

const StatCard = ({ title, value, icon, color, subtitle, trend }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="h6">
            {title}
          </Typography>
          <Typography variant="h4" component="h2" color={color}>
            {value}
          </Typography>
          {subtitle && (
            <Typography color="textSecondary" variant="body2">
              {subtitle}
            </Typography>
          )}
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <TrendingUp color="success" fontSize="small" />
              <Typography color="success.main" variant="body2" sx={{ ml: 0.5 }}>
                +{trend}% من الشهر الماضي
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

const DashboardAdvanced = () => {
  const [stats, setStats] = useState({});
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // استخدام Firebase Client SDK بدلاً من API
      const overviewStats = await analyticsService.getOverviewStats();
      const recentActivity = await analyticsService.getRecentActivity(10);

      setStats(overviewStats);
      setAnalytics({ recentActivity });
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error);
    } finally {
      setLoading(false);
    }
  };

  // بيانات مبسطة للعرض
  const enrollmentData = analytics.enrollments?.data || [12, 19, 8, 15, 22, 18, 25];
  const revenueData = analytics.revenue?.data || [3588, 5681, 2392, 4485, 6578, 5382, 7475];
  const progressData = {
    completed: analytics.studentProgress?.completed || 45,
    inProgress: analytics.studentProgress?.inProgress || 78,
    notStarted: analytics.studentProgress?.notStarted || 23
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>جاري تحميل بيانات لوحة التحكم...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        لوحة التحكم
      </Typography>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الطلاب"
            value={stats.totalStudents}
            icon={<People />}
            color="primary.main"
            subtitle={`${stats.activeStudents} نشط`}
            trend="12"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الكورسات"
            value={stats.totalCourses}
            icon={<School />}
            color="success.main"
            subtitle={`${stats.activeCourses} نشط`}
            trend="8"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الفيديوهات"
            value={stats.totalVideos}
            icon={<VideoLibrary />}
            color="info.main"
            subtitle="عبر جميع الكورسات"
            trend="15"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الإيرادات"
            value={`${stats.totalRevenue?.toLocaleString()} ريال`}
            icon={<AttachMoney />}
            color="warning.main"
            subtitle={`${stats.monthlyRevenue?.toLocaleString()} هذا الشهر`}
            trend="25"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* الإحصائيات المبسطة */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                التسجيلات الأسبوعية
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2 }}>
                {['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'].map((day, index) => (
                  <Box key={day} sx={{ textAlign: 'center', minWidth: 80 }}>
                    <Typography variant="body2" color="text.secondary">{day}</Typography>
                    <Typography variant="h6" color="primary">{enrollmentData[index]}</Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                الإيرادات الأسبوعية
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2 }}>
                {['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'].map((day, index) => (
                  <Box key={day} sx={{ textAlign: 'center', minWidth: 80 }}>
                    <Typography variant="body2" color="text.secondary">{day}</Typography>
                    <Typography variant="h6" color="success.main">{revenueData[index].toLocaleString()} ريال</Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* الأنشطة الحديثة وتقدم الطلاب */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                الأنشطة الحديثة
              </Typography>
              <List>
                {stats.recentActivity?.map((activity) => (
                  <ListItem key={activity.id} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {activity.icon === 'person_add' && <PersonAdd />}
                        {activity.icon === 'school' && <School />}
                        {activity.icon === 'workspace_premium' && <WorkspacePremium />}
                        {activity.icon === 'payment' && <Payment />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={activity.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {activity.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.time}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                تقدم الطلاب
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ width: 20, height: 20, bgcolor: 'success.main', mr: 1, borderRadius: 1 }}></Box>
                  <Typography variant="body2" sx={{ flexGrow: 1 }}>مكتمل</Typography>
                  <Typography variant="h6" color="success.main">{progressData.completed}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ width: 20, height: 20, bgcolor: 'warning.main', mr: 1, borderRadius: 1 }}></Box>
                  <Typography variant="body2" sx={{ flexGrow: 1 }}>قيد التقدم</Typography>
                  <Typography variant="h6" color="warning.main">{progressData.inProgress}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 20, height: 20, bgcolor: 'grey.400', mr: 1, borderRadius: 1 }}></Box>
                  <Typography variant="body2" sx={{ flexGrow: 1 }}>لم يبدأ</Typography>
                  <Typography variant="h6" color="text.secondary">{progressData.notStarted}</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* أفضل الكورسات */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            أفضل الكورسات أداءً
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم الكورس</TableCell>
                  <TableCell align="center">التسجيلات</TableCell>
                  <TableCell align="center">الإيرادات</TableCell>
                  <TableCell align="center">الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.topCourses?.map((course, index) => (
                  <TableRow key={index}>
                    <TableCell>{course.name}</TableCell>
                    <TableCell align="center">
                      <Chip label={course.enrollments} color="primary" size="small" />
                    </TableCell>
                    <TableCell align="center">
                      {course.revenue.toLocaleString()} ريال
                    </TableCell>
                    <TableCell align="center">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                      <IconButton size="small">
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DashboardAdvanced;
