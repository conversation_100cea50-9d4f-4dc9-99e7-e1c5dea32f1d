import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Refresh,
  TrendingUp,
  TrendingDown,
  People,
  Visibility,
  Mouse,
  TouchApp,
  Timer,
  Error,
  Warning,
  CheckCircle,
  Analytics,
  RealTimeIcon
} from '@mui/icons-material';
import analyticsService from '../../services/analyticsService';
import interactionTracker from '../../services/interactionTracker';
import toast from 'react-hot-toast';

const RealTimeAnalytics = () => {
  const [realTimeData, setRealTimeData] = useState(null);
  const [platformAnalytics, setPlatformAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('overview');

  // تحديث البيانات الفورية
  useEffect(() => {
    let unsubscribe;
    
    if (autoRefresh) {
      unsubscribe = analyticsService.setupRealTimeMonitoring((data) => {
        setRealTimeData(data);
        console.log('📊 تحديث البيانات الفورية:', data);
      });
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [autoRefresh]);

  // جلب التحليلات العامة
  useEffect(() => {
    fetchPlatformAnalytics();
  }, [timeRange]);

  const fetchPlatformAnalytics = async () => {
    try {
      setLoading(true);
      const analytics = await analyticsService.getPlatformAnalytics(timeRange);
      setPlatformAnalytics(analytics);
    } catch (error) {
      console.error('❌ خطأ في جلب التحليلات:', error);
      toast.error('فشل في جلب التحليلات');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = () => {
    fetchPlatformAnalytics();
    toast.success('تم تحديث البيانات');
  };

  const getMetricIcon = (type) => {
    const icons = {
      users: <People />,
      views: <Visibility />,
      clicks: <Mouse />,
      interactions: <TouchApp />,
      time: <Timer />,
      errors: <Error />,
      warnings: <Warning />,
      success: <CheckCircle />
    };
    return icons[type] || <Analytics />;
  };

  const getMetricColor = (value, threshold = 0) => {
    if (value > threshold) return 'success';
    if (value > threshold * 0.5) return 'warning';
    return 'error';
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
  };

  const formatDuration = (ms) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (loading && !platformAnalytics) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          جاري تحميل التحليلات...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Analytics color="primary" />
          التحليلات الفورية
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>الفترة الزمنية</InputLabel>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              label="الفترة الزمنية"
            >
              <MenuItem value="1d">آخر يوم</MenuItem>
              <MenuItem value="7d">آخر أسبوع</MenuItem>
              <MenuItem value="30d">آخر شهر</MenuItem>
              <MenuItem value="90d">آخر 3 أشهر</MenuItem>
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                color="primary"
              />
            }
            label="تحديث تلقائي"
          />
          
          <Tooltip title="تحديث البيانات">
            <IconButton onClick={refreshData} color="primary">
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* التنبيهات الفورية */}
      {realTimeData?.alerts && realTimeData.alerts.length > 0 && (
        <Box sx={{ mb: 3 }}>
          {realTimeData.alerts.map((alert, index) => (
            <Alert 
              key={index} 
              severity={alert.severity} 
              sx={{ mb: 1 }}
              icon={getMetricIcon(alert.type)}
            >
              <Typography variant="body2">
                <strong>{alert.message}</strong> - {alert.value}
              </Typography>
            </Alert>
          ))}
        </Box>
      )}

      {/* المقاييس الفورية */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'primary.light', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4">
                    {realTimeData?.currentActiveUsers || 0}
                  </Typography>
                  <Typography variant="body2">
                    المستخدمون النشطون الآن
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'success.light', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4">
                    {formatNumber(platformAnalytics?.overview?.totalUsers || 0)}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي المستخدمين
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'warning.light', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4">
                    {formatNumber(platformAnalytics?.userEngagement?.totalInteractions || 0)}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي التفاعلات
                  </Typography>
                </Box>
                <TouchApp sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'info.light', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4">
                    {formatDuration(platformAnalytics?.overview?.averageSessionDuration || 0)}
                  </Typography>
                  <Typography variant="body2">
                    متوسط مدة الجلسة
                  </Typography>
                </Box>
                <Timer sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التفاعلات الفورية */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                التفاعلات الفورية
              </Typography>
              
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>النوع</TableCell>
                      <TableCell>المستخدم</TableCell>
                      <TableCell>الوقت</TableCell>
                      <TableCell>التفاصيل</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {realTimeData?.liveInteractions?.slice(0, 10).map((interaction, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Chip 
                            label={interaction.type} 
                            size="small"
                            color={getMetricColor(1, 0)}
                          />
                        </TableCell>
                        <TableCell>
                          {interaction.userId?.substring(0, 8) || 'مجهول'}
                        </TableCell>
                        <TableCell>
                          {new Date(interaction.data?.timestamp).toLocaleTimeString('ar-SA')}
                        </TableCell>
                        <TableCell>
                          {interaction.data?.url?.split('/').pop() || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                أنواع التفاعلات
              </Typography>
              
              {platformAnalytics?.userEngagement?.interactionsByType && (
                <Box>
                  {Object.entries(platformAnalytics.userEngagement.interactionsByType).map(([type, count]) => (
                    <Box key={type} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{type}</Typography>
                        <Typography variant="body2">{formatNumber(count)}</Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={(count / Math.max(...Object.values(platformAnalytics.userEngagement.interactionsByType))) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* حالة النظام */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة النظام
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="success.main">
                      {((1 - (platformAnalytics?.technicalMetrics?.errorRate || 0)) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2">معدل النجاح</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="info.main">
                      {formatDuration(platformAnalytics?.technicalMetrics?.loadTimes?.average || 0)}
                    </Typography>
                    <Typography variant="body2">متوسط وقت التحميل</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="warning.main">
                      {((platformAnalytics?.overview?.bounceRate || 0) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2">معدل الارتداد</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="success.main">
                      {((platformAnalytics?.overview?.retentionRate || 0) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2">معدل الاحتفاظ</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RealTimeAnalytics;
