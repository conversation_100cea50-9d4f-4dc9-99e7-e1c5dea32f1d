import { useState, useEffect } from 'react';
import courseService from '../firebase/courseService';
import studentService from '../firebase/studentService';

/**
 * Hook للحصول على تحديثات فورية للكورسات
 */
export const useRealtimeCourses = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let unsubscribe = null;

    const setupRealtimeListener = async () => {
      try {
        // إعداد مستمع التحديثات الفورية
        unsubscribe = courseService.subscribeToCoursesUpdates((updatedCourses) => {
          setCourses(updatedCourses);
          setLoading(false);
        });
      } catch (err) {
        console.error('خطأ في إعداد مستمع الكورسات:', err);
        setError(err);
        setLoading(false);
        
        // Fallback: جلب البيانات مرة واحدة
        try {
          const coursesData = await courseService.getAllCourses();
          setCourses(coursesData);
        } catch (fallbackErr) {
          console.error('خطأ في جلب الكورسات:', fallbackErr);
        }
      }
    };

    setupRealtimeListener();

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  return { courses, loading, error };
};

/**
 * Hook للحصول على تحديثات فورية للطلاب
 */
export const useRealtimeStudents = () => {
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let unsubscribe = null;

    const setupRealtimeListener = async () => {
      try {
        // إعداد مستمع التحديثات الفورية
        unsubscribe = studentService.subscribeToStudentsUpdates((updatedStudents) => {
          setStudents(updatedStudents);
          setLoading(false);
        });
      } catch (err) {
        console.error('خطأ في إعداد مستمع الطلاب:', err);
        setError(err);
        setLoading(false);
        
        // Fallback: جلب البيانات مرة واحدة
        try {
          const studentsData = await studentService.getAllStudents();
          setStudents(studentsData);
        } catch (fallbackErr) {
          console.error('خطأ في جلب الطلاب:', fallbackErr);
        }
      }
    };

    setupRealtimeListener();

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  return { students, loading, error };
};

/**
 * Hook للحصول على فيديوهات كورس معين مع تحديثات فورية
 */
export const useRealtimeCourseVideos = (courseId) => {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!courseId) {
      setVideos([]);
      setLoading(false);
      return;
    }

    const fetchVideos = async () => {
      try {
        setLoading(true);
        const videosData = await courseService.getCourseVideos(courseId);
        setVideos(videosData);
      } catch (err) {
        console.error('خطأ في جلب فيديوهات الكورس:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, [courseId]);

  return { videos, loading, error, refetch: () => {
    if (courseId) {
      const fetchVideos = async () => {
        try {
          const videosData = await courseService.getCourseVideos(courseId);
          setVideos(videosData);
        } catch (err) {
          console.error('خطأ في إعادة جلب فيديوهات الكورس:', err);
        }
      };
      fetchVideos();
    }
  }};
};

/**
 * Hook للحصول على كورسات الطالب مع تحديثات فورية
 */
export const useRealtimeStudentCourses = (studentId) => {
  const [studentCourses, setStudentCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!studentId) {
      setStudentCourses([]);
      setLoading(false);
      return;
    }

    const fetchStudentCourses = async () => {
      try {
        setLoading(true);
        const { getStudentCourses } = await import('../firebase/databaseService');
        const coursesData = await getStudentCourses(studentId);
        setStudentCourses(coursesData);
      } catch (err) {
        console.error('خطأ في جلب كورسات الطالب:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchStudentCourses();
  }, [studentId]);

  return { studentCourses, loading, error, refetch: () => {
    if (studentId) {
      const fetchStudentCourses = async () => {
        try {
          const { getStudentCourses } = await import('../firebase/databaseService');
          const coursesData = await getStudentCourses(studentId);
          setStudentCourses(coursesData);
        } catch (err) {
          console.error('خطأ في إعادة جلب كورسات الطالب:', err);
        }
      };
      fetchStudentCourses();
    }
  }};
};

/**
 * Hook عام للتحديثات الفورية
 */
export const useRealtimeUpdates = () => {
  const coursesData = useRealtimeCourses();
  const studentsData = useRealtimeStudents();

  return {
    courses: coursesData,
    students: studentsData,
    isLoading: coursesData.loading || studentsData.loading,
    hasError: coursesData.error || studentsData.error
  };
};

export default {
  useRealtimeCourses,
  useRealtimeStudents,
  useRealtimeCourseVideos,
  useRealtimeStudentCourses,
  useRealtimeUpdates
};
