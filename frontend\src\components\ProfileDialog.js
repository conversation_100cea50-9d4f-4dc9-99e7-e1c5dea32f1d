import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  Button,
  Box,
  Typography,
  Avatar,
  IconButton,
  Grid,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  Close,
  Edit,
  Save,
  Cancel,
  Person
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { updateUserProfile, saveUserProfile } from '../firebase/profileService';
import toast from 'react-hot-toast';

const ProfileDialog = ({ open, onClose, user }) => {
  const { updateUser } = useAuth();
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    studentCode: ''
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        studentCode: user.studentCode || ''
      });
    }
  }, [user]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // تحديث البيانات في AuthContext
      const updatedUser = {
        ...user,
        ...formData
      };

      // حفظ في Firebase أولاً
      if (user?.id) {
        try {
          console.log('💾 حفظ البيانات في Firebase...', { userId: user.id, data: formData });

          // استخدام updateUserProfile لتحديث البيانات الموجودة
          await updateUserProfile(user.id, {
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            studentCode: formData.studentCode
          });

          console.log('✅ تم حفظ البيانات في Firebase بنجاح');
        } catch (firebaseError) {
          console.error('❌ خطأ في حفظ البيانات في Firebase:', firebaseError);

          // في حالة فشل التحديث، جرب إنشاء ملف جديد
          try {
            await saveUserProfile(user.id, {
              name: formData.name,
              email: formData.email,
              phone: formData.phone,
              studentCode: formData.studentCode,
              role: user.role || 'student'
            });
            console.log('✅ تم إنشاء ملف شخصي جديد في Firebase');
          } catch (createError) {
            console.error('❌ فشل في إنشاء الملف الشخصي:', createError);
            // المتابعة مع الحفظ المحلي فقط
          }
        }
      }

      // تحديث البيانات محلياً
      updateUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));

      setEditing(false);
      toast.success('تم تحديث الملف الشخصي بنجاح!');

      console.log('✅ تم تحديث الملف الشخصي:', updatedUser);
    } catch (error) {
      console.error('❌ خطأ في تحديث الملف الشخصي:', error);
      toast.error('فشل في تحديث الملف الشخصي');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      studentCode: user?.studentCode || ''
    });
    setEditing(false);
  };

  const handleClose = () => {
    handleCancel();
    onClose();
  };

  if (!user) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 1
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#0000FF' }}>
          الملف الشخصي
        </Typography>
        <IconButton onClick={handleClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Avatar
            sx={{
              width: 80,
              height: 80,
              mx: 'auto',
              mb: 2,
              bgcolor: '#0000FF',
              fontSize: '2rem'
            }}
          >
            <Person sx={{ fontSize: '2.5rem' }} />
          </Avatar>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            {formData.name || 'اسم الطالب'}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            كود الطالب: {formData.studentCode}
          </Typography>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="الاسم الكامل"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              disabled={!editing}
              variant={editing ? "outlined" : "filled"}
              sx={{
                '& .MuiFilledInput-root': {
                  backgroundColor: editing ? 'transparent' : '#f5f5f5'
                }
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="البريد الإلكتروني"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              disabled={!editing}
              variant={editing ? "outlined" : "filled"}
              sx={{
                '& .MuiFilledInput-root': {
                  backgroundColor: editing ? 'transparent' : '#f5f5f5'
                }
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="رقم الهاتف"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              disabled={!editing}
              variant={editing ? "outlined" : "filled"}
              sx={{
                '& .MuiFilledInput-root': {
                  backgroundColor: editing ? 'transparent' : '#f5f5f5'
                }
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="كود الطالب"
              value={formData.studentCode}
              disabled={true}
              variant="filled"
              sx={{
                '& .MuiFilledInput-root': {
                  backgroundColor: '#f0f0f0'
                }
              }}
              helperText="لا يمكن تغيير كود الطالب"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2, gap: 1 }}>
        {editing ? (
          <>
            <Button
              onClick={handleCancel}
              startIcon={<Cancel />}
              sx={{ color: '#666' }}
            >
              إلغاء
            </Button>
            <Button
              onClick={handleSave}
              variant="contained"
              startIcon={saving ? <CircularProgress size={16} color="inherit" /> : <Save />}
              disabled={saving}
              sx={{
                bgcolor: '#0000FF',
                '&:hover': { bgcolor: '#0000CC' },
                '&:disabled': { bgcolor: '#cccccc' }
              }}
            >
              {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </Button>
          </>
        ) : (
          <Button
            onClick={() => setEditing(true)}
            variant="contained"
            startIcon={<Edit />}
            sx={{
              bgcolor: '#0000FF',
              '&:hover': { bgcolor: '#0000CC' }
            }}
          >
            تعديل الملف الشخصي
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ProfileDialog;
