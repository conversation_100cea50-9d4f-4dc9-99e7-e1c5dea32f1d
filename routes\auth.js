const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { auth } = require('../middleware/auth');

const router = express.Router();

// إنشاء توكن JWT
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

// تسجيل دخول المدير
router.post('/admin/login', [
  body('email').isEmail().withMessage('البريد الإلكتروني غير صالح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // البحث عن المدير
    const admin = await User.findOne({ email, role: 'admin' });
    if (!admin) {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    // التحقق من كلمة المرور
    const isMatch = await admin.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    // التحقق من أن الحساب مفعل
    if (!admin.isActive) {
      return res.status(401).json({ message: 'الحساب غير مفعل' });
    }

    // تحديث آخر تسجيل دخول
    admin.lastLogin = new Date();
    await admin.save();

    // إنشاء التوكن
    const token = generateToken(admin._id);

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول المدير:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل دخول الطالب بالكود
router.post('/student/login', [
  body('code').isLength({ min: 6, max: 6 }).withMessage('الكود يجب أن يكون 6 أرقام')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { code } = req.body;

    // البحث عن الطالب بالكود
    const student = await User.findOne({ studentCode: code, role: 'student' });
    if (!student) {
      return res.status(401).json({ message: 'كود غير صحيح' });
    }

    // التحقق من أن الحساب مفعل
    if (!student.isActive) {
      return res.status(401).json({ message: 'الحساب غير مفعل' });
    }

    // تحديث آخر تسجيل دخول
    student.lastLogin = new Date();
    await student.save();

    // إنشاء التوكن
    const token = generateToken(student._id);

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: student._id,
        name: student.name,
        role: student.role,
        studentCode: student.studentCode
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على بيانات المستخدم الحالي
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .select('-password')
      .populate('enrolledCourses.courseId', 'title thumbnail')
      .populate('certificates');

    res.json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        studentCode: user.studentCode,
        enrolledCourses: user.enrolledCourses,
        completedCourses: user.completedCourses,
        certificates: user.certificates,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('خطأ في الحصول على بيانات المستخدم:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل الخروج
router.post('/logout', auth, async (req, res) => {
  try {
    // في التطبيقات الحقيقية، يمكن إضافة التوكن إلى قائمة سوداء
    res.json({ message: 'تم تسجيل الخروج بنجاح' });
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تغيير كلمة المرور (للمديرين)
router.put('/change-password', [
  auth,
  body('currentPassword').notEmpty().withMessage('كلمة المرور الحالية مطلوبة'),
  body('newPassword').isLength({ min: 6 }).withMessage('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;
    const user = await User.findById(req.user.id);

    if (user.role !== 'admin') {
      return res.status(403).json({ message: 'هذه الميزة متاحة للمديرين فقط' });
    }

    // التحقق من كلمة المرور الحالية
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({ message: 'كلمة المرور الحالية غير صحيحة' });
    }

    // تحديث كلمة المرور
    user.password = newPassword;
    await user.save();

    res.json({ message: 'تم تغيير كلمة المرور بنجاح' });
  } catch (error) {
    console.error('خطأ في تغيير كلمة المرور:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
