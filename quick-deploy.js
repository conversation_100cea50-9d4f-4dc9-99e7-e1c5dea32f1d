#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 إعداد المشروع للنشر السريع...');

// Create deployment package
const deploymentInfo = {
  name: 'منصة كورسات علاء عبد الحميد',
  version: '1.0.0',
  description: 'منصة تعليمية متخصصة في التسويق الرقمي',
  author: 'علاء عبد الحميد',
  timestamp: new Date().toISOString(),
  status: 'ready-for-deployment'
};

// Write deployment info
fs.writeFileSync(
  path.join(__dirname, 'deployment-info.json'),
  JSON.stringify(deploymentInfo, null, 2)
);

console.log('✅ تم إعداد المشروع بنجاح!');
console.log('');
console.log('🎯 المشروع جاهز للنشر على:');
console.log('   ✓ Render.com');
console.log('   ✓ Heroku');
console.log('   ✓ DigitalOcean');
console.log('   ✓ AWS');
console.log('   ✓ أي منصة تدعم Node.js');
console.log('');
console.log('📋 الملفات المهمة:');
console.log('   ✓ production-server.js - الخادم الرئيسي');
console.log('   ✓ package.json - إعدادات المشروع');
console.log('   ✓ .env.production - متغيرات الإنتاج');
console.log('   ✓ Dockerfile - للنشر بـ Docker');
console.log('   ✓ render.yaml - للنشر على Render');
console.log('   ✓ Procfile - للنشر على Heroku');
console.log('');
console.log('🔧 خطوات النشر:');
console.log('1. إنشاء قاعدة بيانات MongoDB Atlas');
console.log('2. اختيار منصة النشر (Render موصى به)');
console.log('3. ربط المستودع');
console.log('4. تكوين متغيرات البيئة');
console.log('5. نشر المشروع');
console.log('');
console.log('🌐 بعد النشر:');
console.log('   📱 الموقع: https://your-app.onrender.com');
console.log('   🔍 اختبار: https://your-app.onrender.com/api/health');
console.log('   👨‍💼 دخول المدير: <EMAIL> / Admin123!');
console.log('   👨‍🎓 أكواد الطلاب: 123456, 789012');
console.log('');
console.log('📖 للمزيد من التفاصيل، راجع: deploy-guide.md');
console.log('');
console.log('🎉 المشروع جاهز للنشر! حظاً موفقاً!');

// Test server locally
console.log('');
console.log('🧪 لاختبار المشروع محلياً:');
console.log('   node production-server.js');
console.log('   ثم افتح: http://localhost:5000');
console.log('');
