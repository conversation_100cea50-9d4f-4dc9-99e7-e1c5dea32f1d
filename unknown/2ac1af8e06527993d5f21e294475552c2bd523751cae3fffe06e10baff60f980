import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  collection,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './config';

// خدمة إدارة بيانات المستخدمين في Firebase

/**
 * جلب بيانات المستخدم من Firebase
 * @param {string} userId - معرف المستخدم
 * @returns {Object|null} بيانات المستخدم أو null إذا لم توجد
 */
export const getUserProfile = async (userId) => {
  try {
    if (!userId) {
      console.error('User ID is required');
      return null;
    }

    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('تم جلب بيانات المستخدم بنجاح:', userData);
      return {
        id: userDoc.id,
        ...userData
      };
    } else {
      console.log('لا توجد بيانات للمستخدم');
      return null;
    }
  } catch (error) {
    console.error('خطأ في جلب بيانات المستخدم:', error);
    throw error;
  }
};

/**
 * إنشاء ملف شخصي جديد للمستخدم
 * @param {string} userId - معرف المستخدم
 * @param {Object} profileData - بيانات الملف الشخصي
 * @returns {boolean} true إذا تم الإنشاء بنجاح
 */
export const createUserProfile = async (userId, profileData) => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const userDocRef = doc(db, 'users', userId);
    
    const defaultProfileData = {
      name: profileData.name || 'طالب جديد',
      email: profileData.email || '',
      phone: profileData.phone || '',
      bio: profileData.bio || 'طالب في منصة SKILLS WORLD ACADEMY',
      studentCode: profileData.studentCode || '',
      enrolledCourses: [],
      completedCourses: [],
      certificates: [],
      totalWatchTime: 0,
      lastLoginAt: serverTimestamp(),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      isActive: true,
      role: 'student'
    };

    await setDoc(userDocRef, {
      ...defaultProfileData,
      ...profileData
    });

    console.log('تم إنشاء الملف الشخصي بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في إنشاء الملف الشخصي:', error);
    throw error;
  }
};

/**
 * تحديث بيانات الملف الشخصي
 * @param {string} userId - معرف المستخدم
 * @param {Object} updates - البيانات المراد تحديثها
 * @returns {boolean} true إذا تم التحديث بنجاح
 */
export const updateUserProfile = async (userId, updates) => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const userDocRef = doc(db, 'users', userId);
    
    // إضافة timestamp للتحديث
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };

    await updateDoc(userDocRef, updateData);
    console.log('تم تحديث الملف الشخصي بنجاح:', updates);
    return true;
  } catch (error) {
    console.error('خطأ في تحديث الملف الشخصي:', error);
    throw error;
  }
};

/**
 * تحديث آخر وقت دخول للمستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {boolean} true إذا تم التحديث بنجاح
 */
export const updateLastLogin = async (userId) => {
  try {
    if (!userId) {
      return false;
    }

    const userDocRef = doc(db, 'users', userId);
    await updateDoc(userDocRef, {
      lastLoginAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('خطأ في تحديث آخر وقت دخول:', error);
    return false;
  }
};

/**
 * إضافة كورس للطالب
 * @param {string} userId - معرف المستخدم
 * @param {string} courseId - معرف الكورس
 * @returns {boolean} true إذا تم التحديث بنجاح
 */
export const enrollUserInCourse = async (userId, courseId) => {
  try {
    if (!userId || !courseId) {
      throw new Error('User ID and Course ID are required');
    }

    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const enrolledCourses = userData.enrolledCourses || [];
      
      // التحقق من عدم وجود الكورس مسبقاً
      if (!enrolledCourses.includes(courseId)) {
        enrolledCourses.push(courseId);
        
        await updateDoc(userDocRef, {
          enrolledCourses,
          updatedAt: serverTimestamp()
        });
        
        console.log('تم تسجيل الطالب في الكورس بنجاح');
        return true;
      } else {
        console.log('الطالب مسجل في هذا الكورس مسبقاً');
        return true;
      }
    } else {
      throw new Error('المستخدم غير موجود');
    }
  } catch (error) {
    console.error('خطأ في تسجيل الطالب في الكورس:', error);
    throw error;
  }
};

/**
 * تحديث تقدم الطالب في كورس معين
 * @param {string} userId - معرف المستخدم
 * @param {string} courseId - معرف الكورس
 * @param {Object} progressData - بيانات التقدم
 * @returns {boolean} true إذا تم التحديث بنجاح
 */
export const updateCourseProgress = async (userId, courseId, progressData) => {
  try {
    if (!userId || !courseId) {
      throw new Error('User ID and Course ID are required');
    }

    const progressDocRef = doc(db, 'userProgress', `${userId}_${courseId}`);
    
    const progressUpdate = {
      userId,
      courseId,
      ...progressData,
      updatedAt: serverTimestamp()
    };

    await setDoc(progressDocRef, progressUpdate, { merge: true });
    console.log('تم تحديث تقدم الطالب بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في تحديث تقدم الطالب:', error);
    throw error;
  }
};

/**
 * جلب تقدم الطالب في جميع الكورسات
 * @param {string} userId - معرف المستخدم
 * @returns {Array} قائمة بتقدم الطالب في الكورسات
 */
export const getUserProgress = async (userId) => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    // يمكن تطوير هذه الدالة لاحقاً لجلب تقدم الطالب
    // من مجموعة userProgress
    return [];
  } catch (error) {
    console.error('خطأ في جلب تقدم الطالب:', error);
    throw error;
  }
};

/**
 * التحقق من وجود المستخدم وإنشاء ملف شخصي إذا لم يكن موجوداً
 * @param {string} userId - معرف المستخدم
 * @param {Object} initialData - البيانات الأولية
 * @returns {Object} بيانات المستخدم
 */
export const ensureUserProfile = async (userId, initialData = {}) => {
  try {
    let userProfile = await getUserProfile(userId);
    
    if (!userProfile) {
      // إنشاء ملف شخصي جديد إذا لم يكن موجوداً
      await createUserProfile(userId, initialData);
      userProfile = await getUserProfile(userId);
    }
    
    return userProfile;
  } catch (error) {
    console.error('خطأ في التحقق من الملف الشخصي:', error);
    throw error;
  }
};

// تصدير جميع الدوال
export default {
  getUserProfile,
  createUserProfile,
  updateUserProfile,
  updateLastLogin,
  enrollUserInCourse,
  updateCourseProgress,
  getUserProgress,
  ensureUserProfile
};
