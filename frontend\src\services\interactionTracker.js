import { db } from '../firebase/config';
import { collection, addDoc, serverTimestamp, onSnapshot, query, where, orderBy, limit } from 'firebase/firestore';

/**
 * نظام تتبع التفاعلات الشامل
 * يسجل كل تفاعل للمستخدم في قاعدة البيانات
 */
class InteractionTracker {
  constructor() {
    this.userId = null;
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    this.interactions = [];
    this.isTracking = false;
    this.batchSize = 10; // عدد التفاعلات قبل الحفظ
    this.saveInterval = 30000; // حفظ كل 30 ثانية
    
    this.initializeTracking();
  }

  /**
   * تهيئة نظام التتبع
   */
  initializeTracking() {
    // تتبع حركة الماوس
    document.addEventListener('mousemove', this.throttle(this.trackMouseMove.bind(this), 1000));
    
    // تتبع النقرات
    document.addEventListener('click', this.trackClick.bind(this));
    
    // تتبع التمرير
    window.addEventListener('scroll', this.throttle(this.trackScroll.bind(this), 500));
    
    // تتبع تغيير الصفحة
    window.addEventListener('beforeunload', this.trackPageExit.bind(this));
    
    // تتبع الوقت المقضي
    this.startTimeTracking();
    
    // حفظ دوري للتفاعلات
    setInterval(() => {
      this.saveBatchInteractions();
    }, this.saveInterval);
    
    console.log('🔍 تم تفعيل نظام تتبع التفاعلات');
  }

  /**
   * تعيين معرف المستخدم
   */
  setUserId(userId) {
    this.userId = userId;
    this.trackInteraction('user_login', {
      userId: userId,
      sessionId: this.sessionId
    });
  }

  /**
   * تتبع تفاعل عام
   */
  trackInteraction(type, data = {}) {
    const interaction = {
      id: this.generateInteractionId(),
      userId: this.userId,
      sessionId: this.sessionId,
      type: type,
      data: {
        ...data,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        viewportSize: `${window.innerWidth}x${window.innerHeight}`
      },
      createdAt: new Date().toISOString()
    };

    this.interactions.push(interaction);
    
    // حفظ فوري للتفاعلات المهمة
    const criticalInteractions = ['user_login', 'course_enrollment', 'payment_completed', 'certificate_downloaded'];
    if (criticalInteractions.includes(type)) {
      this.saveInteractionImmediately(interaction);
    }
    
    // حفظ دفعي عند الوصول للحد الأقصى
    if (this.interactions.length >= this.batchSize) {
      this.saveBatchInteractions();
    }

    // تسجيل التفاعلات المهمة فقط
    if (!['mouse_move'].includes(type)) {
      console.log(`📊 تفاعل جديد: ${type}`, data);
    }
  }

  /**
   * الحصول على className بشكل آمن
   */
  getElementClass(element) {
    try {
      if (!element) return '';

      // معالجة SVGAnimatedString
      if (element.className && typeof element.className === 'object' && element.className.baseVal) {
        return element.className.baseVal;
      }

      // معالجة className العادي
      if (typeof element.className === 'string') {
        return element.className;
      }

      return '';
    } catch (error) {
      console.warn('خطأ في الحصول على className:', error);
      return '';
    }
  }

  /**
   * تتبع حركة الماوس
   */
  trackMouseMove(event) {
    this.trackInteraction('mouse_move', {
      x: event.clientX,
      y: event.clientY,
      element: event.target.tagName,
      elementId: event.target.id || '',
      elementClass: this.getElementClass(event.target)
    });
  }

  /**
   * تتبع النقرات
   */
  trackClick(event) {
    this.trackInteraction('click', {
      x: event.clientX,
      y: event.clientY,
      element: event.target.tagName,
      elementId: event.target.id || '',
      elementClass: this.getElementClass(event.target),
      elementText: event.target.textContent?.substring(0, 100) || '',
      button: event.button // 0=يسار، 1=وسط، 2=يمين
    });
  }

  /**
   * تتبع التمرير
   */
  trackScroll() {
    this.trackInteraction('scroll', {
      scrollY: window.scrollY,
      scrollX: window.scrollX,
      documentHeight: document.documentElement.scrollHeight,
      viewportHeight: window.innerHeight,
      scrollPercentage: Math.round((window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100)
    });
  }

  /**
   * تتبع مشاهدة الفيديو
   */
  trackVideoInteraction(videoId, action, data = {}) {
    this.trackInteraction('video_interaction', {
      videoId,
      action, // play, pause, seek, ended, etc.
      currentTime: data.currentTime || 0,
      duration: data.duration || 0,
      playbackRate: data.playbackRate || 1,
      volume: data.volume || 1,
      quality: data.quality || 'auto'
    });
  }

  /**
   * تتبع تفاعل الكورس
   */
  trackCourseInteraction(courseId, action, data = {}) {
    this.trackInteraction('course_interaction', {
      courseId,
      action, // view, enroll, complete, rate, etc.
      lessonId: data.lessonId,
      progress: data.progress || 0,
      timeSpent: data.timeSpent || 0,
      rating: data.rating
    });
  }

  /**
   * تتبع البحث
   */
  trackSearch(query, results = []) {
    this.trackInteraction('search', {
      query,
      resultsCount: results.length,
      results: results.slice(0, 5).map(r => ({ id: r.id, title: r.title }))
    });
  }

  /**
   * تتبع الأخطاء
   */
  trackError(error, context = {}) {
    this.trackInteraction('error', {
      message: error.message,
      stack: error.stack,
      context,
      severity: context.severity || 'medium'
    });
  }

  /**
   * تتبع الأداء
   */
  trackPerformance(metric, value, context = {}) {
    this.trackInteraction('performance', {
      metric, // page_load_time, api_response_time, etc.
      value,
      context
    });
  }

  /**
   * تتبع خروج من الصفحة
   */
  trackPageExit() {
    const timeSpent = Date.now() - this.startTime;
    this.trackInteraction('page_exit', {
      timeSpent,
      url: window.location.href
    });
    
    // حفظ جميع التفاعلات المتبقية
    this.saveBatchInteractions();
  }

  /**
   * تتبع الوقت المقضي
   */
  startTimeTracking() {
    setInterval(() => {
      if (document.visibilityState === 'visible') {
        this.trackInteraction('time_spent', {
          duration: 60000, // دقيقة واحدة
          isActive: true
        });
      }
    }, 60000); // كل دقيقة
  }

  /**
   * حفظ التفاعلات بشكل دفعي
   */
  async saveBatchInteractions() {
    if (this.interactions.length === 0) return;

    const batch = [...this.interactions];
    this.interactions = []; // مسح المصفوفة

    try {
      // تجنب حفظ التفاعلات إذا لم يكن هناك مستخدم
      if (!this.userId) {
        // تقليل رسائل التحذير للتفاعلات غير المهمة
        if (batch.some(interaction => ['user_login', 'course_enrollment', 'payment_completed'].includes(interaction.type))) {
          console.log('⚠️ تخطي حفظ التفاعلات المهمة - لا يوجد مستخدم');
        }
        return;
      }

      // حفظ في Firestore مع معالجة أفضل للأخطاء
      await addDoc(collection(db, 'user_interactions'), {
        userId: this.userId,
        sessionId: this.sessionId,
        interactions: batch,
        batchTimestamp: serverTimestamp(),
        createdAt: new Date().toISOString(),
        interactionCount: batch.length
      });

      console.log(`💾 تم حفظ ${batch.length} تفاعل في قاعدة البيانات`);
    } catch (error) {
      console.warn('⚠️ تحذير: فشل في حفظ التفاعلات:', error.message);
      // لا نعيد التفاعلات للمصفوفة لتجنب تراكمها
      // this.interactions.unshift(...batch);
    }
  }

  /**
   * حفظ تفاعل فوري
   */
  async saveInteractionImmediately(interaction) {
    try {
      // تجنب حفظ التفاعلات إذا لم يكن هناك مستخدم
      if (!this.userId) {
        // عرض تحذير فقط للتفاعلات الحرجة
        if (['user_login', 'course_enrollment', 'payment_completed', 'certificate_downloaded'].includes(interaction.type)) {
          console.log('⚠️ تخطي حفظ التفاعل الحرج - لا يوجد مستخدم:', interaction.type);
        }
        return;
      }

      await addDoc(collection(db, 'critical_interactions'), {
        ...interaction,
        timestamp: serverTimestamp(),
        savedAt: new Date().toISOString()
      });
      console.log(`⚡ تم حفظ تفاعل حرج فوري: ${interaction.type}`);
    } catch (error) {
      console.warn('⚠️ تحذير: فشل في حفظ التفاعل الفوري:', error.message);
    }
  }

  /**
   * جلب تفاعلات المستخدم
   */
  async getUserInteractions(userId, limit = 100) {
    try {
      const q = query(
        collection(db, 'user_interactions'),
        where('userId', '==', userId),
        orderBy('batchTimestamp', 'desc'),
        limit(limit)
      );

      return new Promise((resolve) => {
        onSnapshot(q, (snapshot) => {
          const interactions = [];
          snapshot.docs.forEach(doc => {
            const data = doc.data();
            interactions.push(...data.interactions);
          });
          resolve(interactions);
        });
      });
    } catch (error) {
      console.error('❌ خطأ في جلب التفاعلات:', error);
      return [];
    }
  }

  /**
   * تحليل سلوك المستخدم
   */
  async analyzeUserBehavior(userId) {
    const interactions = await this.getUserInteractions(userId, 1000);
    
    const analysis = {
      totalInteractions: interactions.length,
      sessionCount: new Set(interactions.map(i => i.sessionId)).size,
      mostActiveHours: this.getMostActiveHours(interactions),
      preferredContent: this.getPreferredContent(interactions),
      averageSessionDuration: this.getAverageSessionDuration(interactions),
      clickHeatmap: this.generateClickHeatmap(interactions),
      scrollBehavior: this.analyzeScrollBehavior(interactions),
      videoEngagement: this.analyzeVideoEngagement(interactions)
    };

    return analysis;
  }

  /**
   * مساعدات للتحليل
   */
  getMostActiveHours(interactions) {
    const hours = {};
    interactions.forEach(i => {
      const hour = new Date(i.data.timestamp).getHours();
      hours[hour] = (hours[hour] || 0) + 1;
    });
    return Object.entries(hours).sort(([,a], [,b]) => b - a).slice(0, 3);
  }

  getPreferredContent(interactions) {
    const content = {};
    interactions.filter(i => i.type === 'course_interaction').forEach(i => {
      const courseId = i.data.courseId;
      content[courseId] = (content[courseId] || 0) + 1;
    });
    return Object.entries(content).sort(([,a], [,b]) => b - a).slice(0, 5);
  }

  getAverageSessionDuration(interactions) {
    const sessions = {};
    interactions.forEach(i => {
      if (!sessions[i.sessionId]) {
        sessions[i.sessionId] = { start: i.data.timestamp, end: i.data.timestamp };
      } else {
        sessions[i.sessionId].end = Math.max(sessions[i.sessionId].end, i.data.timestamp);
      }
    });
    
    const durations = Object.values(sessions).map(s => s.end - s.start);
    return durations.reduce((a, b) => a + b, 0) / durations.length;
  }

  generateClickHeatmap(interactions) {
    const clicks = interactions.filter(i => i.type === 'click');
    const heatmap = {};
    
    clicks.forEach(click => {
      const x = Math.floor(click.data.x / 50) * 50; // تجميع بوحدات 50 بكسل
      const y = Math.floor(click.data.y / 50) * 50;
      const key = `${x},${y}`;
      heatmap[key] = (heatmap[key] || 0) + 1;
    });
    
    return heatmap;
  }

  analyzeScrollBehavior(interactions) {
    const scrolls = interactions.filter(i => i.type === 'scroll');
    if (scrolls.length === 0) return {};
    
    const maxScroll = Math.max(...scrolls.map(s => s.data.scrollPercentage));
    const avgScroll = scrolls.reduce((sum, s) => sum + s.data.scrollPercentage, 0) / scrolls.length;
    
    return { maxScroll, avgScroll, totalScrolls: scrolls.length };
  }

  analyzeVideoEngagement(interactions) {
    const videos = interactions.filter(i => i.type === 'video_interaction');
    const engagement = {};
    
    videos.forEach(v => {
      const videoId = v.data.videoId;
      if (!engagement[videoId]) {
        engagement[videoId] = { plays: 0, pauses: 0, seeks: 0, completions: 0 };
      }
      engagement[videoId][v.data.action + 's'] = (engagement[videoId][v.data.action + 's'] || 0) + 1;
    });
    
    return engagement;
  }

  /**
   * مساعدات عامة
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateInteractionId() {
    return 'interaction_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }
}

// إنشاء مثيل عام
const interactionTracker = new InteractionTracker();

export default interactionTracker;
