import {
  doc,
  getDoc,
  updateDoc,
  addDoc,
  query,
  where,
  collection,
  getDocs,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './config';

// خدمة المصادقة الحقيقية مع Firebase

/**
 * البحث عن مستخدم بواسطة بيانات الاعتماد
 * @param {Object} credentials - بيانات الاعتماد
 * @returns {Promise<Object|null>} بيانات المستخدم أو null
 */
export const getUserByCredentials = async (credentials) => {
  try {
    console.log('🔍 البحث عن المستخدم في Firebase...', credentials);

    let userQuery;
    
    // البحث بكود الطالب
    if (credentials.studentCode) {
      userQuery = query(
        collection(db, 'profiles'),
        where('studentCode', '==', credentials.studentCode)
      );
    }
    // البحث بالبريد الإلكتروني
    else if (credentials.email) {
      userQuery = query(
        collection(db, 'profiles'),
        where('email', '==', credentials.email)
      );
    }
    else {
      throw new Error('يجب توفير كود الطالب أو البريد الإلكتروني');
    }

    const querySnapshot = await getDocs(userQuery);
    
    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0];
      const userData = {
        id: userDoc.id,
        ...userDoc.data()
      };
      
      console.log('✅ تم العثور على المستخدم:', userData);
      return userData;
    } else {
      console.log('❌ لم يتم العثور على المستخدم');
      return null;
    }
  } catch (error) {
    console.error('❌ خطأ في البحث عن المستخدم:', error);
    throw error;
  }
};

/**
 * تحديث آخر دخول للمستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<boolean>} true إذا تم التحديث بنجاح
 */
export const updateLastLogin = async (userId) => {
  try {
    console.log('🔄 تحديث آخر دخول...', userId);

    const userRef = doc(db, 'profiles', userId);
    await updateDoc(userRef, {
      lastLoginAt: serverTimestamp()
    });

    console.log('✅ تم تحديث آخر دخول بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تحديث آخر دخول:', error);
    throw error;
  }
};

/**
 * التحقق من صحة كود الطالب
 * @param {string} studentCode - كود الطالب
 * @returns {Promise<Object|null>} بيانات الطالب أو null
 */
export const validateStudentCode = async (studentCode) => {
  try {
    console.log('🔍 التحقق من كود الطالب...', studentCode);

    const userQuery = query(
      collection(db, 'profiles'),
      where('studentCode', '==', studentCode),
      where('role', '==', 'student')
    );

    const querySnapshot = await getDocs(userQuery);
    
    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0];
      const userData = {
        id: userDoc.id,
        ...userDoc.data()
      };
      
      console.log('✅ كود الطالب صحيح:', userData);
      return userData;
    } else {
      console.log('❌ كود الطالب غير صحيح');
      return null;
    }
  } catch (error) {
    console.error('❌ خطأ في التحقق من كود الطالب:', error);
    throw error;
  }
};

/**
 * التحقق من صحة بيانات المدير
 * @param {string} email - البريد الإلكتروني
 * @param {string} password - كلمة المرور (للتحقق المحلي)
 * @returns {Promise<Object|null>} بيانات المدير أو null
 */
export const validateAdminCredentials = async (email, password) => {
  try {
    console.log('🔍 التحقق من بيانات المدير...', email);

    const userQuery = query(
      collection(db, 'profiles'),
      where('email', '==', email),
      where('role', '==', 'admin')
    );

    const querySnapshot = await getDocs(userQuery);
    
    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0];
      const userData = {
        id: userDoc.id,
        ...userDoc.data()
      };
      
      // للتطوير: التحقق من كلمة المرور محلياً
      // في الإنتاج: يجب استخدام Firebase Auth
      const validPasswords = ['admin123', 'password', '123456'];
      if (validPasswords.includes(password)) {
        console.log('✅ بيانات المدير صحيحة:', userData);
        return userData;
      } else {
        console.log('❌ كلمة المرور غير صحيحة');
        return null;
      }
    } else {
      console.log('❌ بيانات المدير غير صحيحة');
      return null;
    }
  } catch (error) {
    console.error('❌ خطأ في التحقق من بيانات المدير:', error);
    throw error;
  }
};

/**
 * جلب بيانات المستخدم الكاملة
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Object|null>} بيانات المستخدم الكاملة
 */
export const getFullUserData = async (userId) => {
  try {
    console.log('📥 جلب بيانات المستخدم الكاملة...', userId);

    // جلب الملف الشخصي
    const profileRef = doc(db, 'profiles', userId);
    const profileSnap = await getDoc(profileRef);
    
    if (!profileSnap.exists()) {
      console.log('❌ الملف الشخصي غير موجود');
      return null;
    }

    const profileData = profileSnap.data();

    // جلب تقدم الطالب إذا كان طالباً
    let progressData = [];
    if (profileData.role === 'student') {
      const progressQuery = query(
        collection(db, 'userProgress'),
        where('userId', '==', userId)
      );
      const progressSnapshot = await getDocs(progressQuery);
      progressData = progressSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    }

    const fullUserData = {
      id: profileSnap.id,
      ...profileData,
      progress: progressData
    };

    console.log('✅ تم جلب بيانات المستخدم الكاملة:', fullUserData);
    return fullUserData;
  } catch (error) {
    console.error('❌ خطأ في جلب بيانات المستخدم:', error);
    throw error;
  }
};

/**
 * تحديث إحصائيات المستخدم
 * @param {string} userId - معرف المستخدم
 * @param {Object} stats - الإحصائيات الجديدة
 * @returns {Promise<boolean>} true إذا تم التحديث بنجاح
 */
export const updateUserStats = async (userId, stats) => {
  try {
    console.log('📊 تحديث إحصائيات المستخدم...', { userId, stats });

    const userRef = doc(db, 'profiles', userId);
    await updateDoc(userRef, {
      ...stats,
      lastUpdated: serverTimestamp()
    });

    console.log('✅ تم تحديث الإحصائيات بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تحديث الإحصائيات:', error);
    throw error;
  }
};

/**
 * تسجيل نشاط المستخدم
 * @param {string} userId - معرف المستخدم
 * @param {string} action - نوع النشاط
 * @param {Object} details - تفاصيل النشاط
 * @returns {Promise<boolean>} true إذا تم التسجيل بنجاح
 */
export const logUserActivity = async (userId, action, details = {}) => {
  try {
    console.log('📝 تسجيل نشاط المستخدم...', { userId, action, details });

    const activityData = {
      userId,
      action,
      details,
      timestamp: serverTimestamp(),
      createdAt: new Date().toISOString() // للفرز المحلي
    };

    const activityRef = collection(db, 'userActivity');
    await addDoc(activityRef, activityData);

    console.log('✅ تم تسجيل النشاط بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تسجيل النشاط:', error);
    // لا نرمي خطأ هنا لأن تسجيل النشاط ليس حرجاً
    return false;
  }
};

/**
 * إنشاء نشاطات افتراضية للطالب الجديد
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<boolean>} true إذا تم الإنشاء بنجاح
 */
export const createDefaultActivities = async (userId) => {
  try {
    console.log('🔧 إنشاء نشاطات افتراضية للطالب...', userId);

    const now = new Date();
    const activities = [
      {
        userId,
        action: 'login',
        details: { description: 'تسجيل الدخول إلى المنصة' },
        timestamp: serverTimestamp(),
        createdAt: now.toISOString()
      },
      {
        userId,
        action: 'profile_created',
        details: { description: 'إنشاء الملف الشخصي' },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 5 * 60 * 1000).toISOString() // منذ 5 دقائق
      },
      {
        userId,
        action: 'course_enrolled',
        details: {
          description: 'التسجيل في دورة: أساسيات التسويق الرقمي',
          courseId: 'course1',
          courseTitle: 'أساسيات التسويق الرقمي'
        },
        timestamp: serverTimestamp(),
        createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString() // منذ ساعتين
      }
    ];

    const activityRef = collection(db, 'userActivity');

    for (const activity of activities) {
      await addDoc(activityRef, activity);
    }

    console.log('✅ تم إنشاء النشاطات الافتراضية بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء النشاطات الافتراضية:', error);
    return false;
  }
};

export default {
  getUserByCredentials,
  updateLastLogin,
  validateStudentCode,
  validateAdminCredentials,
  getFullUserData,
  updateUserStats,
  logUserActivity,
  createDefaultActivities
};
