import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  IconButton,
  Button,
  Chip,
  Divider,
  Menu,
  MenuItem,
  Badge
} from '@mui/material';
import {
  Notifications as NotificationIcon,
  MoreVert as MoreIcon,
  Delete as DeleteIcon,
  DoneAll as MarkAllReadIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useRealtimeNotifications } from '../../hooks/useRealtimeNotifications';

const NotificationPanel = () => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications
  } = useRealtimeNotifications();

  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedNotification, setSelectedNotification] = useState(null);

  const handleMenuOpen = (event, notification) => {
    setAnchorEl(event.currentTarget);
    setSelectedNotification(notification);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedNotification(null);
  };

  const handleMarkAsRead = (notificationId) => {
    markAsRead(notificationId);
    handleMenuClose();
  };

  const handleDelete = (notificationId) => {
    removeNotification(notificationId);
    handleMenuClose();
  };

  const getNotificationIcon = (type) => {
    const iconMap = {
      student_registration: '👨‍🎓',
      course_creation: '📚',
      video_upload: '🎥',
      student_enrollment: '✅',
      system_update: '🔄',
      error: '⚠️',
      data_change: '🔔',
      data_update: '🔄'
    };
    return iconMap[type] || '🔔';
  };

  const getPriorityColor = (priority) => {
    const colorMap = {
      high: 'error',
      medium: 'warning',
      low: 'info'
    };
    return colorMap[priority] || 'default';
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const diff = now - new Date(timestamp);
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'الآن';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    return `منذ ${days} يوم`;
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <NotificationIcon />
            <Typography variant="h6">
              الإشعارات
            </Typography>
            {unreadCount > 0 && (
              <Badge badgeContent={unreadCount} color="error" />
            )}
          </Box>
          
          <Box>
            {unreadCount > 0 && (
              <Button
                size="small"
                startIcon={<MarkAllReadIcon />}
                onClick={markAllAsRead}
                sx={{ mr: 1 }}
              >
                تمييز الكل كمقروء
              </Button>
            )}
            <Button
              size="small"
              startIcon={<ClearIcon />}
              onClick={clearAllNotifications}
              color="error"
            >
              مسح الكل
            </Button>
          </Box>
        </Box>

        {notifications.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography variant="body2" color="text.secondary">
              لا توجد إشعارات
            </Typography>
          </Box>
        ) : (
          <List>
            {notifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  sx={{
                    bgcolor: notification.read ? 'transparent' : 'action.hover',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'transparent' }}>
                      {getNotificationIcon(notification.type)}
                    </Avatar>
                  </ListItemAvatar>
                  
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle2">
                          {notification.title}
                        </Typography>
                        <Chip
                          label={notification.priority}
                          size="small"
                          color={getPriorityColor(notification.priority)}
                          variant="outlined"
                        />
                        {!notification.read && (
                          <Chip
                            label="جديد"
                            size="small"
                            color="primary"
                            variant="filled"
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTime(notification.timestamp)}
                        </Typography>
                      </Box>
                    }
                  />
                  
                  <IconButton
                    edge="end"
                    onClick={(e) => handleMenuOpen(e, notification)}
                  >
                    <MoreIcon />
                  </IconButton>
                </ListItem>
                
                {index < notifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}

        {/* قائمة الإجراءات */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {selectedNotification && !selectedNotification.read && (
            <MenuItem onClick={() => handleMarkAsRead(selectedNotification.id)}>
              <MarkAllReadIcon sx={{ mr: 1 }} />
              تمييز كمقروء
            </MenuItem>
          )}
          <MenuItem onClick={() => handleDelete(selectedNotification?.id)}>
            <DeleteIcon sx={{ mr: 1 }} />
            حذف
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
};

export default NotificationPanel;
