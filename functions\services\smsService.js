const twilio = require('twilio');
const functions = require('firebase-functions');

// إعدادات Twilio
const TWILIO_ACCOUNT_SID = functions.config().twilio?.account_sid || process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = functions.config().twilio?.auth_token || process.env.TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = functions.config().twilio?.phone_number || process.env.TWILIO_PHONE_NUMBER;

// تهيئة عميل Twilio
let twilioClient = null;

if (TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN) {
  twilioClient = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
}

/**
 * خدمة إرسال الرسائل النصية
 */
class SMSService {
  /**
   * التحقق من توفر خدمة SMS
   */
  static isAvailable() {
    return twilioClient !== null && TWILIO_PHONE_NUMBER;
  }

  /**
   * إرسال رسالة SMS
   * @param {string} to - رقم الهاتف المستقبل
   * @param {string} message - نص الرسالة
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  static async sendSMS(to, message) {
    try {
      if (!this.isAvailable()) {
        throw new Error('خدمة SMS غير متاحة - يرجى التحقق من إعدادات Twilio');
      }

      // تنسيق رقم الهاتف
      const formattedPhone = this.formatPhoneNumber(to);
      
      console.log(`📱 إرسال SMS إلى: ${formattedPhone}`);
      console.log(`📝 النص: ${message}`);

      // إرسال الرسالة عبر Twilio
      const result = await twilioClient.messages.create({
        body: message,
        from: TWILIO_PHONE_NUMBER,
        to: formattedPhone
      });

      console.log(`✅ تم إرسال SMS بنجاح - SID: ${result.sid}`);

      return {
        success: true,
        messageId: result.sid,
        status: result.status,
        to: formattedPhone,
        sentAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ خطأ في إرسال SMS:', error);
      
      return {
        success: false,
        error: error.message,
        code: error.code || 'SMS_SEND_ERROR',
        to: to
      };
    }
  }

  /**
   * إرسال كلمة المرور للمدير
   * @param {string} phoneNumber - رقم هاتف المدير
   * @param {string} password - كلمة المرور
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  static async sendPasswordReset(phoneNumber, password) {
    const message = this.createPasswordResetMessage(password);
    return await this.sendSMS(phoneNumber, message);
  }

  /**
   * إنشاء نص رسالة استرداد كلمة المرور
   * @param {string} password - كلمة المرور
   * @returns {string} نص الرسالة
   */
  static createPasswordResetMessage(password) {
    return `🔐 SKILLS WORLD ACADEMY

كلمة المرور الخاصة بك:
${password}

⚠️ لا تشارك كلمة المرور مع أي شخص
🔒 احتفظ بها في مكان آمن

للدعم: ALAA <EMAIL>
📞 0506747770`;
  }

  /**
   * تنسيق رقم الهاتف للصيغة الدولية
   * @param {string} phoneNumber - رقم الهاتف
   * @returns {string} رقم الهاتف المنسق
   */
  static formatPhoneNumber(phoneNumber) {
    // إزالة المسافات والرموز
    let cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    // تحويل للصيغة الدولية
    if (cleanPhone.startsWith('05')) {
      return '+966' + cleanPhone.substring(1);
    } else if (cleanPhone.startsWith('+966')) {
      return cleanPhone;
    } else if (cleanPhone.startsWith('966')) {
      return '+' + cleanPhone;
    } else if (cleanPhone.startsWith('00966')) {
      return '+' + cleanPhone.substring(2);
    }
    
    return cleanPhone;
  }

  /**
   * التحقق من صحة رقم الهاتف السعودي
   * @param {string} phoneNumber - رقم الهاتف
   * @returns {boolean} صحة الرقم
   */
  static validateSaudiPhone(phoneNumber) {
    const cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    const saudiPatterns = [
      /^05\d{8}$/, // 05xxxxxxxx
      /^\+9665\d{8}$/, // +9665xxxxxxxx
      /^9665\d{8}$/, // 9665xxxxxxxx
      /^00966\d{9}$/ // 00966xxxxxxxxx
    ];
    
    return saudiPatterns.some(pattern => pattern.test(cleanPhone));
  }

  /**
   * إرسال رسالة اختبار
   * @param {string} phoneNumber - رقم الهاتف
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  static async sendTestMessage(phoneNumber) {
    const message = `🧪 رسالة اختبار من SKILLS WORLD ACADEMY

هذه رسالة اختبار للتأكد من عمل خدمة SMS.

الوقت: ${new Date().toLocaleString('ar-SA')}

إذا وصلتك هذه الرسالة، فإن الخدمة تعمل بشكل صحيح.`;

    return await this.sendSMS(phoneNumber, message);
  }

  /**
   * الحصول على حالة الرسالة
   * @param {string} messageId - معرف الرسالة
   * @returns {Promise<Object>} حالة الرسالة
   */
  static async getMessageStatus(messageId) {
    try {
      if (!this.isAvailable()) {
        throw new Error('خدمة SMS غير متاحة');
      }

      const message = await twilioClient.messages(messageId).fetch();
      
      return {
        success: true,
        status: message.status,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage,
        dateCreated: message.dateCreated,
        dateSent: message.dateSent,
        dateUpdated: message.dateUpdated
      };

    } catch (error) {
      console.error('❌ خطأ في جلب حالة الرسالة:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * إحصائيات الرسائل المرسلة
   * @param {Date} startDate - تاريخ البداية
   * @param {Date} endDate - تاريخ النهاية
   * @returns {Promise<Object>} إحصائيات الرسائل
   */
  static async getMessagesStats(startDate, endDate) {
    try {
      if (!this.isAvailable()) {
        throw new Error('خدمة SMS غير متاحة');
      }

      const messages = await twilioClient.messages.list({
        dateSentAfter: startDate,
        dateSentBefore: endDate,
        limit: 1000
      });

      const stats = {
        total: messages.length,
        sent: 0,
        delivered: 0,
        failed: 0,
        pending: 0
      };

      messages.forEach(message => {
        switch (message.status) {
          case 'sent':
            stats.sent++;
            break;
          case 'delivered':
            stats.delivered++;
            break;
          case 'failed':
          case 'undelivered':
            stats.failed++;
            break;
          default:
            stats.pending++;
        }
      });

      return {
        success: true,
        stats,
        messages: messages.map(msg => ({
          sid: msg.sid,
          to: msg.to,
          status: msg.status,
          dateSent: msg.dateSent,
          errorCode: msg.errorCode
        }))
      };

    } catch (error) {
      console.error('❌ خطأ في جلب إحصائيات الرسائل:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = SMSService;
