import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  writeBatch,
  onSnapshot
} from 'firebase/firestore';
import { db } from './config';
import { logActivity, generateStudentCode } from './databaseService';

// ===== خدمة إدارة الطلاب =====

/**
 * إنشاء طالب جديد
 */
export const createStudent = async (studentData, adminId) => {
  try {
    const studentCode = await generateStudentCode();
    
    const newStudent = {
      ...studentData,
      studentCode,
      role: 'student',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, 'users'), newStudent);
    
    // تسجيل النشاط
    await logActivity(adminId, 'student_created', {
      studentId: docRef.id,
      studentName: studentData.name,
      studentCode
    });

    return {
      id: docRef.id,
      ...newStudent,
      studentCode
    };
  } catch (error) {
    console.error('خطأ في إنشاء الطالب:', error);
    throw error;
  }
};

/**
 * تحديث بيانات طالب
 */
export const updateStudent = async (studentId, updateData, adminId) => {
  try {
    const studentRef = doc(db, 'users', studentId);
    
    const updatedData = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(studentRef, updatedData);
    
    // تسجيل النشاط
    await logActivity(adminId, 'student_updated', {
      studentId,
      studentName: updateData.name
    });

    return true;
  } catch (error) {
    console.error('خطأ في تحديث الطالب:', error);
    throw error;
  }
};

/**
 * حذف طالب
 */
export const deleteStudent = async (studentId, adminId) => {
  try {
    const batch = writeBatch(db);
    
    // حذف الطالب
    const studentRef = doc(db, 'users', studentId);
    batch.delete(studentRef);
    
    // حذف التسجيلات المرتبطة
    const enrollmentsQuery = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId)
    );
    const enrollmentsSnapshot = await getDocs(enrollmentsQuery);
    
    enrollmentsSnapshot.docs.forEach(enrollmentDoc => {
      batch.delete(enrollmentDoc.ref);
    });
    
    // حذف التقدم المرتبط
    const progressQuery = query(
      collection(db, 'progress'),
      where('studentId', '==', studentId)
    );
    const progressSnapshot = await getDocs(progressQuery);
    
    progressSnapshot.docs.forEach(progressDoc => {
      batch.delete(progressDoc.ref);
    });

    await batch.commit();
    
    // تسجيل النشاط
    await logActivity(adminId, 'student_deleted', {
      studentId
    });

    return true;
  } catch (error) {
    console.error('خطأ في حذف الطالب:', error);
    throw error;
  }
};

/**
 * تفعيل/إلغاء تفعيل طالب
 */
export const toggleStudentStatus = async (studentId, isActive, adminId) => {
  try {
    const studentRef = doc(db, 'users', studentId);
    
    await updateDoc(studentRef, {
      isActive,
      updatedAt: serverTimestamp()
    });
    
    // تسجيل النشاط
    await logActivity(adminId, isActive ? 'student_activated' : 'student_deactivated', {
      studentId
    });

    return true;
  } catch (error) {
    console.error('خطأ في تغيير حالة الطالب:', error);
    throw error;
  }
};

/**
 * جلب جميع الطلاب
 */
export const getAllStudents = async () => {
  try {
    const studentsQuery = query(
      collection(db, 'users'),
      where('role', '==', 'student'),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(studentsQuery);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
    }));
  } catch (error) {
    console.error('خطأ في جلب الطلاب:', error);
    throw error;
  }
};

/**
 * البحث عن طالب بكود التسجيل
 */
export const findStudentByCode = async (studentCode) => {
  try {
    const studentQuery = query(
      collection(db, 'users'),
      where('studentCode', '==', studentCode),
      where('role', '==', 'student'),
      limit(1)
    );
    
    const snapshot = await getDocs(studentQuery);
    
    if (snapshot.empty) {
      return null;
    }

    const studentDoc = snapshot.docs[0];
    return {
      id: studentDoc.id,
      ...studentDoc.data(),
      createdAt: studentDoc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: studentDoc.data().updatedAt?.toDate?.() || new Date()
    };
  } catch (error) {
    console.error('خطأ في البحث عن الطالب:', error);
    throw error;
  }
};

/**
 * جلب طالب محدد
 */
export const getStudent = async (studentId) => {
  try {
    const studentDoc = await getDoc(doc(db, 'users', studentId));
    
    if (!studentDoc.exists()) {
      throw new Error('الطالب غير موجود');
    }

    return {
      id: studentDoc.id,
      ...studentDoc.data(),
      createdAt: studentDoc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: studentDoc.data().updatedAt?.toDate?.() || new Date()
    };
  } catch (error) {
    console.error('خطأ في جلب الطالب:', error);
    throw error;
  }
};

/**
 * تسجيل طالب في كورس
 */
export const enrollStudentInCourse = async (studentId, courseId, adminId) => {
  try {
    // التحقق من عدم وجود تسجيل مسبق
    const existingEnrollment = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId),
      where('courseId', '==', courseId),
      limit(1)
    );
    
    const existingSnapshot = await getDocs(existingEnrollment);
    
    if (!existingSnapshot.empty) {
      throw new Error('الطالب مسجل في هذا الكورس مسبقاً');
    }

    // إنشاء تسجيل جديد
    const enrollmentData = {
      studentId,
      courseId,
      enrolledAt: serverTimestamp(),
      progress: 0,
      completedVideos: [],
      lastWatchedVideo: null,
      totalWatchTime: 0
    };

    const docRef = await addDoc(collection(db, 'enrollments'), enrollmentData);
    
    // تحديث عدد الكورسات المسجل فيها الطالب
    const studentRef = doc(db, 'users', studentId);
    const studentDoc = await getDoc(studentRef);
    
    if (studentDoc.exists()) {
      const currentEnrolled = studentDoc.data().enrolledCourses || 0;
      await updateDoc(studentRef, {
        enrolledCourses: currentEnrolled + 1,
        updatedAt: serverTimestamp()
      });
    }
    
    // تحديث عدد الطلاب المسجلين في الكورس
    const courseRef = doc(db, 'courses', courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (courseDoc.exists()) {
      const currentEnrolled = courseDoc.data().enrolledStudents || 0;
      await updateDoc(courseRef, {
        enrolledStudents: currentEnrolled + 1,
        updatedAt: serverTimestamp()
      });
    }
    
    // تسجيل النشاط
    await logActivity(adminId, 'student_enrolled', {
      studentId,
      courseId,
      enrollmentId: docRef.id
    });

    return {
      id: docRef.id,
      ...enrollmentData
    };
  } catch (error) {
    console.error('خطأ في تسجيل الطالب في الكورس:', error);
    throw error;
  }
};

/**
 * إلغاء تسجيل طالب من كورس
 */
export const unenrollStudentFromCourse = async (studentId, courseId, adminId) => {
  try {
    // البحث عن التسجيل
    const enrollmentQuery = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId),
      where('courseId', '==', courseId),
      limit(1)
    );
    
    const enrollmentSnapshot = await getDocs(enrollmentQuery);
    
    if (enrollmentSnapshot.empty) {
      throw new Error('الطالب غير مسجل في هذا الكورس');
    }

    const enrollmentDoc = enrollmentSnapshot.docs[0];
    await deleteDoc(enrollmentDoc.ref);
    
    // تحديث عدد الكورسات المسجل فيها الطالب
    const studentRef = doc(db, 'users', studentId);
    const studentDoc = await getDoc(studentRef);
    
    if (studentDoc.exists()) {
      const currentEnrolled = studentDoc.data().enrolledCourses || 0;
      await updateDoc(studentRef, {
        enrolledCourses: Math.max(0, currentEnrolled - 1),
        updatedAt: serverTimestamp()
      });
    }
    
    // تحديث عدد الطلاب المسجلين في الكورس
    const courseRef = doc(db, 'courses', courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (courseDoc.exists()) {
      const currentEnrolled = courseDoc.data().enrolledStudents || 0;
      await updateDoc(courseRef, {
        enrolledStudents: Math.max(0, currentEnrolled - 1),
        updatedAt: serverTimestamp()
      });
    }
    
    // تسجيل النشاط
    await logActivity(adminId, 'student_unenrolled', {
      studentId,
      courseId
    });

    return true;
  } catch (error) {
    console.error('خطأ في إلغاء تسجيل الطالب من الكورس:', error);
    throw error;
  }
};

/**
 * الاستماع للتحديثات الفورية للطلاب
 */
export const subscribeToStudentsUpdates = (callback) => {
  try {
    const studentsQuery = query(
      collection(db, 'users'),
      where('role', '==', 'student'),
      orderBy('createdAt', 'desc')
    );
    
    return onSnapshot(studentsQuery, (snapshot) => {
      const students = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));
      
      callback(students);
    });
  } catch (error) {
    console.error('خطأ في الاستماع لتحديثات الطلاب:', error);
    throw error;
  }
};

export default {
  createStudent,
  updateStudent,
  deleteStudent,
  toggleStudentStatus,
  getAllStudents,
  findStudentByCode,
  getStudent,
  enrollStudentInCourse,
  unenrollStudentFromCourse,
  subscribeToStudentsUpdates
};
