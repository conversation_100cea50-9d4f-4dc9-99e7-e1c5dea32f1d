# منصة كورسات علاء عبد الحميد

منصة تعليمية متكاملة لكورسات التسويق مع لوحة تحكم متقدمة ونظام إدارة شامل.

## 🌟 المميزات

### للمدير:
- ✅ لوحة تحكم جمالية وسهلة الاستخدام
- ✅ إضافة/تعديل/حذف الكورسات
- ✅ إنشاء أقسام الكورسات
- ✅ إنشاء أكواد دخول للطلاب (6 أرقام)
- ✅ إدارة الشهادات وإصدارها
- ✅ تتبع تقدم الطلاب

### للطلاب:
- ✅ تسجيل دخول بكود 6 أرقام
- ✅ مشاهدة الكورسات بجودة عالية
- ✅ تتبع التقدم وإكمال الكورسات
- ✅ الحصول على الشهادات
- ✅ واجهة جميلة ومتجاوبة

## 🛠️ التقنيات المستخدمة

### الخادم الخلفي (Backend):
- Node.js + Express
- MongoDB + Mongoose
- JWT للمصادقة
- Bcrypt للتشفير
- Multer لرفع الملفات
- PDFKit للشهادات

### الواجهة الأمامية (Frontend):
- React.js
- Material-UI (MUI)
- React Router
- Axios
- React Hot Toast

### قاعدة البيانات:
- MongoDB (محلية أو سحابية)

## 🚀 التشغيل السريع

### 1. باستخدام Docker (الطريقة الأسهل):

```bash
# تشغيل جميع الخدمات
docker-compose up -d

# الوصول للموقع
http://localhost:3000
```

### 2. التشغيل اليدوي:

#### تشغيل الخادم الخلفي:
```bash
# تثبيت المكتبات
npm install

# تشغيل الخادم
npm run dev
```

#### تشغيل الواجهة الأمامية:
```bash
# الانتقال لمجلد الواجهة
cd frontend

# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm start
```

## 🔐 بيانات الدخول الافتراضية

### المدير:
- البريد الإلكتروني: `<EMAIL>`
# منصة كورسات علاء عبد الحميد 📚

منصة تعليمية شاملة لإدارة الكورسات والطلاب مع لوحة تحكم متقدمة.

## 🚀 متطلبات النظام

### 1. البرامج المطلوبة
- **Node.js** (الإصدار 16 أو أحدث)
- **npm** أو **yarn**
- **Git**
- متصفح حديث (Chrome, Firefox, Safari, Edge)

### 2. حساب Firebase
- مشروع Firebase نشط
- Firestore Database مُفعّل
- Authentication مُفعّل

## 🔧 خطوات الإعداد

### الخطوة 1: تحميل المشروع
```bash
git clone [repository-url]
cd كوسات/frontend
```

### الخطوة 2: تثبيت التبعيات
```bash
npm install
```

### الخطوة 3: إعداد Firebase
1. انتقل إلى [Firebase Console](https://console.firebase.google.com)
2. أنشئ مشروع جديد أو استخدم المشروع الموجود
3. فعّل Firestore Database
4. فعّل Authentication
5. انسخ إعدادات Firebase

### الخطوة 4: إعداد متغيرات البيئة
أنشئ ملف `.env` في مجلد `frontend` مع المحتوى التالي:

```env
# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id

# App Configuration
REACT_APP_TITLE=منصة كورسات علاء عبد الحميد
REACT_APP_VERSION=1.0.0
```

### الخطوة 5: تشغيل المشروع
```bash
npm start
```

## 🗄️ إعداد قاعدة البيانات

### الإعداد السريع (موصى به)
1. افتح المتصفح على `http://localhost:3000`
2. اضغط F12 لفتح وحدة التحكم
3. شغّل الأمر التالي:
```javascript
quickSetup()
```

هذا سيقوم بـ:
- مسح البيانات الموجودة
- إنشاء حساب المدير
- إنشاء طلاب تجريبيين
- إنشاء كورس تجريبي

### الإعداد اليدوي
إذا كنت تفضل الإعداد اليدوي:
```javascript
resetDatabase()
```

## 👤 بيانات تسجيل الدخول

### المدير
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `Admin123!`

### الطلاب التجريبيين
- **أحمد محمد علي:** `123456`
- **فاطمة أحمد حسن:** `654321`
- **محمد عبد الله:** `111111`

## 🧪 اختبار النظام

### اختبار تسجيل دخول الطلاب
```javascript
testStudentLogin('123456')
```

### فحص بيانات الطلاب
```javascript
checkStudentData()
```

### البحث عن طالب بالكود
```javascript
checkStudentByCode('123456')
```

## 🏗️ هيكل المشروع

```
frontend/
├── public/
├── src/
│   ├── components/
│   │   ├── admin/          # مكونات لوحة المدير
│   │   ├── Login.js        # صفحة تسجيل الدخول
│   │   ├── AdminDashboard.js
│   │   └── StudentDashboard.js
│   ├── contexts/
│   │   └── AuthContext.js  # إدارة المصادقة
│   ├── firebase/
│   │   ├── config.js       # إعدادات Firebase
│   │   ├── authServiceNew.js
│   │   ├── studentService.js
│   │   └── databaseService.js
│   ├── utils/
│   │   ├── quickSetup.js   # إعداد سريع
│   │   └── resetDatabase.js
│   └── debug/
│       └── checkStudentData.js
└── package.json
```

## 🔧 حل المشاكل الشائعة

### مشكلة تسجيل دخول الطلاب
إذا لم يتمكن الطلاب من تسجيل الدخول:
1. تأكد من تشغيل `quickSetup()`
2. تحقق من وجود الطلاب: `checkStudentData()`
3. اختبر كود محدد: `testStudentLogin('123456')`

### مشكلة الاتصال بـ Firebase
1. تحقق من صحة متغيرات البيئة
2. تأكد من تفعيل Firestore
3. تحقق من قواعد الأمان في Firebase

### أخطاء التبعيات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

## 📱 الميزات

### لوحة المدير
- ✅ إدارة الطلاب (إضافة، تعديل، حذف)
- ✅ إدارة الكورسات
- ✅ نظام الشهادات
- ✅ الأسئلة الشائعة
- ✅ نظام الدردشة
- ✅ التقارير والإحصائيات

### لوحة الطالب
- ✅ عرض الكورسات المسجل بها
- ✅ تتبع التقدم
- ✅ الشهادات
- ✅ الملف الشخصي
- ✅ الدعم الفني

### الميزات التقنية
- ✅ واجهة مستخدم متجاوبة (RTL)
- ✅ Firebase Firestore
- ✅ مصادقة آمنة
- ✅ تحديثات فورية
- ✅ نظام إشعارات

## 🚀 النشر

### Firebase Hosting
```bash
npm run build
firebase deploy
```

### Vercel
```bash
npm run build
vercel --prod
```

## 📞 الدعم

للحصول على المساعدة:
- **البريد الإلكتروني:** ALAA <EMAIL>
- **الهاتف:** 0506747770

## 📄 الترخيص

هذا المشروع مملوك لـ **علاء عبد الحميد** - جميع الحقوق محفوظة.

### الطالب:
- سيتم إنشاء أكواد الطلاب من قبل المدير

## 📁 هيكل المشروع

```
alaa-courses/
├── backend/
│   ├── models/          # نماذج قاعدة البيانات
│   ├── routes/          # مسارات API
│   ├── middleware/      # وسطاء المصادقة
│   └── server.js        # الخادم الرئيسي
├── frontend/
│   ├── src/
│   │   ├── components/  # مكونات React
│   │   ├── contexts/    # سياقات التطبيق
│   │   └── App.js       # التطبيق الرئيسي
│   └── public/
├── docker-compose.yml   # إعداد Docker
└── README.md
```

## 🌐 الروابط المهمة

- الواجهة الأمامية: http://localhost:3000
- API الخادم: http://localhost:5000
- قاعدة البيانات: mongodb://localhost:27017

## 📊 قاعدة البيانات

### المجموعات (Collections):
- `users` - المستخدمين (مديرين وطلاب)
- `categories` - أقسام الكورسات
- `courses` - الكورسات والدروس
- `progress` - تقدم الطلاب
- `certificates` - الشهادات

## 🔧 الإعدادات

### متغيرات البيئة (.env):
```env
MONGODB_URI=mongodb://localhost:27017/alaa-courses
JWT_SECRET=your-secret-key
PORT=5000
NODE_ENV=development
```

## 🚀 النشر

### 🔥 النشر على Firebase (موصى به):

المشروع جاهز للنشر على Firebase مع جميع الملفات المطلوبة!

```bash
# تشغيل سكريبت النشر التلقائي
node firebase-deploy.js

# أو النشر اليدوي
firebase login
firebase use --add
firebase deploy
```

**📖 دليل مفصل**: راجع `FIREBASE-DEPLOY-GUIDE.md`

**🔗 الموقع بعد النشر**: `https://alaa-courses-platform.web.app`

### النشر التلقائي على Render:

#### 1. إعداد قاعدة البيانات:
```bash
# إنشاء حساب على MongoDB Atlas
# https://cloud.mongodb.com

# نسخ رابط الاتصال وإضافته في متغيرات البيئة
```

#### 2. النشر على Render:
```bash
# رفع الكود على GitHub
git add .
git commit -m "إعداد المشروع للنشر"
git push origin main

# ربط المستودع بـ Render
# https://render.com
# استخدام ملف render.yaml للتكوين التلقائي
```

#### 3. تكوين متغيرات البيئة:
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/alaa-courses
JWT_SECRET=your-secure-jwt-secret
FRONTEND_URL=https://your-app.onrender.com
```

### النشر باستخدام Docker:

#### للتطوير:
```bash
# تشغيل جميع الخدمات
npm run docker:dev

# أو
docker-compose up -d
```

#### للإنتاج:
```bash
# تشغيل في وضع الإنتاج
npm run docker:prod

# أو
docker-compose -f docker-compose.prod.yml up -d
```

### النشر اليدوي:

#### على خادم محلي:
```bash
# بناء الواجهة الأمامية
npm run build

# تشغيل الخادم في وضع الإنتاج
NODE_ENV=production npm start
```

#### على السحابة:
- **Render**: استخدم ملف `render.yaml`
- **Heroku**: استخدم `Procfile`
- **DigitalOcean**: استخدم Docker
- **AWS**: استخدم Elastic Beanstalk أو ECS
- **قاعدة البيانات**: MongoDB Atlas
- **الملفات**: AWS S3 أو Cloudinary

## 🛡️ الأمان

- تشفير كلمات المرور باستخدام bcrypt
- مصادقة JWT آمنة
- حماية من CORS
- تحديد معدل الطلبات
- تنظيف المدخلات

## 📱 التجاوب

- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- واجهة مستخدم حديثة وجميلة

## 🔄 التحديثات المستقبلية

- [ ] نظام الدفع الإلكتروني
- [ ] تطبيق الهاتف المحمول
- [ ] نظام الاختبارات والتقييم
- [ ] منتدى النقاش
- [ ] نظام الإشعارات
- [ ] تحليلات متقدمة

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم تطوير هذا المشروع بواسطة فريق علاء عبد الحميد للتسويق**
