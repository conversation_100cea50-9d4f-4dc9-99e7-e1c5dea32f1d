import { collection, getDocs, deleteDoc, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * مسح جميع البيانات من قاعدة البيانات
 */
export const clearAllData = async () => {
  try {
    console.log('🗑️ بدء مسح جميع البيانات...');

    // مسح جميع المجموعات
    const collections = ['users', 'courses', 'enrollments', 'progress', 'activities', 'certificates', 'faqs', 'chats'];
    
    for (const collectionName of collections) {
      try {
        const snapshot = await getDocs(collection(db, collectionName));
        console.log(`📂 مسح مجموعة ${collectionName} - ${snapshot.size} عنصر`);
        
        for (const doc of snapshot.docs) {
          await deleteDoc(doc.ref);
        }
        
        console.log(`✅ تم مسح مجموعة ${collectionName}`);
      } catch (error) {
        console.log(`⚠️ خطأ في مسح مجموعة ${collectionName}:`, error.message);
      }
    }

    console.log('✅ تم مسح جميع البيانات بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في مسح البيانات:', error);
    throw error;
  }
};

/**
 * إعادة تهيئة قاعدة البيانات بالبيانات الأساسية
 */
export const initializeCleanDatabase = async () => {
  try {
    console.log('🔧 إعادة تهيئة قاعدة البيانات...');

    // إنشاء حساب المدير الأساسي
    const adminData = {
      name: 'علاء عبد الحميد',
      email: '<EMAIL>',
      phone: '0506747770',
      role: 'admin',
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const adminRef = await addDoc(collection(db, 'users'), adminData);
    console.log('👨‍💼 تم إنشاء حساب المدير:', adminRef.id);

    // إنشاء كورس تجريبي
    const courseData = {
      title: 'مقدمة في التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين',
      instructor: 'علاء عبد الحميد',
      duration: '4 ساعات',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 3,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      videos: [
        {
          id: 1,
          title: 'مقدمة في التسويق الرقمي',
          description: 'نظرة عامة على التسويق الرقمي وأهميته',
          duration: '15:30',
          videoUrl: '',
          order: 1
        },
        {
          id: 2,
          title: 'استراتيجيات التسويق',
          description: 'تعلم أهم استراتيجيات التسويق الرقمي',
          duration: '22:45',
          videoUrl: '',
          order: 2
        },
        {
          id: 3,
          title: 'قياس النتائج',
          description: 'كيفية قياس نجاح حملاتك التسويقية',
          duration: '18:20',
          videoUrl: '',
          order: 3
        }
      ]
    };

    const courseRef = await addDoc(collection(db, 'courses'), courseData);
    console.log('📚 تم إنشاء كورس تجريبي:', courseRef.id);

    // إنشاء أسئلة شائعة أساسية
    const faqs = [
      {
        question: 'كيف يمكنني التسجيل في الكورسات؟',
        answer: 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير',
        category: 'التسجيل',
        isActive: true,
        order: 1,
        createdAt: serverTimestamp()
      },
      {
        question: 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟',
        answer: 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات',
        category: 'المشاهدة',
        isActive: true,
        order: 2,
        createdAt: serverTimestamp()
      },
      {
        question: 'كيف أحصل على شهادة إتمام الكورس؟',
        answer: 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس',
        category: 'الشهادات',
        isActive: true,
        order: 3,
        createdAt: serverTimestamp()
      }
    ];

    for (const faq of faqs) {
      await addDoc(collection(db, 'faqs'), faq);
    }
    console.log('❓ تم إنشاء الأسئلة الشائعة');

    console.log('✅ تم إعادة تهيئة قاعدة البيانات بنجاح');
    
    return {
      adminId: adminRef.id,
      courseId: courseRef.id,
      message: 'تم إعادة تهيئة قاعدة البيانات بنجاح'
    };
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تهيئة قاعدة البيانات:', error);
    throw error;
  }
};

/**
 * إعادة تعيين كاملة لقاعدة البيانات
 */
export const resetDatabase = async () => {
  try {
    console.log('🔄 بدء إعادة تعيين قاعدة البيانات...');
    
    // مسح جميع البيانات
    await clearAllData();
    
    // إعادة تهيئة البيانات الأساسية
    const result = await initializeCleanDatabase();
    
    console.log('🎉 تم إعادة تعيين قاعدة البيانات بنجاح!');
    console.log('📧 بيانات تسجيل دخول المدير:');
    console.log('البريد الإلكتروني: <EMAIL>');
    console.log('كلمة المرور: Admin123!');
    
    return result;
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين قاعدة البيانات:', error);
    throw error;
  }
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.clearAllData = clearAllData;
window.initializeCleanDatabase = initializeCleanDatabase;
window.resetDatabase = resetDatabase;

export default {
  clearAllData,
  initializeCleanDatabase,
  resetDatabase
};
