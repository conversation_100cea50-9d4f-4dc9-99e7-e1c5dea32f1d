# نظام إدارة محتوى الكورسات المتقدم - SKILLS WORLD ACADEMY

## 🎯 نظرة عامة

تم تطوير نظام شامل لإدارة محتوى الكورسات يدعم رفع الفيديوهات وملفات PDF مع التكامل الكامل بين Firebase و Supabase لضمان الموثوقية والأداء العالي.

## ✨ الميزات الرئيسية

### 🎥 إدارة الفيديوهات
- **رفع متعدد الأجهزة**: دعم رفع الفيديوهات من أي جهاز (موبايل، ويندوز، إلخ)
- **أنواع ملفات متعددة**: MP4, AVI, MOV, WMV, FLV, WebM
- **حد أقصى للحجم**: 500MB لكل فيديو
- **تتبع التقدم**: شريط تقدم مباشر أثناء الرفع
- **نسخ احتياطية تلقائية**: رفع تلقائي إلى Firebase و Supabase
- **معاينة فورية**: إمكانية مشاهدة الفيديوهات فور الرفع

### 📄 إدارة المستندات
- **رفع ملفات PDF**: دعم كامل لملفات PDF
- **حد أقصى للحجم**: 50MB لكل ملف
- **فئات متعددة**: مادة تعليمية، واجبات، مراجع
- **تحكم في الوصول**: إمكانية تحديد الملفات العامة والخاصة
- **تحميل مباشر**: إمكانية تحميل ومشاهدة الملفات

### 🔄 التزامن الفوري
- **قاعدة بيانات مختلطة**: تكامل Firebase + Supabase
- **تحديثات فورية**: ظهور المحتوى الجديد للطلاب فوراً
- **مزامنة تلقائية**: نسخ احتياطية تلقائية عبر المنصتين

### 🎨 تحسينات التصميم
- **خلفية احترافية**: دمج صورة empty-notepad-coffee-beverage.jpg
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **واجهة عربية**: دعم كامل للغة العربية واتجاه RTL

## 🏗️ البنية التقنية

### المكونات الجديدة

#### 1. خدمة رفع الملفات (`fileUploadService.js`)
```javascript
// رفع فيديو
await fileUploadService.uploadCourseVideo(file, courseId, onProgress);

// رفع PDF
await fileUploadService.uploadCoursePDF(file, courseId, onProgress);
```

#### 2. خدمة التخزين المتقدمة (`storageService.js`)
```javascript
// رفع مع نسخة احتياطية
await storageService.uploadWithBackup(file, 'videos', courseId, onProgress);
```

#### 3. مدير رفع الفيديوهات (`VideoUploadManager.js`)
- واجهة سحب وإفلات
- معاينة الملفات
- تحرير معلومات الفيديو
- تتبع التقدم

#### 4. مدير رفع المستندات (`PDFUploadManager.js`)
- رفع ملفات PDF
- تصنيف المستندات
- إعدادات الوصول

#### 5. عارض المحتوى (`CourseContentViewer.js`)
- عرض الفيديوهات والمستندات
- مشغل فيديو مدمج
- تتبع التقدم
- تحميل المستندات

### قاعدة البيانات

#### جداول جديدة:
1. **course_videos**: فيديوهات الكورسات
2. **course_documents**: مستندات الكورسات  
3. **student_video_progress**: تقدم الطلاب
4. **document_downloads**: تحميلات المستندات

#### ميزات قاعدة البيانات:
- **RLS (Row Level Security)**: أمان على مستوى الصفوف
- **Triggers**: تحديث تلقائي للإحصائيات
- **Indexes**: فهرسة محسنة للأداء
- **Functions**: دوال مساعدة للتقارير

## 🚀 كيفية الاستخدام

### للمدراء:

#### 1. رفع فيديو جديد:
1. اذهب إلى إدارة الكورسات
2. انقر على أيقونة رفع الفيديو (🎥)
3. اسحب الملفات أو انقر لاختيارها
4. أدخل معلومات الفيديو
5. انقر "رفع جميع الفيديوهات"

#### 2. رفع مستند PDF:
1. انقر على أيقونة رفع PDF (📄)
2. اختر ملفات PDF
3. حدد الفئة والإعدادات
4. انقر "رفع جميع الملفات"

### للطلاب:

#### 1. مشاهدة المحتوى:
1. ادخل إلى الكورس
2. تصفح تبويبي "الفيديوهات" و "المستندات"
3. انقر على الفيديو للمشاهدة
4. انقر على المستند للتحميل

## 🔧 التثبيت والإعداد

### 1. تثبيت المكتبات:
```bash
cd frontend
npm install react-dropzone
```

### 2. تحديث قاعدة البيانات:
```sql
-- تشغيل ملف التحديث
\i database/update-schema.sql
```

### 3. إعداد Supabase Storage:
- إنشاء buckets: course-videos, course-documents
- تكوين الصلاحيات والسياسات

### 4. إعداد Firebase Storage:
- تكوين قواعد الأمان
- إعداد CORS للمجالات المطلوبة

## 📊 الإحصائيات والتقارير

### معلومات متاحة:
- عدد الفيديوهات لكل كورس
- عدد المستندات لكل كورس
- تقدم الطلاب في المشاهدة
- إحصائيات التحميل

### دوال مفيدة:
```sql
-- الحصول على تقدم طالب في كورس
SELECT get_course_progress('course_id', 'student_id');

-- ملخص محتوى الكورسات
SELECT * FROM course_content_summary;
```

## 🔒 الأمان

### ميزات الأمان:
- **التحقق من نوع الملف**: فقط الأنواع المدعومة
- **حدود الحجم**: منع رفع ملفات كبيرة جداً
- **RLS**: تحكم في الوصول على مستوى قاعدة البيانات
- **التشفير**: جميع الاتصالات مشفرة
- **النسخ الاحتياطية**: حماية من فقدان البيانات

## 🎨 التخصيص

### تخصيص الألوان:
```css
/* ألوان الفيديو */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)

/* ألوان PDF */
background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)
```

### تخصيص الأحجام:
```javascript
// في fileUploadService.js
VIDEO: {
  maxSize: 500 * 1024 * 1024, // 500MB
},
PDF: {
  maxSize: 50 * 1024 * 1024, // 50MB
}
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. فشل رفع الملف:
- تحقق من حجم الملف
- تأكد من نوع الملف المدعوم
- تحقق من اتصال الإنترنت

#### 2. عدم ظهور المحتوى:
- تحقق من صلاحيات قاعدة البيانات
- تأكد من نشر المحتوى
- تحقق من تسجيل الطالب في الكورس

#### 3. مشاكل التزامن:
- تحقق من إعدادات Firebase
- تأكد من اتصال Supabase
- راجع سجلات الأخطاء

## 📞 الدعم

للحصول على المساعدة:
- راجع ملفات السجل في المتصفح
- تحقق من حالة الخدمات (Firebase/Supabase)
- تواصل مع فريق التطوير

## 🔄 التحديثات المستقبلية

### ميزات مخططة:
- دعم أنواع ملفات إضافية
- ضغط الفيديوهات تلقائياً
- ترجمات للفيديوهات
- تحليلات متقدمة للمشاهدة
- نظام تقييم المحتوى

---

**تم التطوير بواسطة فريق SKILLS WORLD ACADEMY**  
**التاريخ**: 2025-07-10  
**الإصدار**: 2.0.0
