import React, { createContext, useContext, useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import authServiceNew from '../firebase/authServiceNew';
import interactionTracker from '../services/interactionTracker';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      // التحقق من وجود مستخدم محفوظ محلياً
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        try {
          const userData = JSON.parse(savedUser);
          setUser(userData);
        } catch (parseError) {
          console.error('خطأ في تحليل بيانات المستخدم المحفوظة:', parseError);
          localStorage.removeItem('user');
        }
      }
      setLoading(false);
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error);
      setLoading(false);
    }
  };

  const loginAdmin = async (email, password) => {
    try {
      // استخدام النظام الجديد لتسجيل دخول المدير
      const result = await authServiceNew.loginAdmin(email, password);

      if (result.success) {
        setUser(result.user);
        localStorage.setItem('user', JSON.stringify(result.user));

        // تتبع تسجيل دخول المدير
        interactionTracker.setUserId(result.user.id);
        interactionTracker.trackInteraction('admin_login', {
          userId: result.user.id,
          email: result.user.email,
          role: result.user.role,
          loginMethod: 'email_password'
        });

        toast.success(`مرحباً ${result.user.name}!`);
        return { success: true };
      } else {
        toast.error(result.message || 'فشل في تسجيل الدخول');
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('خطأ في تسجيل دخول المدير:', error);
      toast.error('خطأ في تسجيل الدخول');
      return { success: false, message: error.message };
    }
  };

  const loginStudent = async (code) => {
    try {
      // استخدام النظام الجديد لتسجيل دخول الطالب
      const result = await authServiceNew.loginStudent(code);

      if (result.success) {
        setUser(result.user);
        localStorage.setItem('user', JSON.stringify(result.user));

        // تتبع تسجيل دخول الطالب
        interactionTracker.setUserId(result.user.id);
        interactionTracker.trackInteraction('student_login', {
          userId: result.user.id,
          studentCode: result.user.studentCode,
          role: result.user.role,
          loginMethod: 'student_code'
        });

        toast.success(`مرحباً ${result.user.name}!`);
        return { success: true };
      } else {
        toast.error(result.message || 'كود التسجيل غير صحيح');
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('خطأ في تسجيل دخول الطالب:', error);
      toast.error('خطأ في تسجيل الدخول');
      return { success: false, message: error.message };
    }
  };

  const logout = async () => {
    try {
      if (user) {
        await authServiceNew.logout(user.id);
      }
    } catch (error) {
      console.log('خطأ في تسجيل الخروج:', error);
    }

    // تتبع تسجيل الخروج
    if (user) {
      interactionTracker.trackInteraction('user_logout', {
        userId: user.id,
        role: user.role,
        sessionDuration: Date.now() - interactionTracker.startTime
      });
    }

    // Clear local storage and state
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    toast.success('تم تسجيل الخروج بنجاح');
  };

  const updateUser = async (userData) => {
    try {
      if (user) {
        if (user.role === 'admin') {
          await authServiceNew.updateAdminProfile(user.id, userData);
        } else {
          await authServiceNew.updateStudentProfile(user.id, userData);
        }
      }

      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
      return updatedUser;
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    loginAdmin,
    loginStudent,
    logout,
    updateUser,
    checkAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
