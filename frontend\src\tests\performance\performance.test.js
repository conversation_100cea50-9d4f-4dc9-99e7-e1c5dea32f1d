import { renderHook, act } from '@testing-library/react';
import {
  usePerformanceMonitor,
  useComponentPerformance,
  useMemoryMonitor
} from '../../hooks/usePerformanceMonitor';

// Mock performance API
const mockPerformance = {
  now: jest.fn(),
  memory: {
    usedJSHeapSize: 10000000, // 10MB
    totalJSHeapSize: 20000000, // 20MB
    jsHeapSizeLimit: 100000000 // 100MB
  }
};

Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
});

describe('usePerformanceMonitor Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPerformance.now.mockReturnValue(Date.now());
  });

  test('should initialize with default metrics', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    expect(result.current.metrics).toEqual({
      updateCount: 0,
      averageUpdateTime: 0,
      lastUpdateTime: null,
      connectionQuality: 'good',
      dataTransferRate: 0,
      errorCount: 0,
      totalDataReceived: 0
    });
  });

  test('should record updates correctly', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    act(() => {
      result.current.recordUpdate(1000, 500); // 1KB data, 500ms duration
    });

    expect(result.current.metrics.updateCount).toBe(1);
    expect(result.current.metrics.averageUpdateTime).toBe(500);
    expect(result.current.metrics.totalDataReceived).toBe(1000);
    expect(result.current.metrics.lastUpdateTime).toBeDefined();
  });

  test('should calculate average update time correctly', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    act(() => {
      result.current.recordUpdate(1000, 400);
      result.current.recordUpdate(1000, 600);
      result.current.recordUpdate(1000, 500);
    });

    expect(result.current.metrics.updateCount).toBe(3);
    expect(result.current.metrics.averageUpdateTime).toBe(500); // (400+600+500)/3
  });

  test('should update connection quality based on update duration', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    // اختبار جودة جيدة
    act(() => {
      result.current.recordUpdate(1000, 1000); // 1 second
    });
    expect(result.current.metrics.connectionQuality).toBe('good');

    // اختبار جودة متوسطة
    act(() => {
      result.current.recordUpdate(1000, 3000); // 3 seconds
    });
    expect(result.current.metrics.connectionQuality).toBe('fair');

    // اختبار جودة ضعيفة
    act(() => {
      result.current.recordUpdate(1000, 6000); // 6 seconds
    });
    expect(result.current.metrics.connectionQuality).toBe('poor');
  });

  test('should record errors correctly', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    act(() => {
      result.current.recordError();
      result.current.recordError();
    });

    expect(result.current.metrics.errorCount).toBe(2);
  });

  test('should calculate data transfer rate', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    // محاكاة مرور الوقت
    const startTime = Date.now();
    mockPerformance.now.mockReturnValue(startTime);
    
    act(() => {
      result.current.recordUpdate(5000, 1000); // 5KB
    });

    // محاكاة مرور ثانية واحدة
    mockPerformance.now.mockReturnValue(startTime + 1000);
    
    act(() => {
      result.current.recordUpdate(5000, 1000); // 5KB أخرى
    });

    expect(result.current.metrics.dataTransferRate).toBeGreaterThan(0);
  });

  test('should reset metrics correctly', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    // إضافة بعض البيانات
    act(() => {
      result.current.recordUpdate(1000, 500);
      result.current.recordError();
    });

    // إعادة تعيين
    act(() => {
      result.current.resetMetrics();
    });

    expect(result.current.metrics).toEqual({
      updateCount: 0,
      averageUpdateTime: 0,
      lastUpdateTime: null,
      connectionQuality: 'good',
      dataTransferRate: 0,
      errorCount: 0,
      totalDataReceived: 0
    });
  });

  test('should calculate advanced metrics correctly', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    // إضافة بعض البيانات
    act(() => {
      result.current.recordUpdate(1000, 500);
      result.current.recordUpdate(1000, 600);
      result.current.recordError();
    });

    const advancedMetrics = result.current.getAdvancedMetrics();
    
    expect(advancedMetrics.uptime).toBeGreaterThanOrEqual(0);
    expect(advancedMetrics.updatesPerMinute).toBeGreaterThan(0);
    expect(advancedMetrics.errorRate).toBe(50); // 1 error out of 2 updates = 50%
    expect(advancedMetrics.dataTransferRateKB).toBeGreaterThanOrEqual(0);
  });
});

describe('useComponentPerformance Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPerformance.now.mockReturnValue(1000);
  });

  test('should track render count', () => {
    const { result, rerender } = renderHook(() => 
      useComponentPerformance('TestComponent')
    );

    expect(result.current.renderCount).toBe(1);

    rerender();
    expect(result.current.renderCount).toBe(2);

    rerender();
    expect(result.current.renderCount).toBe(3);
  });

  test('should calculate render times', () => {
    let currentTime = 1000;
    mockPerformance.now.mockImplementation(() => {
      currentTime += 10; // محاكاة مرور 10ms لكل استدعاء
      return currentTime;
    });

    const { result, rerender } = renderHook(() => 
      useComponentPerformance('TestComponent')
    );

    rerender();
    
    expect(result.current.averageRenderTime).toBeGreaterThan(0);
    expect(result.current.lastRenderTime).toBeGreaterThan(0);
  });

  test('should maintain render time history', () => {
    let currentTime = 1000;
    mockPerformance.now.mockImplementation(() => {
      currentTime += Math.random() * 20; // أوقات رندر متغيرة
      return currentTime;
    });

    const { result, rerender } = renderHook(() => 
      useComponentPerformance('TestComponent')
    );

    // إجراء عدة عمليات رندر
    for (let i = 0; i < 5; i++) {
      rerender();
    }

    expect(result.current.renderCount).toBe(6); // 1 initial + 5 rerenders
    expect(result.current.averageRenderTime).toBeGreaterThan(0);
  });
});

describe('useMemoryMonitor Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should return memory information when available', () => {
    const { result } = renderHook(() => useMemoryMonitor());
    
    expect(result.current.usedMemoryMB).toBe(9.54); // 10MB / 1024 / 1024
    expect(result.current.totalMemoryMB).toBe(19.07); // 20MB / 1024 / 1024
    expect(result.current.memoryLimitMB).toBe(95.37); // 100MB / 1024 / 1024
    expect(result.current.memoryUsagePercentage).toBe(50); // 10MB / 20MB * 100
  });

  test('should handle missing performance.memory gracefully', () => {
    // إزالة performance.memory مؤقتاً
    const originalMemory = mockPerformance.memory;
    delete mockPerformance.memory;

    const { result } = renderHook(() => useMemoryMonitor());
    
    expect(result.current.usedMemoryMB).toBe(0);
    expect(result.current.totalMemoryMB).toBe(0);
    expect(result.current.memoryLimitMB).toBe(0);
    expect(result.current.memoryUsagePercentage).toBe(0);

    // استعادة performance.memory
    mockPerformance.memory = originalMemory;
  });

  test('should update memory info periodically', (done) => {
    const { result } = renderHook(() => useMemoryMonitor());
    
    const initialUsedMemory = result.current.usedMemoryMB;
    
    // تغيير قيم الذاكرة
    mockPerformance.memory.usedJSHeapSize = 15000000; // 15MB
    
    // انتظار التحديث التلقائي (5 ثوان)
    setTimeout(() => {
      expect(result.current.usedMemoryMB).not.toBe(initialUsedMemory);
      done();
    }, 100); // استخدام وقت قصير للاختبار
  });
});

describe('Performance Integration Tests', () => {
  test('should work together for comprehensive monitoring', () => {
    const { result: perfResult } = renderHook(() => usePerformanceMonitor());
    const { result: compResult } = renderHook(() => 
      useComponentPerformance('IntegrationTest')
    );
    const { result: memResult } = renderHook(() => useMemoryMonitor());

    // اختبار التكامل
    act(() => {
      perfResult.current.recordUpdate(2000, 800);
    });

    expect(perfResult.current.metrics.updateCount).toBe(1);
    expect(compResult.current.renderCount).toBeGreaterThan(0);
    expect(memResult.current.usedMemoryMB).toBeGreaterThan(0);
  });

  test('should handle high-frequency updates efficiently', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    // محاكاة تحديثات عالية التردد
    act(() => {
      for (let i = 0; i < 100; i++) {
        result.current.recordUpdate(100, Math.random() * 1000);
      }
    });

    expect(result.current.metrics.updateCount).toBe(100);
    expect(result.current.metrics.averageUpdateTime).toBeGreaterThan(0);
    expect(result.current.metrics.totalDataReceived).toBe(10000); // 100 * 100 bytes
  });

  test('should maintain performance under stress', () => {
    const { result } = renderHook(() => usePerformanceMonitor());
    
    const startTime = performance.now();
    
    // اختبار الضغط
    act(() => {
      for (let i = 0; i < 1000; i++) {
        result.current.recordUpdate(1000, 500);
        if (i % 10 === 0) {
          result.current.recordError();
        }
      }
    });

    const endTime = performance.now();
    const executionTime = endTime - startTime;

    // التأكد من أن الأداء مقبول (أقل من ثانية واحدة)
    expect(executionTime).toBeLessThan(1000);
    expect(result.current.metrics.updateCount).toBe(1000);
    expect(result.current.metrics.errorCount).toBe(100);
  });
});
