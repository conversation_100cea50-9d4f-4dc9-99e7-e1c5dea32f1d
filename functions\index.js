const functions = require('firebase-functions');
const admin = require('firebase-admin');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();

// Import routes
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');
const studentRoutes = require('./routes/student');

// Import SQL APIs
const {
  testSQL,
  executeSQL,
  studentsAPI,
  authAPI,
  coursesAPI,
  statsAPI
} = require('./sql-api');

// Import Password Reset Functions
const {
  forgotPassword,
  testSMS,
  getSecurityStats,
  cleanupSecurityLogs
} = require('./forgotPassword');

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'"]
    }
  }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
  }
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: true,
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Make Firestore available to routes
app.use((req, res, next) => {
  req.db = db;
  req.admin = admin;
  next();
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/student', studentRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'الخادم يعمل بنجاح',
    timestamp: new Date().toISOString(),
    environment: 'firebase'
  });
});

// Initialize database with default data
const initializeDatabase = async () => {
  try {
    // Check if admin user exists
    const adminQuery = await db.collection('users').where('role', '==', 'admin').limit(1).get();

    if (adminQuery.empty) {
      console.log('🔧 تهيئة قاعدة البيانات بالبيانات الافتراضية...');

      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('Admin123!', 12);

      // Create admin user
      const adminRef = db.collection('users').doc();
      await adminRef.set({
        name: 'علاء عبد الحميد',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        isActive: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log('👨‍💼 تم إنشاء حساب المدير الافتراضي');

      // Create default categories
      const categories = [
        {
          name: 'التسويق الرقمي',
          description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
          icon: 'digital-marketing',
          color: '#2196F3',
          order: 1,
          isActive: true,
          createdBy: adminRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        },
        {
          name: 'وسائل التواصل الاجتماعي',
          description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
          icon: 'social-media',
          color: '#4CAF50',
          order: 2,
          isActive: true,
          createdBy: adminRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        },
        {
          name: 'التسويق بالمحتوى',
          description: 'إنشاء وتسويق المحتوى الفعال',
          icon: 'content-marketing',
          color: '#FF9800',
          order: 3,
          isActive: true,
          createdBy: adminRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        }
      ];

      const batch = db.batch();
      categories.forEach(category => {
        const categoryRef = db.collection('categories').doc();
        batch.set(categoryRef, category);
      });
      await batch.commit();

      console.log('📂 تم إنشاء الأقسام الافتراضية');

      // Create sample students
      const sampleStudents = [
        {
          name: 'أحمد محمد علي',
          studentCode: '123456',
          role: 'student',
          isActive: true,
          createdBy: adminRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        },
        {
          name: 'فاطمة علي حسن',
          studentCode: '789012',
          role: 'student',
          isActive: true,
          createdBy: adminRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        }
      ];

      const studentBatch = db.batch();
      sampleStudents.forEach(student => {
        const studentRef = db.collection('users').doc();
        studentBatch.set(studentRef, student);
      });
      await studentBatch.commit();

      console.log('👥 تم إنشاء الطلاب التجريبيين');
      console.log('✅ تم تهيئة قاعدة البيانات بنجاح!');
    } else {
      console.log('ℹ️ قاعدة البيانات مهيأة بالفعل');
    }
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error.message);
  }
};

// Initialize database on first function call
let isInitialized = false;
app.use(async (req, res, next) => {
  if (!isInitialized) {
    await initializeDatabase();
    isInitialized = true;
  }
  next();
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err.stack);
  
  res.status(err.status || 500).json({
    error: 'حدث خطأ في الخادم',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    path: req.originalUrl
  });
});

// Export the Express app as a Firebase Function
exports.api = functions.region('us-central1').https.onRequest(app);

// Export SQL APIs
exports.testSQL = testSQL;
exports.executeSQL = executeSQL;
exports.studentsAPI = studentsAPI;
exports.authAPI = authAPI;
exports.coursesAPI = coursesAPI;
exports.statsAPI = statsAPI;

// Export Password Reset Functions
exports.forgotPassword = forgotPassword;
exports.testSMS = testSMS;
exports.getSecurityStats = getSecurityStats;
exports.cleanupSecurityLogs = cleanupSecurityLogs;
