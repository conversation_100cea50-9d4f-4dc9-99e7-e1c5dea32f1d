import authServiceNew from '../firebase/authServiceNew';
import { initializeApp } from '../firebase/initializeApp';

// Mock Firebase
jest.mock('../firebase/config', () => ({
  db: {},
  auth: {}
}));

describe('Auth Service Tests', () => {
  beforeAll(async () => {
    // تهيئة التطبيق قبل الاختبارات
    await initializeApp();
  });

  describe('Admin Authentication', () => {
    test('should login admin with correct credentials', async () => {
      const result = await authServiceNew.loginAdmin('<EMAIL>', 'Admin123!');
      
      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.user.role).toBe('admin');
      expect(result.user.email).toBe('<EMAIL>');
    });

    test('should reject admin login with incorrect credentials', async () => {
      const result = await authServiceNew.loginAdmin('<EMAIL>', 'wrongpassword');
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });

    test('should reject admin login with invalid email', async () => {
      const result = await authServiceNew.loginAdmin('<EMAIL>', 'Admin123!');
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });
  });

  describe('Student Authentication', () => {
    test('should login student with valid code', async () => {
      // إنشاء طالب تجريبي أولاً
      const studentData = {
        name: 'طالب تجريبي',
        email: '<EMAIL>',
        phone: '**********'
      };
      
      const createdStudent = await authServiceNew.createStudent(studentData, 'admin');
      expect(createdStudent).toBeDefined();
      expect(createdStudent.studentCode).toBeDefined();

      // اختبار تسجيل الدخول
      const loginResult = await authServiceNew.loginStudent(createdStudent.studentCode);
      
      expect(loginResult.success).toBe(true);
      expect(loginResult.user).toBeDefined();
      expect(loginResult.user.role).toBe('student');
      expect(loginResult.user.studentCode).toBe(createdStudent.studentCode);
    });

    test('should reject student login with invalid code', async () => {
      const result = await authServiceNew.loginStudent('INVALID123');
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });

    test('should reject student login with inactive account', async () => {
      // إنشاء طالب وإلغاء تفعيله
      const studentData = {
        name: 'طالب غير مفعل',
        email: '<EMAIL>',
        phone: '**********'
      };
      
      const createdStudent = await authServiceNew.createStudent(studentData, 'admin');
      await authServiceNew.toggleStudentStatus(createdStudent.id, false, 'admin');

      const loginResult = await authServiceNew.loginStudent(createdStudent.studentCode);
      
      expect(loginResult.success).toBe(false);
      expect(loginResult.message).toContain('غير مفعل');
    });
  });

  describe('Profile Management', () => {
    test('should update admin profile', async () => {
      const adminLogin = await authServiceNew.loginAdmin('<EMAIL>', 'Admin123!');
      expect(adminLogin.success).toBe(true);

      const updateData = {
        name: 'اسم محدث',
        email: '<EMAIL>',
        phone: '**********'
      };

      const updateResult = await authServiceNew.updateAdminProfile(adminLogin.user.id, updateData);
      expect(updateResult.success).toBe(true);
    });

    test('should update student profile', async () => {
      // إنشاء طالب
      const studentData = {
        name: 'طالب للتحديث',
        email: '<EMAIL>',
        phone: '**********'
      };
      
      const createdStudent = await authServiceNew.createStudent(studentData, 'admin');
      
      const updateData = {
        name: 'اسم محدث للطالب',
        email: '<EMAIL>',
        phone: '**********'
      };

      const updateResult = await authServiceNew.updateStudentProfile(createdStudent.id, updateData);
      expect(updateResult.success).toBe(true);
    });
  });

  describe('Student Management', () => {
    test('should create new student', async () => {
      const studentData = {
        name: 'طالب جديد',
        email: '<EMAIL>',
        phone: '5555555555'
      };

      const result = await authServiceNew.createStudent(studentData, 'admin');
      
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.studentCode).toBeDefined();
      expect(result.name).toBe(studentData.name);
      expect(result.email).toBe(studentData.email);
      expect(result.isActive).toBe(true);
    });

    test('should toggle student status', async () => {
      // إنشاء طالب
      const studentData = {
        name: 'طالب للتفعيل',
        email: '<EMAIL>',
        phone: '1111111111'
      };
      
      const createdStudent = await authServiceNew.createStudent(studentData, 'admin');
      expect(createdStudent.isActive).toBe(true);

      // إلغاء التفعيل
      const deactivateResult = await authServiceNew.toggleStudentStatus(createdStudent.id, false, 'admin');
      expect(deactivateResult.success).toBe(true);

      // إعادة التفعيل
      const activateResult = await authServiceNew.toggleStudentStatus(createdStudent.id, true, 'admin');
      expect(activateResult.success).toBe(true);
    });

    test('should delete student', async () => {
      // إنشاء طالب
      const studentData = {
        name: 'طالب للحذف',
        email: '<EMAIL>',
        phone: '2222222222'
      };
      
      const createdStudent = await authServiceNew.createStudent(studentData, 'admin');
      
      // حذف الطالب
      const deleteResult = await authServiceNew.deleteStudent(createdStudent.id, 'admin');
      expect(deleteResult.success).toBe(true);

      // التأكد من عدم إمكانية تسجيل الدخول
      const loginResult = await authServiceNew.loginStudent(createdStudent.studentCode);
      expect(loginResult.success).toBe(false);
    });
  });

  describe('Activity Logging', () => {
    test('should log user activity', async () => {
      const adminLogin = await authServiceNew.loginAdmin('<EMAIL>', 'Admin123!');
      expect(adminLogin.success).toBe(true);

      const activityData = {
        action: 'test_action',
        description: 'اختبار تسجيل النشاط',
        metadata: { test: true }
      };

      const logResult = await authServiceNew.logActivity(adminLogin.user.id, activityData);
      expect(logResult.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid user ID gracefully', async () => {
      const result = await authServiceNew.updateAdminProfile('invalid-id', { name: 'test' });
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });

    test('should handle network errors gracefully', async () => {
      // محاكاة خطأ في الشبكة
      const originalFetch = global.fetch;
      global.fetch = jest.fn(() => Promise.reject(new Error('Network error')));

      const result = await authServiceNew.loginAdmin('<EMAIL>', 'Admin123!');
      
      // استعادة fetch الأصلي
      global.fetch = originalFetch;
      
      // يجب أن يتعامل مع الخطأ بشكل صحيح
      expect(result.success).toBe(false);
    });
  });
});
