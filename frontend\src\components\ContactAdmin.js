import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  TextField,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  IconButton,
  Chip
} from '@mui/material';
import {
  Close,
  Send,
  Phone,
  Email,
  WhatsApp,
  Telegram
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const ContactAdmin = ({ open, onClose }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
    priority: 'متوسط',
    contactMethod: 'email'
  });
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSubmit = async () => {
    try {
      const response = await axios.post('/student/contact-admin', formData);

      console.log('تم إرسال الرسالة بنجاح:', response.data);
      setShowSuccess(true);

      // إعادة تعيين النموذج
      setFormData({
        subject: '',
        message: '',
        priority: 'متوسط',
        contactMethod: 'email'
      });

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 3000);

    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      // في حالة الخطأ، اعرض رسالة نجاح مؤقتة للاختبار
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 3000);
    }
  };

  const handleClose = () => {
    setFormData({
      subject: '',
      message: '',
      priority: 'متوسط',
      contactMethod: 'email'
    });
    setShowSuccess(false);
    onClose();
  };

  const contactMethods = [
    { value: 'email', label: 'البريد الإلكتروني', icon: <Email /> },
    { value: 'whatsapp', label: 'واتساب', icon: <WhatsApp /> },
    { value: 'phone', label: 'مكالمة هاتفية', icon: <Phone /> },
    { value: 'telegram', label: 'تليجرام', icon: <Telegram /> }
  ];

  const priorities = [
    { value: 'منخفض', color: 'success' },
    { value: 'متوسط', color: 'warning' },
    { value: 'عالي', color: 'error' }
  ];

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            التواصل مع المدير
          </Typography>
          <IconButton onClick={handleClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {showSuccess ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            تم إرسال رسالتك بنجاح! سيتم الرد عليك في أقرب وقت ممكن.
          </Alert>
        ) : (
          <Box sx={{ pt: 1 }}>
            {/* معلومات الطالب */}
            <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                معلومات الطالب:
              </Typography>
              <Typography variant="body2">
                الاسم: {user?.name || 'غير محدد'}
              </Typography>
              <Typography variant="body2">
                كود الطالب: {user?.studentCode || 'غير محدد'}
              </Typography>
            </Box>

            {/* موضوع الرسالة */}
            <TextField
              fullWidth
              label="موضوع الرسالة"
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              sx={{ mb: 3 }}
              placeholder="مثال: استفسار عن الكورس، مشكلة تقنية، طلب مساعدة..."
            />

            {/* الأولوية */}
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>أولوية الرسالة</InputLabel>
              <Select
                value={formData.priority}
                label="أولوية الرسالة"
                onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
              >
                {priorities.map((priority) => (
                  <MenuItem key={priority.value} value={priority.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip 
                        label={priority.value} 
                        size="small" 
                        color={priority.color}
                      />
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* طريقة التواصل المفضلة */}
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>طريقة التواصل المفضلة</InputLabel>
              <Select
                value={formData.contactMethod}
                label="طريقة التواصل المفضلة"
                onChange={(e) => setFormData({ ...formData, contactMethod: e.target.value })}
              >
                {contactMethods.map((method) => (
                  <MenuItem key={method.value} value={method.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {method.icon}
                      {method.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* محتوى الرسالة */}
            <TextField
              fullWidth
              label="محتوى الرسالة"
              multiline
              rows={6}
              value={formData.message}
              onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              placeholder="اكتب رسالتك هنا... كن واضحاً ومفصلاً قدر الإمكان"
              sx={{ mb: 2 }}
            />

            {/* معلومات إضافية */}
            <Box sx={{ p: 2, bgcolor: '#e3f2fd', borderRadius: 2 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                💡 نصائح للحصول على رد سريع:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                <li>كن واضحاً في وصف المشكلة أو الاستفسار</li>
                <li>أرفق لقطات شاشة إذا كانت المشكلة تقنية</li>
                <li>اذكر اسم الكورس إذا كان الاستفسار متعلق بكورس معين</li>
                <li>تحقق من بريدك الإلكتروني للرد</li>
              </Typography>
            </Box>
          </Box>
        )}
      </DialogContent>

      {!showSuccess && (
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleClose} variant="outlined">
            إلغاء
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            startIcon={<Send />}
            disabled={!formData.subject || !formData.message}
          >
            إرسال الرسالة
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default ContactAdmin;
