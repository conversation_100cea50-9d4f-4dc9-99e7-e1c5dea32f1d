/**
 * مدير التسجيلات الذكي المتطور - Skills World Academy
 * Smart Enrollment Manager with Advanced Features
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Grid,
  Alert,
  CircularProgress,
  Autocomplete,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar
} from '@mui/material';
import {
  Add as AddIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Analytics as AnalyticsIcon,
  Notifications as NotificationsIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import { smartCourseService } from '../../services/smartCourseService';
import { hybridStudents } from '../../services/hybridDatabaseService';
import { realTimeSyncService } from '../../services/realTimeSyncService';

/**
 * مكون إدارة التسجيلات الذكي
 */
const SmartEnrollmentManager = () => {
  // الحالات الأساسية
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [enrollments, setEnrollments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  // حالات النموذج المتطور
  const [formData, setFormData] = useState({
    studentId: '',
    courseId: '',
    priority: 'normal',
    source: 'admin',
    notes: '',
    sendNotification: true,
    autoStart: false,
    customDeadline: null
  });

  // حالات التحليلات
  const [analytics, setAnalytics] = useState({
    totalEnrollments: 0,
    activeEnrollments: 0,
    completionRate: 0,
    averageProgress: 0
  });

  // حالات البحث والفلترة
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);

  /**
   * تحميل البيانات الأولية
   */
  useEffect(() => {
    loadInitialData();
    setupRealtimeListeners();
  }, []);

  /**
   * تحميل البيانات الأولية
   */
  const loadInitialData = async () => {
    setLoading(true);
    try {
      console.log('📊 تحميل البيانات الأولية...');
      
      // تحميل البيانات بشكل متوازي
      const [studentsData, coursesData, enrollmentsData] = await Promise.all([
        hybridStudents.getAllStudents(),
        realTimeSyncService.getAllCourses(),
        realTimeSyncService.getAllEnrollments()
      ]);

      setStudents(studentsData || []);
      setCourses(coursesData || []);
      setEnrollments(enrollmentsData || []);

      // حساب التحليلات
      calculateAnalytics(enrollmentsData || []);

      console.log('✅ تم تحميل البيانات بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تحميل البيانات:', error);
      toast.error('فشل في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  /**
   * إعداد المراقبة الفورية
   */
  const setupRealtimeListeners = () => {
    console.log('🔄 إعداد المراقبة الفورية للتسجيلات...');

    // مراقبة التسجيلات
    const unsubscribeEnrollments = realTimeSyncService.watchEnrollments((updatedEnrollments) => {
      setEnrollments(updatedEnrollments);
      calculateAnalytics(updatedEnrollments);
      toast.success('تم تحديث التسجيلات فورياً! 🔄', { duration: 2000 });
    });

    // مراقبة الكورسات
    const unsubscribeCourses = realTimeSyncService.watchCourses((updatedCourses) => {
      setCourses(updatedCourses);
    });

    // مراقبة الطلاب
    const unsubscribeStudents = hybridStudents.watchStudents((updatedStudents) => {
      setStudents(updatedStudents);
    });

    // تنظيف المراقبة عند إلغاء المكون
    return () => {
      unsubscribeEnrollments();
      unsubscribeCourses();
      unsubscribeStudents();
    };
  };

  /**
   * حساب التحليلات
   */
  const calculateAnalytics = (enrollmentsData) => {
    const total = enrollmentsData.length;
    const active = enrollmentsData.filter(e => e.status === 'active' && !e.isDeleted).length;
    const completed = enrollmentsData.filter(e => e.isCompleted).length;
    const totalProgress = enrollmentsData.reduce((sum, e) => sum + (e.progress || 0), 0);

    setAnalytics({
      totalEnrollments: total,
      activeEnrollments: active,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
      averageProgress: total > 0 ? Math.round(totalProgress / total) : 0
    });
  };

  /**
   * تسجيل طالب في كورس بالنظام الذكي
   */
  const handleSmartEnrollment = async () => {
    if (!formData.studentId || !formData.courseId) {
      toast.error('يرجى اختيار الطالب والكورس');
      return;
    }

    setLoading(true);
    try {
      console.log('🚀 بدء التسجيل الذكي...');

      // استخدام النظام الذكي الجديد
      const result = await smartCourseService.enrollStudentInCourse(
        formData.studentId,
        formData.courseId,
        {
          priority: formData.priority,
          source: formData.source,
          notes: formData.notes,
          sendNotification: formData.sendNotification,
          autoStart: formData.autoStart,
          customDeadline: formData.customDeadline,
          adminId: 'current-admin-id' // يمكن الحصول عليه من السياق
        }
      );

      if (result.success) {
        toast.success(
          <div>
            <strong>🎉 تم التسجيل بنجاح!</strong>
            <br />
            {result.message}
          </div>,
          { duration: 5000 }
        );

        // إعادة تعيين النموذج
        setFormData({
          studentId: '',
          courseId: '',
          priority: 'normal',
          source: 'admin',
          notes: '',
          sendNotification: true,
          autoStart: false,
          customDeadline: null
        });

        setSelectedStudent(null);
        setSelectedCourse(null);
        setDialogOpen(false);

        // تحديث البيانات
        loadInitialData();
      }
    } catch (error) {
      console.error('❌ خطأ في التسجيل الذكي:', error);
      toast.error(
        <div>
          <strong>❌ فشل في التسجيل</strong>
          <br />
          {error.message}
        </div>,
        { duration: 5000 }
      );
    } finally {
      setLoading(false);
    }
  };

  /**
   * فتح نافذة التسجيل
   */
  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  /**
   * إغلاق نافذة التسجيل
   */
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setFormData({
      studentId: '',
      courseId: '',
      priority: 'normal',
      source: 'admin',
      notes: '',
      sendNotification: true,
      autoStart: false,
      customDeadline: null
    });
    setSelectedStudent(null);
    setSelectedCourse(null);
  };

  /**
   * فلترة التسجيلات
   */
  const filteredEnrollments = enrollments.filter(enrollment => {
    const matchesSearch = searchTerm === '' || 
      enrollment.studentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      enrollment.courseTitle?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || enrollment.status === filterStatus;
    
    return matchesSearch && matchesStatus && !enrollment.isDeleted;
  });

  return (
    <Box sx={{ p: 3 }}>
      {/* رأس الصفحة مع التحليلات */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ mb: 2, fontWeight: 'bold' }}>
          🎓 إدارة التسجيلات الذكية
        </Typography>
        
        {/* بطاقات التحليلات */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'primary.main', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SchoolIcon sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h6">{analytics.totalEnrollments}</Typography>
                    <Typography variant="body2">إجمالي التسجيلات</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'success.main', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircleIcon sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h6">{analytics.activeEnrollments}</Typography>
                    <Typography variant="body2">التسجيلات النشطة</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'info.main', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AnalyticsIcon sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h6">{analytics.completionRate}%</Typography>
                    <Typography variant="body2">معدل الإنجاز</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'warning.main', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SpeedIcon sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h6">{analytics.averageProgress}%</Typography>
                    <Typography variant="body2">متوسط التقدم</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* أزرار التحكم */}
        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenDialog}
            sx={{ bgcolor: 'primary.main' }}
          >
            تسجيل ذكي جديد
          </Button>
          
          <TextField
            placeholder="البحث في التسجيلات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            sx={{ minWidth: 250 }}
          />
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>الحالة</InputLabel>
            <Select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              label="الحالة"
            >
              <MenuItem value="all">جميع الحالات</MenuItem>
              <MenuItem value="active">نشط</MenuItem>
              <MenuItem value="completed">مكتمل</MenuItem>
              <MenuItem value="paused">متوقف</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* قائمة التسجيلات */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            📋 قائمة التسجيلات ({filteredEnrollments.length})
          </Typography>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : filteredEnrollments.length === 0 ? (
            <Alert severity="info">
              لا توجد تسجيلات تطابق معايير البحث
            </Alert>
          ) : (
            <List>
              {filteredEnrollments.map((enrollment, index) => (
                <ListItem key={enrollment.id || index} divider>
                  <ListItemIcon>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <PersonIcon />
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {enrollment.studentName || 'طالب غير محدد'}
                        </Typography>
                        <Chip
                          label={enrollment.status || 'غير محدد'}
                          color={enrollment.status === 'active' ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          📚 الكورس: {enrollment.courseTitle || 'كورس غير محدد'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          📊 التقدم: {enrollment.progress || 0}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          📅 تاريخ التسجيل: {enrollment.enrolledAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* نافذة التسجيل الذكي */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AddIcon sx={{ mr: 1, color: 'primary.main' }} />
            🚀 تسجيل ذكي جديد
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* اختيار الطالب */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={students}
                getOptionLabel={(option) => `${option.name} (${option.studentCode})`}
                value={selectedStudent}
                onChange={(event, newValue) => {
                  setSelectedStudent(newValue);
                  setFormData(prev => ({ ...prev, studentId: newValue?.id || '' }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="اختر الطالب"
                    required
                    fullWidth
                  />
                )}
              />
            </Grid>

            {/* اختيار الكورس */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={courses}
                getOptionLabel={(option) => option.title}
                value={selectedCourse}
                onChange={(event, newValue) => {
                  setSelectedCourse(newValue);
                  setFormData(prev => ({ ...prev, courseId: newValue?.id || '' }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="اختر الكورس"
                    required
                    fullWidth
                  />
                )}
              />
            </Grid>

            {/* الأولوية */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الأولوية</InputLabel>
                <Select
                  value={formData.priority}
                  onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                  label="الأولوية"
                >
                  <MenuItem value="low">منخفضة</MenuItem>
                  <MenuItem value="normal">عادية</MenuItem>
                  <MenuItem value="high">عالية</MenuItem>
                  <MenuItem value="urgent">عاجلة</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* مصدر التسجيل */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>مصدر التسجيل</InputLabel>
                <Select
                  value={formData.source}
                  onChange={(e) => setFormData(prev => ({ ...prev, source: e.target.value }))}
                  label="مصدر التسجيل"
                >
                  <MenuItem value="admin">إدارة</MenuItem>
                  <MenuItem value="self">تسجيل ذاتي</MenuItem>
                  <MenuItem value="bulk">تسجيل جماعي</MenuItem>
                  <MenuItem value="api">API</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* ملاحظات */}
            <Grid item xs={12}>
              <TextField
                label="ملاحظات (اختياري)"
                multiline
                rows={3}
                fullWidth
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="أضف أي ملاحظات خاصة بهذا التسجيل..."
              />
            </Grid>

            <Divider sx={{ width: '100%', my: 2 }} />

            {/* خيارات متقدمة */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                ⚙️ خيارات متقدمة
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.sendNotification}
                    onChange={(e) => setFormData(prev => ({ ...prev, sendNotification: e.target.checked }))}
                  />
                }
                label="إرسال إشعار للطالب"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.autoStart}
                    onChange={(e) => setFormData(prev => ({ ...prev, autoStart: e.target.checked }))}
                  />
                }
                label="بدء تلقائي للكورس"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            إلغاء
          </Button>
          <Button
            onClick={handleSmartEnrollment}
            variant="contained"
            disabled={loading || !formData.studentId || !formData.courseId}
            startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}
          >
            {loading ? 'جاري التسجيل...' : 'تسجيل ذكي'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SmartEnrollmentManager;
