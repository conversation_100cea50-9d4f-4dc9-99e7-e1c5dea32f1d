# إعداد قاعدة البيانات SQL التلقائي 🗄️

## 🚀 الإعداد السريع (5 دقائق)

### الخطوة 1: إنشاء قاعدة البيانات

#### أ) PlanetScale (موصى به - مجاني)
1. اذه<PERSON> إلى [PlanetScale](https://planetscale.com)
2. أنشئ حساب جديد (مجاني)
3. اضغط "Create database"
4. اسم قاعدة البيانات: `skills-world-academy`
5. اختر المنطقة: `AWS us-east-1`
6. اضغط "Create database"

#### ب) Railway (بديل سريع)
1. اذهب إلى [Railway](https://railway.app)
2. أنشئ حساب جديد
3. اضغط "New Project" → "Provision MySQL"
4. انتظر حتى يكتمل الإعداد

### الخطوة 2: الحصول على بيانات الاتصال

#### PlanetScale:
1. اذه<PERSON> إلى قاعدة البيانات التي أنشأتها
2. اضغط "Connect"
3. اختر "Connect with: General"
4. انسخ البيانات

#### Railway:
1. اضغط على قاعدة البيانات MySQL
2. اذهب إلى تبويب "Connect"
3. انسخ بيانات الاتصال

### الخطوة 3: إضافة البيانات لملف .env

أضف هذه الأسطر لملف `.env` في مجلد `frontend`:

```env
# قاعدة البيانات SQL
REACT_APP_DB_HOST=your_host_here
REACT_APP_DB_USER=your_username_here
REACT_APP_DB_PASSWORD=your_password_here
REACT_APP_DB_NAME=skills-world-academy
REACT_APP_DB_PORT=3306
REACT_APP_USE_SQL=true
```

**مثال لـ PlanetScale:**
```env
REACT_APP_DB_HOST=aws.connect.psdb.cloud
REACT_APP_DB_USER=abc123def456
REACT_APP_DB_PASSWORD=pscale_pw_xyz789
REACT_APP_DB_NAME=skills-world-academy
REACT_APP_DB_PORT=3306
REACT_APP_USE_SQL=true
```

### الخطوة 4: إعداد Firebase Functions

```bash
# في مجلد functions
cd functions

# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
firebase functions:config:set db.host="your_host"
firebase functions:config:set db.user="your_username"
firebase functions:config:set db.password="your_password"
firebase functions:config:set db.name="skills-world-academy"
firebase functions:config:set db.port="3306"
```

### الخطوة 5: النشر والإعداد

```bash
# نشر Functions
firebase deploy --only functions

# تشغيل التطبيق محلياً
cd ../frontend
npm start
```

### الخطوة 6: الإعداد التلقائي

1. افتح المتصفح على `http://localhost:3000`
2. اضغط F12 لفتح وحدة التحكم
3. شغّل الأمر:

```javascript
autoSQLSetup()
```

## 🎯 ما سيحدث تلقائياً:

### ✅ إنشاء الجداول:
- `users` - المستخدمين (مدراء وطلاب)
- `courses` - الكورسات
- `course_videos` - فيديوهات الكورسات
- `enrollments` - تسجيلات الطلاب
- `certificates` - الشهادات
- `faqs` - الأسئلة الشائعة
- `settings` - الإعدادات العامة

### ✅ إدراج البيانات الأساسية:
- **المدير:** علاء عبد الحميد
- **5 طلاب تجريبيين** بأكواد معروفة
- **3 كورسات تجريبية** مع فيديوهات
- **5 أسئلة شائعة** أساسية
- **إعدادات النظام** الأساسية

### ✅ البيانات الجاهزة للاستخدام:

#### 👨‍💼 تسجيل دخول المدير:
- **البريد:** `<EMAIL>`
- **كلمة المرور:** `Admin123!`

#### 👨‍🎓 أكواد الطلاب:
- **أحمد محمد علي:** `123456`
- **فاطمة أحمد حسن:** `654321`
- **محمد عبد الله:** `111111`
- **سارة أحمد:** `222222`
- **يوسف محمد:** `333333`

#### 📚 الكورسات المتاحة:
1. **مقدمة في التسويق الرقمي** (3 فيديوهات)
2. **إدارة وسائل التواصل الاجتماعي** (4 فيديوهات)
3. **التجارة الإلكترونية للمبتدئين** (5 فيديوهات)

## 🔧 استكشاف الأخطاء

### مشكلة: "فشل في الاتصال بقاعدة البيانات"
**الحل:**
1. تحقق من صحة بيانات الاتصال في `.env`
2. تأكد من أن قاعدة البيانات تعمل
3. شغّل `testSQLConnection()` للاختبار

### مشكلة: "Database connection error"
**الحل:**
```javascript
// اختبار الاتصال
testSQLConnection()

// إعادة المحاولة
autoSQLSetup()
```

### مشكلة: "Functions deployment failed"
**الحل:**
```bash
# تحقق من التبعيات
cd functions
npm install mysql2

# إعادة النشر
firebase deploy --only functions
```

## 🌐 النشر النهائي

بعد نجاح الإعداد المحلي:

```bash
# بناء التطبيق
npm run build

# نشر كامل
firebase deploy

# اختبار الموقع المنشور
# افتح https://marketwise-academy-qhizq.web.app
# شغّل autoSQLSetup() في وحدة التحكم
```

## 📊 مراقبة الأداء

### أوامر مفيدة في وحدة التحكم:

```javascript
// فحص حالة قاعدة البيانات
testSQLConnection()

// إعادة الإعداد الكامل
autoSQLSetup()

// فحص البيانات
systemCheck()

// اختبار تسجيل دخول طالب
testStudentLogin('123456')
```

## 🎉 النتيجة النهائية

بعد اكتمال الإعداد ستحصل على:

✅ **قاعدة بيانات SQL كاملة** مع جميع الجداول
✅ **بيانات تجريبية جاهزة** للاختبار
✅ **API endpoints** متكاملة
✅ **نظام مصادقة** يعمل مع SQL
✅ **لوحة مدير** متصلة بـ SQL
✅ **لوحة طالب** تعرض البيانات الحقيقية
✅ **نظام متكامل** جاهز للإنتاج

## 📞 الدعم

إذا واجهت أي مشاكل:
- **البريد:** ALAA <EMAIL>
- **الهاتف:** 0506747770

---

**ملاحظة:** هذا الإعداد سيحول مشروعك من Firestore إلى SQL بالكامل مع الحفاظ على جميع الميزات الموجودة.
