const admin = require('firebase-admin');
const crypto = require('crypto');

/**
 * خدمة الأمان وتسجيل الأنشطة
 */
class SecurityService {
  /**
   * تسجيل حدث أمني
   * @param {string} eventType - نوع الحدث
   * @param {Object} data - بيانات الحدث
   * @param {string} ip - عنوان IP
   * @param {string} userAgent - معلومات المتصفح
   * @returns {Promise<string>} معرف السجل
   */
  static async logSecurityEvent(eventType, data, ip = null, userAgent = null) {
    try {
      const db = admin.firestore();
      
      const logEntry = {
        eventType,
        data: this.sanitizeData(data),
        ip: this.hashIP(ip),
        userAgent: this.sanitizeUserAgent(userAgent),
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        createdAt: new Date().toISOString(),
        severity: this.getEventSeverity(eventType),
        hash: this.generateEventHash(eventType, data, ip)
      };

      const docRef = await db.collection('security_logs').add(logEntry);
      
      console.log(`🔒 تم تسجيل الحدث الأمني: ${eventType} - ID: ${docRef.id}`);
      
      // إرسال تنبيه للأحداث الحرجة
      if (logEntry.severity === 'HIGH') {
        await this.sendSecurityAlert(eventType, data);
      }
      
      return docRef.id;
      
    } catch (error) {
      console.error('❌ خطأ في تسجيل الحدث الأمني:', error);
      throw error;
    }
  }

  /**
   * تنظيف البيانات الحساسة
   * @param {Object} data - البيانات الأصلية
   * @returns {Object} البيانات المنظفة
   */
  static sanitizeData(data) {
    const sanitized = { ...data };
    
    // إزالة البيانات الحساسة
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    
    const removeSensitiveData = (obj) => {
      for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          removeSensitiveData(obj[key]);
        } else if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
          obj[key] = '[REDACTED]';
        }
      }
    };
    
    removeSensitiveData(sanitized);
    return sanitized;
  }

  /**
   * تشفير عنوان IP
   * @param {string} ip - عنوان IP
   * @returns {string} عنوان IP المشفر
   */
  static hashIP(ip) {
    if (!ip) return null;
    
    // استخدام SHA-256 لتشفير IP مع salt
    const salt = 'security_salt_2024';
    return crypto.createHash('sha256').update(ip + salt).digest('hex').substring(0, 16);
  }

  /**
   * تنظيف معلومات المتصفح
   * @param {string} userAgent - معلومات المتصفح
   * @returns {string} معلومات المتصفح المنظفة
   */
  static sanitizeUserAgent(userAgent) {
    if (!userAgent) return null;
    
    // الاحتفاظ بالمعلومات الأساسية فقط
    const basicInfo = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/[\d.]+/);
    return basicInfo ? basicInfo[0] : 'Unknown Browser';
  }

  /**
   * تحديد مستوى خطورة الحدث
   * @param {string} eventType - نوع الحدث
   * @returns {string} مستوى الخطورة
   */
  static getEventSeverity(eventType) {
    const severityMap = {
      'PASSWORD_RESET_SENT': 'MEDIUM',
      'PASSWORD_RESET_FAILED': 'MEDIUM',
      'PASSWORD_RESET_ERROR': 'HIGH',
      'INVALID_PHONE_ATTEMPT': 'HIGH',
      'RATE_LIMITED': 'MEDIUM',
      'SUSPICIOUS_ACTIVITY': 'HIGH',
      'MULTIPLE_FAILED_ATTEMPTS': 'HIGH',
      'ADMIN_LOGIN': 'LOW',
      'ADMIN_LOGOUT': 'LOW',
      'SYSTEM_ERROR': 'HIGH'
    };
    
    return severityMap[eventType] || 'MEDIUM';
  }

  /**
   * إنشاء hash فريد للحدث
   * @param {string} eventType - نوع الحدث
   * @param {Object} data - بيانات الحدث
   * @param {string} ip - عنوان IP
   * @returns {string} hash الحدث
   */
  static generateEventHash(eventType, data, ip) {
    const hashData = {
      eventType,
      timestamp: Date.now(),
      ip: ip || 'unknown',
      dataHash: crypto.createHash('md5').update(JSON.stringify(data)).digest('hex')
    };
    
    return crypto.createHash('sha256').update(JSON.stringify(hashData)).digest('hex').substring(0, 12);
  }

  /**
   * إرسال تنبيه أمني للأحداث الحرجة
   * @param {string} eventType - نوع الحدث
   * @param {Object} data - بيانات الحدث
   */
  static async sendSecurityAlert(eventType, data) {
    try {
      console.log('🚨 تنبيه أمني حرج:');
      console.log(`📋 نوع الحدث: ${eventType}`);
      console.log(`⏰ الوقت: ${new Date().toLocaleString('ar-SA')}`);
      console.log(`📊 البيانات: ${JSON.stringify(data, null, 2)}`);
      
      // يمكن إضافة إرسال إيميل أو SMS للمدير هنا
      // await this.sendEmailAlert(eventType, data);
      
    } catch (error) {
      console.error('❌ خطأ في إرسال التنبيه الأمني:', error);
    }
  }

  /**
   * التحقق من الأنشطة المشبوهة
   * @param {string} identifier - معرف المستخدم (IP, phone, etc.)
   * @param {string} action - نوع العملية
   * @param {number} timeWindow - النافذة الزمنية بالدقائق
   * @param {number} maxAttempts - الحد الأقصى للمحاولات
   * @returns {Promise<Object>} نتيجة التحقق
   */
  static async checkSuspiciousActivity(identifier, action, timeWindow = 15, maxAttempts = 5) {
    try {
      const db = admin.firestore();
      const now = new Date();
      const timeThreshold = new Date(now.getTime() - (timeWindow * 60 * 1000));
      
      // البحث عن الأنشطة الحديثة
      const recentActivities = await db.collection('security_logs')
        .where('data.identifier', '==', identifier)
        .where('eventType', '==', action)
        .where('createdAt', '>=', timeThreshold.toISOString())
        .get();
      
      const attemptCount = recentActivities.size;
      
      if (attemptCount >= maxAttempts) {
        // تسجيل نشاط مشبوه
        await this.logSecurityEvent('SUSPICIOUS_ACTIVITY', {
          identifier,
          action,
          attemptCount,
          timeWindow,
          maxAttempts
        });
        
        return {
          suspicious: true,
          attemptCount,
          message: `تم اكتشاف نشاط مشبوه: ${attemptCount} محاولة في ${timeWindow} دقيقة`
        };
      }
      
      return {
        suspicious: false,
        attemptCount,
        remaining: maxAttempts - attemptCount
      };
      
    } catch (error) {
      console.error('❌ خطأ في فحص الأنشطة المشبوهة:', error);
      return { suspicious: false, error: error.message };
    }
  }

  /**
   * تنظيف السجلات القديمة
   * @param {number} daysToKeep - عدد الأيام للاحتفاظ بالسجلات
   * @returns {Promise<number>} عدد السجلات المحذوفة
   */
  static async cleanupOldLogs(daysToKeep = 90) {
    try {
      const db = admin.firestore();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      const oldLogsQuery = await db.collection('security_logs')
        .where('createdAt', '<', cutoffDate.toISOString())
        .limit(500) // تحديد عدد السجلات المحذوفة في المرة الواحدة
        .get();
      
      if (oldLogsQuery.empty) {
        console.log('ℹ️ لا توجد سجلات قديمة للحذف');
        return 0;
      }
      
      const batch = db.batch();
      oldLogsQuery.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
      
      console.log(`🗑️ تم حذف ${oldLogsQuery.size} سجل أمني قديم`);
      return oldLogsQuery.size;
      
    } catch (error) {
      console.error('❌ خطأ في تنظيف السجلات القديمة:', error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات الأمان
   * @param {number} days - عدد الأيام للإحصائيات
   * @returns {Promise<Object>} إحصائيات الأمان
   */
  static async getSecurityStats(days = 7) {
    try {
      const db = admin.firestore();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const logsQuery = await db.collection('security_logs')
        .where('createdAt', '>=', startDate.toISOString())
        .get();
      
      const logs = logsQuery.docs.map(doc => doc.data());
      
      // تجميع الإحصائيات
      const stats = {
        totalEvents: logs.length,
        eventTypes: {},
        severityLevels: { LOW: 0, MEDIUM: 0, HIGH: 0 },
        dailyStats: {},
        topEvents: []
      };
      
      logs.forEach(log => {
        // إحصائيات أنواع الأحداث
        stats.eventTypes[log.eventType] = (stats.eventTypes[log.eventType] || 0) + 1;
        
        // إحصائيات مستويات الخطورة
        stats.severityLevels[log.severity] = (stats.severityLevels[log.severity] || 0) + 1;
        
        // إحصائيات يومية
        const day = log.createdAt.split('T')[0];
        stats.dailyStats[day] = (stats.dailyStats[day] || 0) + 1;
      });
      
      // أهم الأحداث
      stats.topEvents = Object.entries(stats.eventTypes)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([event, count]) => ({ event, count }));
      
      return stats;
      
    } catch (error) {
      console.error('❌ خطأ في جلب إحصائيات الأمان:', error);
      throw error;
    }
  }

  /**
   * تشفير البيانات الحساسة
   * @param {string} data - البيانات المراد تشفيرها
   * @param {string} key - مفتاح التشفير
   * @returns {string} البيانات المشفرة
   */
  static encryptSensitiveData(data, key = 'default_encryption_key_2024') {
    try {
      const algorithm = 'aes-256-cbc';
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(algorithm, key);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return iv.toString('hex') + ':' + encrypted;
      
    } catch (error) {
      console.error('❌ خطأ في تشفير البيانات:', error);
      return data; // إرجاع البيانات الأصلية في حالة الخطأ
    }
  }

  /**
   * فك تشفير البيانات
   * @param {string} encryptedData - البيانات المشفرة
   * @param {string} key - مفتاح فك التشفير
   * @returns {string} البيانات المفكوكة
   */
  static decryptSensitiveData(encryptedData, key = 'default_encryption_key_2024') {
    try {
      const algorithm = 'aes-256-cbc';
      const parts = encryptedData.split(':');
      const iv = Buffer.from(parts[0], 'hex');
      const encrypted = parts[1];
      
      const decipher = crypto.createDecipher(algorithm, key);
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
      
    } catch (error) {
      console.error('❌ خطأ في فك تشفير البيانات:', error);
      return encryptedData; // إرجاع البيانات المشفرة في حالة الخطأ
    }
  }
}

module.exports = SecurityService;
