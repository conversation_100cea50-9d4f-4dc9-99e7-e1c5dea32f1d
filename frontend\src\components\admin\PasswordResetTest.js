import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Collapse
} from '@mui/material';
import {
  Send,
  Security,
  Phone,
  Analytics,
  Refresh,
  ExpandMore,
  ExpandLess,
  CheckCircle,
  Error,
  Warning
} from '@mui/icons-material';
import toast from 'react-hot-toast';

const PasswordResetTest = () => {
  const [testPhone, setTestPhone] = useState('0506747770');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState(null);
  const [recentLogs, setRecentLogs] = useState([]);
  const [showLogs, setShowLogs] = useState(false);
  const [testResults, setTestResults] = useState([]);

  // جلب إحصائيات الأمان (محاكاة)
  const fetchSecurityStats = async () => {
    try {
      // محاكاة إحصائيات للاختبار
      const mockStats = {
        totalEvents: Math.floor(Math.random() * 50) + 10,
        severityLevels: {
          HIGH: Math.floor(Math.random() * 5),
          MEDIUM: Math.floor(Math.random() * 15) + 5,
          LOW: Math.floor(Math.random() * 20) + 10
        },
        topEvents: [
          { event: 'PASSWORD_RESET_SENT', count: Math.floor(Math.random() * 10) + 1 },
          { event: 'INVALID_PHONE_ATTEMPT', count: Math.floor(Math.random() * 5) },
          { event: 'RATE_LIMITED', count: Math.floor(Math.random() * 3) }
        ]
      };

      const mockLogs = Array.from({ length: 5 }, (_, i) => ({
        id: `mock_${i}`,
        eventType: ['PASSWORD_RESET_SENT', 'INVALID_PHONE_ATTEMPT', 'RATE_LIMITED'][Math.floor(Math.random() * 3)],
        severity: ['HIGH', 'MEDIUM', 'LOW'][Math.floor(Math.random() * 3)],
        createdAt: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        data: {
          phone: '+966506747770',
          mock: true
        }
      }));

      setStats(mockStats);
      setRecentLogs(mockLogs);
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
    }
  };

  // اختبار إرسال SMS (محاكاة)
  const testSMSService = async () => {
    setLoading(true);

    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log('🧪 اختبار SMS وهمي للرقم:', testPhone);
      console.log('📱 رسالة اختبار: تم إرسال رسالة اختبار من SKILLS WORLD ACADEMY');

      const result = {
        success: true,
        message: 'تم إرسال رسالة الاختبار بنجاح (وضع المحاكاة)',
        service: 'Mock SMS Service',
        result: {
          mock: true,
          messageId: 'MOCK_' + Date.now(),
          to: testPhone
        }
      };
      
      const testResult = {
        timestamp: new Date().toLocaleString('ar-SA'),
        phone: testPhone,
        success: result.success,
        message: result.message,
        service: result.service,
        details: result.result
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 4)]);
      
      if (result.success) {
        toast.success('تم إرسال رسالة الاختبار بنجاح');
      } else {
        toast.error('فشل في إرسال رسالة الاختبار');
      }
      
      // تحديث الإحصائيات
      setTimeout(fetchSecurityStats, 1000);
      
    } catch (error) {
      console.error('خطأ في اختبار SMS:', error);
      toast.error('حدث خطأ في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  // اختبار استرداد كلمة المرور (محاكاة)
  const testPasswordReset = async () => {
    setLoading(true);

    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 2000));

      const adminPhone = '0506747770';
      const normalizedTestPhone = testPhone.replace(/[\s\-\(\)]/g, '');
      const normalizedAdminPhone = adminPhone.replace(/[\s\-\(\)]/g, '');

      console.log('🔐 اختبار استرداد كلمة المرور للرقم:', testPhone);
      console.log('🔍 مقارنة مع رقم المدير:', adminPhone);

      let result;
      if (normalizedTestPhone === normalizedAdminPhone ||
          normalizedTestPhone === '+966506747770' ||
          normalizedTestPhone === '966506747770') {
        result = {
          success: true,
          message: 'تم إرسال كلمة المرور بنجاح (وضع المحاكاة)',
          mock: true
        };
        console.log('✅ تم إرسال كلمة المرور: Admin123!');
      } else {
        result = {
          success: false,
          message: 'رقم الهاتف غير مطابق لرقم هاتف المدير المسجل'
        };
        console.log('❌ رقم الهاتف غير صحيح');
      }
      
      const testResult = {
        timestamp: new Date().toLocaleString('ar-SA'),
        phone: testPhone,
        success: result.success,
        message: result.message,
        type: 'Password Reset',
        mock: result.mock
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 4)]);
      
      if (result.success) {
        toast.success('تم إرسال كلمة المرور بنجاح');
      } else {
        toast.error(result.message || 'فشل في إرسال كلمة المرور');
      }
      
      // تحديث الإحصائيات
      setTimeout(fetchSecurityStats, 1000);
      
    } catch (error) {
      console.error('خطأ في اختبار استرداد كلمة المرور:', error);
      toast.error('حدث خطأ في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  // تحميل الإحصائيات عند بدء التشغيل
  useEffect(() => {
    fetchSecurityStats();
  }, []);

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'HIGH': return 'error';
      case 'MEDIUM': return 'warning';
      case 'LOW': return 'success';
      default: return 'default';
    }
  };

  const getEventTypeColor = (eventType) => {
    switch (eventType) {
      case 'PASSWORD_RESET_SENT': return 'success';
      case 'PASSWORD_RESET_FAILED': return 'error';
      case 'INVALID_PHONE_ATTEMPT': return 'error';
      case 'RATE_LIMITED': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Security color="primary" />
        اختبار نظام استرداد كلمة المرور
      </Typography>

      <Grid container spacing={3}>
        {/* قسم الاختبار */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🧪 اختبار الخدمات
              </Typography>

              <TextField
                fullWidth
                label="رقم الهاتف للاختبار"
                value={testPhone}
                onChange={(e) => setTestPhone(e.target.value)}
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />

              <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                <Button
                  variant="contained"
                  onClick={testSMSService}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Send />}
                  fullWidth
                >
                  اختبار SMS
                </Button>

                <Button
                  variant="outlined"
                  onClick={testPasswordReset}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Security />}
                  fullWidth
                >
                  اختبار استرداد كلمة المرور
                </Button>
              </Box>

              {/* نتائج الاختبار */}
              {testResults.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    آخر نتائج الاختبار:
                  </Typography>
                  {testResults.map((result, index) => (
                    <Alert
                      key={index}
                      severity={result.success ? 'success' : 'error'}
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2">
                        <strong>{result.timestamp}</strong> - {result.message}
                        {result.service && ` (${result.service})`}
                        {result.mock && ' [وضع الاختبار]'}
                      </Typography>
                    </Alert>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* قسم الإحصائيات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  📊 إحصائيات الأمان
                </Typography>
                <IconButton onClick={fetchSecurityStats} size="small">
                  <Refresh />
                </IconButton>
              </Box>

              {stats ? (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 2 }}>
                      <Typography variant="h4" color="white">{stats.totalEvents}</Typography>
                      <Typography variant="body2" color="white">إجمالي الأحداث</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 2 }}>
                      <Typography variant="h4" color="white">{stats.severityLevels.HIGH}</Typography>
                      <Typography variant="body2" color="white">أحداث عالية الخطورة</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      أهم الأحداث:
                    </Typography>
                    {stats.topEvents.map((event, index) => (
                      <Chip
                        key={index}
                        label={`${event.event}: ${event.count}`}
                        color={getEventTypeColor(event.event)}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                  </Grid>
                </Grid>
              ) : (
                <CircularProgress />
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* قسم السجلات */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  📋 السجلات الأمنية الحديثة
                </Typography>
                <Button
                  onClick={() => setShowLogs(!showLogs)}
                  endIcon={showLogs ? <ExpandLess /> : <ExpandMore />}
                >
                  {showLogs ? 'إخفاء' : 'عرض'} السجلات
                </Button>
              </Box>

              <Collapse in={showLogs}>
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>الوقت</TableCell>
                        <TableCell>نوع الحدث</TableCell>
                        <TableCell>الخطورة</TableCell>
                        <TableCell>التفاصيل</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentLogs.map((log, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            {new Date(log.createdAt).toLocaleString('ar-SA')}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.eventType}
                              color={getEventTypeColor(log.eventType)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.severity}
                              color={getSeverityColor(log.severity)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {log.data?.phone && `📱 ${log.data.phone}`}
                            {log.data?.error && ` ❌ ${log.data.error}`}
                            {log.data?.mock && ' 🧪'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Collapse>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* معلومات إضافية */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            ℹ️ معلومات مهمة
          </Typography>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>وضع الاختبار:</strong> النظام يستخدم خدمة SMS وهمية للاختبار. 
              لتفعيل الإرسال الحقيقي، يجب إعداد Twilio في إعدادات Firebase Functions.
            </Typography>
          </Alert>

          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>رقم المدير المسجل:</strong> 0506747770 - فقط هذا الرقم يمكنه استلام كلمة المرور.
            </Typography>
          </Alert>

          <Alert severity="success">
            <Typography variant="body2">
              <strong>كلمة المرور:</strong> Admin123! - هذه هي كلمة المرور التي سيتم إرسالها.
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PasswordResetTest;
