# دليل نشر منصة كورسات علاء عبد الحميد

## 🗄️ الخطوة 1: إعداد قاعدة البيانات السحابية

### MongoDB Atlas (مجاني):
1. اذهب إلى: https://cloud.mongodb.com
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ Cluster جديد (اختر المجاني M0)
4. اختر المنطقة الأقرب لك
5. في Database Access:
   - أنشئ مستخدم جديد
   - احفظ اسم المستخدم وكلمة المرور
6. في Network Access:
   - أضف IP Address: 0.0.0.0/0 (للوصول من أي مكان)
7. احصل على Connection String

## 🌐 الخطوة 2: النشر على Render (مجاني)

### إعداد المستودع:
```bash
# إذا لم يكن لديك Git repository
git init
git add .
git commit -m "Initial commit"

# رفع على GitHub
# أنشئ repository جديد على GitHub
git remote add origin https://github.com/username/alaa-courses.git
git push -u origin main
```

### النشر على Render:
1. اذهب إلى: https://render.com
2. سجل دخول بحساب GitHub
3. أنشئ Web Service جديد
4. اربط مع GitHub repository
5. إعدادات النشر:
   - Build Command: `npm install && npm run build`
   - Start Command: `npm start`
   - Environment: Node

## 🔧 الخطوة 3: متغيرات البيئة

### في Render Dashboard:
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/alaa-courses
JWT_SECRET=your-super-secret-jwt-key-here
PORT=5000
FRONTEND_URL=https://your-app-name.onrender.com
```

## 🎯 الخطوة 4: تحديث الكود للإنتاج

### تحديث package.json:
```json
{
  "scripts": {
    "start": "node server.js",
    "build": "cd frontend && npm install && npm run build",
    "postinstall": "npm run build"
  }
}
```

## 🔄 الخطوة 5: النشر التلقائي

### إنشاء ملف render.yaml:
```yaml
services:
  - type: web
    name: alaa-courses
    env: node
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGODB_URI
        fromDatabase:
          name: alaa-courses-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
```

## 📱 بدائل أخرى للنشر:

### Vercel (للواجهة الأمامية):
1. اذهب إلى: https://vercel.com
2. اربط مع GitHub
3. انشر الواجهة الأمامية

### Railway (شامل):
1. اذهب إلى: https://railway.app
2. اربط مع GitHub
3. انشر المشروع كاملاً

### Heroku (مدفوع الآن):
1. اذهب إلى: https://heroku.com
2. أنشئ تطبيق جديد
3. اربط مع GitHub

## 🛠️ إعدادات إضافية للإنتاج:

### تحديث CORS:
```javascript
app.use(cors({
  origin: [
    'https://your-domain.com',
    'https://your-app.onrender.com'
  ],
  credentials: true
}));
```

### إضافة SSL:
```javascript
// في server.js
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}
```

## 📊 مراقبة الأداء:

### إضافة Logging:
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

## 🔐 الأمان في الإنتاج:

### متغيرات البيئة الآمنة:
- استخدم مفاتيح JWT قوية
- لا تضع كلمات المرور في الكود
- استخدم HTTPS دائماً
- فعل Rate Limiting

## 📝 قائمة المراجعة النهائية:

- [ ] قاعدة البيانات السحابية جاهزة
- [ ] متغيرات البيئة محددة
- [ ] الكود محدث للإنتاج
- [ ] SSL مفعل
- [ ] CORS محدد بشكل صحيح
- [ ] النسخ الاحتياطية مجدولة
- [ ] المراقبة مفعلة

## 🎉 بعد النشر:

1. اختبر جميع الوظائف
2. تأكد من عمل تسجيل الدخول
3. اختبر رفع الملفات
4. تأكد من إنشاء الشهادات
5. اختبر الأداء

---

**ملاحظة:** احتفظ بنسخة احتياطية من قاعدة البيانات دائماً!
