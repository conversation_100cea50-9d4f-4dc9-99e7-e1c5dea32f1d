// إعداد قاعدة البيانات SQL التلقائي
// يعمل مع PlanetScale, Railway, Supabase وغيرها

/**
 * إعداد قاعدة البيانات SQL التلقائي الكامل
 */
export const autoSQLSetup = async () => {
  console.log('🚀 بدء الإعداد التلقائي لقاعدة البيانات SQL...');
  
  try {
    // الخطوة 1: اختبار الاتصال
    console.log('📡 اختبار الاتصال بقاعدة البيانات...');
    const connectionTest = await testSQLConnection();
    
    if (!connectionTest.success) {
      console.log('❌ فشل الاتصال بقاعدة البيانات');
      console.log('💡 تعليمات الإعداد:');
      console.log('1. أنشئ حساب في PlanetScale: https://planetscale.com');
      console.log('2. أنشئ قاعدة بيانات جديدة باسم: skills-world-academy');
      console.log('3. احصل على بيانات الاتصال وأضفها لملف .env');
      console.log('4. شغّل السكريبت مرة أخرى');
      
      return {
        success: false,
        message: 'يجب إعداد قاعدة البيانات أولاً',
        instructions: getSetupInstructions()
      };
    }

    // الخطوة 2: إنشاء الجداول
    console.log('🗄️ إنشاء جداول قاعدة البيانات...');
    await createDatabaseTables();

    // الخطوة 3: إدراج البيانات الأساسية
    console.log('📊 إدراج البيانات الأساسية...');
    await insertInitialData();

    // الخطوة 4: التحقق من البيانات
    console.log('✅ التحقق من البيانات...');
    const verification = await verifyDatabaseSetup();

    if (verification.success) {
      console.log('🎉 تم إعداد قاعدة البيانات SQL بنجاح!');
      console.log('📋 ملخص البيانات:');
      console.log(`👨‍💼 المدراء: ${verification.stats.admins}`);
      console.log(`👨‍🎓 الطلاب: ${verification.stats.students}`);
      console.log(`📚 الكورسات: ${verification.stats.courses}`);
      console.log(`🎥 الفيديوهات: ${verification.stats.videos}`);
      console.log(`❓ الأسئلة الشائعة: ${verification.stats.faqs}`);
      
      console.log('');
      console.log('🔑 بيانات تسجيل الدخول:');
      console.log('👨‍💼 المدير:');
      console.log('   البريد: <EMAIL>');
      console.log('   كلمة المرور: Admin123!');
      console.log('');
      console.log('👨‍🎓 أكواد الطلاب:');
      console.log('   أحمد محمد علي: 123456');
      console.log('   فاطمة أحمد حسن: 654321');
      console.log('   محمد عبد الله: 111111');
      console.log('   سارة أحمد: 222222');
      console.log('   يوسف محمد: 333333');

      return {
        success: true,
        message: 'تم إعداد قاعدة البيانات SQL بنجاح',
        stats: verification.stats
      };
    } else {
      throw new Error('فشل في التحقق من البيانات');
    }

  } catch (error) {
    console.error('❌ خطأ في الإعداد التلقائي:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};

/**
 * اختبار الاتصال بقاعدة البيانات
 */
const testSQLConnection = async () => {
  try {
    const response = await fetch('/api/sql/test', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      return { success: true, data: result };
    } else {
      return { success: false, message: 'فشل في الاتصال' };
    }
  } catch (error) {
    return { success: false, message: error.message };
  }
};

/**
 * إنشاء جداول قاعدة البيانات
 */
const createDatabaseTables = async () => {
  const tables = [
    // جدول المستخدمين
    `CREATE TABLE IF NOT EXISTS users (
      id VARCHAR(36) PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      email VARCHAR(255),
      phone VARCHAR(20),
      role ENUM('admin', 'student') NOT NULL,
      student_code VARCHAR(6),
      password_hash VARCHAR(255),
      avatar_url TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      last_login TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      UNIQUE KEY unique_email (email),
      UNIQUE KEY unique_student_code (student_code),
      INDEX idx_role (role),
      INDEX idx_active (is_active)
    )`,

    // جدول الكورسات
    `CREATE TABLE IF NOT EXISTS courses (
      id VARCHAR(36) PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      instructor VARCHAR(255) NOT NULL,
      duration VARCHAR(50),
      level ENUM('مبتدئ', 'متوسط', 'متقدم') DEFAULT 'مبتدئ',
      price DECIMAL(10,2) DEFAULT 0.00,
      thumbnail_url TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      enrolled_students INT DEFAULT 0,
      total_videos INT DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      INDEX idx_active (is_active)
    )`,

    // جدول الفيديوهات
    `CREATE TABLE IF NOT EXISTS course_videos (
      id VARCHAR(36) PRIMARY KEY,
      course_id VARCHAR(36) NOT NULL,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      video_url TEXT,
      duration_minutes INT DEFAULT 0,
      video_order INT NOT NULL,
      is_free BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      INDEX idx_course_order (course_id, video_order)
    )`,

    // جدول التسجيلات
    `CREATE TABLE IF NOT EXISTS enrollments (
      id VARCHAR(36) PRIMARY KEY,
      student_id VARCHAR(36) NOT NULL,
      course_id VARCHAR(36) NOT NULL,
      enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      progress_percentage DECIMAL(5,2) DEFAULT 0.00,
      is_completed BOOLEAN DEFAULT FALSE,
      completed_at TIMESTAMP NULL,
      
      UNIQUE KEY unique_enrollment (student_id, course_id),
      INDEX idx_student (student_id),
      INDEX idx_course (course_id)
    )`,

    // جدول الشهادات
    `CREATE TABLE IF NOT EXISTS certificates (
      id VARCHAR(36) PRIMARY KEY,
      student_id VARCHAR(36) NOT NULL,
      course_id VARCHAR(36) NOT NULL,
      certificate_number VARCHAR(50) NOT NULL,
      issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      certificate_url TEXT,
      is_valid BOOLEAN DEFAULT TRUE,
      
      UNIQUE KEY unique_certificate (student_id, course_id),
      UNIQUE KEY unique_cert_number (certificate_number)
    )`,

    // جدول الأسئلة الشائعة
    `CREATE TABLE IF NOT EXISTS faqs (
      id VARCHAR(36) PRIMARY KEY,
      question TEXT NOT NULL,
      answer TEXT NOT NULL,
      category VARCHAR(100),
      display_order INT DEFAULT 0,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      INDEX idx_active (is_active)
    )`,

    // جدول الإعدادات
    `CREATE TABLE IF NOT EXISTS settings (
      id VARCHAR(36) PRIMARY KEY,
      setting_key VARCHAR(100) NOT NULL,
      setting_value TEXT,
      setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
      description TEXT,
      is_public BOOLEAN DEFAULT FALSE,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      UNIQUE KEY unique_setting_key (setting_key)
    )`
  ];

  for (const tableSQL of tables) {
    await executeSQL(tableSQL);
  }
};

/**
 * إدراج البيانات الأساسية
 */
const insertInitialData = async () => {
  // إدراج الإعدادات
  await executeSQL(`
    INSERT IGNORE INTO settings (id, setting_key, setting_value, setting_type, description, is_public) VALUES
    ('set-001', 'academy_name', 'SKILLS WORLD ACADEMY', 'string', 'اسم الأكاديمية', TRUE),
    ('set-002', 'admin_name', 'علاء عبد الحميد', 'string', 'اسم المدير', TRUE),
    ('set-003', 'admin_email', 'ALAA <EMAIL>', 'string', 'بريد المدير', FALSE),
    ('set-004', 'admin_phone', '0506747770', 'string', 'هاتف المدير', FALSE),
    ('set-005', 'academy_version', '2.0.0', 'string', 'إصدار النظام', TRUE)
  `);

  // إدراج المدير
  await executeSQL(`
    INSERT IGNORE INTO users (id, name, email, phone, role, is_active, created_at, updated_at) VALUES
    ('admin-001', 'علاء عبد الحميد', '<EMAIL>', '0506747770', 'admin', TRUE, NOW(), NOW())
  `);

  // إدراج الطلاب
  await executeSQL(`
    INSERT IGNORE INTO users (id, name, email, phone, role, student_code, is_active, created_at, updated_at) VALUES
    ('student-001', 'أحمد محمد علي', '<EMAIL>', '0501234567', 'student', '123456', TRUE, NOW(), NOW()),
    ('student-002', 'فاطمة أحمد حسن', '<EMAIL>', '0507654321', 'student', '654321', TRUE, NOW(), NOW()),
    ('student-003', 'محمد عبد الله', '<EMAIL>', '0509876543', 'student', '111111', TRUE, NOW(), NOW()),
    ('student-004', 'سارة أحمد', '<EMAIL>', '0502468135', 'student', '222222', TRUE, NOW(), NOW()),
    ('student-005', 'يوسف محمد', '<EMAIL>', '0508642097', 'student', '333333', TRUE, NOW(), NOW())
  `);

  // إدراج الأسئلة الشائعة
  await executeSQL(`
    INSERT IGNORE INTO faqs (id, question, answer, category, display_order, is_active, created_at, updated_at) VALUES
    ('faq-001', 'كيف يمكنني التسجيل في الكورسات؟', 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير', 'التسجيل', 1, TRUE, NOW(), NOW()),
    ('faq-002', 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟', 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات', 'المشاهدة', 2, TRUE, NOW(), NOW()),
    ('faq-003', 'كيف أحصل على شهادة إتمام الكورس؟', 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس', 'الشهادات', 3, TRUE, NOW(), NOW()),
    ('faq-004', 'ماذا أفعل إذا نسيت كود الطالب؟', 'تواصل مع المدير للحصول على كود الطالب الخاص بك', 'الدعم', 4, TRUE, NOW(), NOW()),
    ('faq-005', 'هل يمكنني تحميل الفيديوهات؟', 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط', 'المشاهدة', 5, TRUE, NOW(), NOW())
  `);

  // إدراج الكورسات
  await executeSQL(`
    INSERT IGNORE INTO courses (id, title, description, instructor, duration, level, is_active, total_videos, created_at, updated_at) VALUES
    ('course-001', 'مقدمة في التسويق الرقمي', 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين', 'علاء عبد الحميد', '4 ساعات', 'مبتدئ', TRUE, 3, NOW(), NOW()),
    ('course-002', 'إدارة وسائل التواصل الاجتماعي', 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية', 'علاء عبد الحميد', '6 ساعات', 'متوسط', TRUE, 4, NOW(), NOW()),
    ('course-003', 'التجارة الإلكترونية للمبتدئين', 'دليل شامل لبدء متجرك الإلكتروني من الصفر', 'علاء عبد الحميد', '8 ساعات', 'مبتدئ', TRUE, 5, NOW(), NOW())
  `);
};

/**
 * التحقق من إعداد قاعدة البيانات
 */
const verifyDatabaseSetup = async () => {
  try {
    const stats = {
      admins: 0,
      students: 0,
      courses: 0,
      videos: 0,
      faqs: 0
    };

    // عد المدراء
    const adminsResult = await executeSQL('SELECT COUNT(*) as count FROM users WHERE role = "admin"');
    stats.admins = adminsResult.data[0].count;

    // عد الطلاب
    const studentsResult = await executeSQL('SELECT COUNT(*) as count FROM users WHERE role = "student"');
    stats.students = studentsResult.data[0].count;

    // عد الكورسات
    const coursesResult = await executeSQL('SELECT COUNT(*) as count FROM courses');
    stats.courses = coursesResult.data[0].count;

    // عد الفيديوهات
    const videosResult = await executeSQL('SELECT COUNT(*) as count FROM course_videos');
    stats.videos = videosResult.data[0].count;

    // عد الأسئلة الشائعة
    const faqsResult = await executeSQL('SELECT COUNT(*) as count FROM faqs');
    stats.faqs = faqsResult.data[0].count;

    return {
      success: true,
      stats
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * تنفيذ استعلام SQL
 */
const executeSQL = async (query, params = []) => {
  try {
    const response = await fetch('/api/sql/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, params })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('SQL execution error:', error);
    throw error;
  }
};

/**
 * تعليمات الإعداد
 */
const getSetupInstructions = () => {
  return {
    planetscale: {
      name: 'PlanetScale (موصى به)',
      steps: [
        '1. اذهب إلى https://planetscale.com',
        '2. أنشئ حساب جديد (مجاني)',
        '3. أنشئ قاعدة بيانات باسم: skills-world-academy',
        '4. احصل على بيانات الاتصال من Dashboard',
        '5. أضف البيانات لملف .env'
      ],
      env: `
REACT_APP_DB_HOST=aws.connect.psdb.cloud
REACT_APP_DB_USER=your_username
REACT_APP_DB_PASSWORD=your_password
REACT_APP_DB_NAME=skills-world-academy
REACT_APP_DB_PORT=3306
      `
    },
    railway: {
      name: 'Railway',
      steps: [
        '1. اذهب إلى https://railway.app',
        '2. أنشئ حساب جديد',
        '3. أنشئ مشروع جديد واختر MySQL',
        '4. احصل على بيانات الاتصال',
        '5. أضف البيانات لملف .env'
      ]
    }
  };
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.autoSQLSetup = autoSQLSetup;
window.testSQLConnection = testSQLConnection;

export default {
  autoSQLSetup,
  testSQLConnection
};
