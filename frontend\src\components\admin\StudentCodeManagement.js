import React, { useState, useEffect } from 'react';
import { useRealtimeStudents } from '../../hooks/useRealtimeData';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  ContentCopy as CopyIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import studentService from '../../firebase/studentService';
import { generateStudentCode } from '../../firebase/databaseService';

const StudentCodeManagement = () => {
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [newStudentData, setNewStudentData] = useState({
    name: '',
    email: '',
    phone: ''
  });

  // استخدام Real-time Updates للطلاب
  const { students: realtimeStudents, loading: studentsLoading } = useRealtimeStudents();

  useEffect(() => {
    if (realtimeStudents.length > 0) {
      setStudents(realtimeStudents);
      setLoading(false);
    }
  }, [realtimeStudents]);

  const fetchStudents = async () => {
    // لم تعد هناك حاجة لهذه الدالة مع Real-time Updates
    // ستبقى للتوافق مع الكود الموجود
    if (realtimeStudents.length === 0) {
      try {
        setLoading(true);
        const studentsData = await studentService.getAllStudents();
        setStudents(studentsData);
      } catch (error) {
        console.error('خطأ في جلب الطلاب:', error);
        toast.error('فشل في جلب بيانات الطلاب');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCreateStudent = async () => {
    try {
      if (!newStudentData.name.trim()) {
        toast.error('اسم الطالب مطلوب');
        return;
      }

      const newStudent = await studentService.createStudent(newStudentData, 'admin');
      setStudents([newStudent, ...students]);
      
      toast.success(`تم إنشاء الطالب بنجاح! كود التسجيل: ${newStudent.studentCode}`);
      
      // نسخ كود التسجيل إلى الحافظة
      navigator.clipboard.writeText(newStudent.studentCode);
      toast.success('تم نسخ كود التسجيل إلى الحافظة');
      
      setOpenDialog(false);
      setNewStudentData({ name: '', email: '', phone: '' });
    } catch (error) {
      console.error('خطأ في إنشاء الطالب:', error);
      toast.error('فشل في إنشاء الطالب');
    }
  };

  const handleCopyCode = (code) => {
    navigator.clipboard.writeText(code);
    toast.success('تم نسخ كود التسجيل');
  };

  const handleDeleteStudent = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      try {
        await studentService.deleteStudent(studentId, 'admin');
        setStudents(students.filter(s => s.id !== studentId));
        toast.success('تم حذف الطالب بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        toast.error('فشل في حذف الطالب');
      }
    }
  };

  const handleToggleStatus = async (studentId, currentStatus) => {
    try {
      await studentService.toggleStudentStatus(studentId, !currentStatus, 'admin');
      setStudents(students.map(s => 
        s.id === studentId ? { ...s, isActive: !currentStatus } : s
      ));
      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الطالب`);
    } catch (error) {
      console.error('خطأ في تغيير حالة الطالب:', error);
      toast.error('فشل في تغيير حالة الطالب');
    }
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" component="h2">
              إدارة أكواد تسجيل الطلاب
            </Typography>
            <Box>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchStudents}
                sx={{ mr: 1 }}
              >
                تحديث
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenDialog(true)}
              >
                إضافة طالب جديد
              </Button>
            </Box>
          </Box>

          <Alert severity="info" sx={{ mb: 3 }}>
            عند إنشاء طالب جديد، سيتم إنشاء كود تسجيل فريد تلقائياً. يمكن للطالب استخدام هذا الكود لتسجيل الدخول إلى المنصة.
          </Alert>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم الطالب</TableCell>
                  <TableCell>كود التسجيل</TableCell>
                  <TableCell>البريد الإلكتروني</TableCell>
                  <TableCell>رقم الهاتف</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>تاريخ الإنشاء</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {students.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>{student.name}</TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Typography variant="body2" sx={{ mr: 1, fontFamily: 'monospace' }}>
                          {student.studentCode}
                        </Typography>
                        <Tooltip title="نسخ الكود">
                          <IconButton
                            size="small"
                            onClick={() => handleCopyCode(student.studentCode)}
                          >
                            <CopyIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell>{student.email || '-'}</TableCell>
                    <TableCell>{student.phone || '-'}</TableCell>
                    <TableCell>
                      <Chip
                        label={student.isActive ? 'مفعل' : 'غير مفعل'}
                        color={student.isActive ? 'success' : 'default'}
                        size="small"
                        onClick={() => handleToggleStatus(student.id, student.isActive)}
                        sx={{ cursor: 'pointer' }}
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(student.createdAt).toLocaleDateString('ar-SA')}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="حذف">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteStudent(student.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {students.length === 0 && !loading && (
            <Box textAlign="center" py={4}>
              <Typography variant="body1" color="text.secondary">
                لا توجد طلاب مسجلين حتى الآن
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Dialog لإضافة طالب جديد */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة طالب جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم الطالب *"
                value={newStudentData.name}
                onChange={(e) => setNewStudentData({ ...newStudentData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={newStudentData.email}
                onChange={(e) => setNewStudentData({ ...newStudentData, email: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={newStudentData.phone}
                onChange={(e) => setNewStudentData({ ...newStudentData, phone: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleCreateStudent}>
            إنشاء الطالب
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentCodeManagement;
