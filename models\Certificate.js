const mongoose = require('mongoose');

const certificateSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  certificateNumber: {
    type: String,
    unique: true,
    required: true
  },
  studentName: {
    type: String,
    required: true
  },
  courseName: {
    type: String,
    required: true
  },
  instructorName: {
    type: String,
    default: 'علاء عبد الحميد'
  },
  completionDate: {
    type: Date,
    required: true
  },
  issueDate: {
    type: Date,
    default: Date.now
  },
  pdfUrl: {
    type: String // رابط ملف PDF للشهادة
  },
  isValid: {
    type: Boolean,
    default: true
  },
  grade: {
    type: String,
    enum: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول'],
    default: 'ممتاز'
  },
  courseDuration: {
    type: Number // مدة الكورس بالساعات
  },
  skills: [{
    type: String // المهارات المكتسبة
  }],
  verificationCode: {
    type: String,
    unique: true // كود للتحقق من صحة الشهادة
  },
  template: {
    type: String,
    default: 'default' // نوع قالب الشهادة
  },
  metadata: {
    totalLessons: Number,
    completedLessons: Number,
    totalWatchTime: Number, // بالدقائق
    averageScore: Number
  }
}, {
  timestamps: true
});

// Generate certificate number before saving
certificateSchema.pre('save', async function(next) {
  if (!this.certificateNumber) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const count = await this.constructor.countDocuments() + 1;
    this.certificateNumber = `ALAA-${year}${month}-${String(count).padStart(4, '0')}`;
  }
  
  if (!this.verificationCode) {
    this.verificationCode = require('crypto').randomBytes(8).toString('hex').toUpperCase();
  }
  
  next();
});

// Indexes
certificateSchema.index({ student: 1, course: 1 }, { unique: true });
certificateSchema.index({ certificateNumber: 1 });
certificateSchema.index({ verificationCode: 1 });
certificateSchema.index({ issueDate: -1 });

module.exports = mongoose.model('Certificate', certificateSchema);
