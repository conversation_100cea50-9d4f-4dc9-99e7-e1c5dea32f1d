import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  writeBatch,
  onSnapshot
} from 'firebase/firestore';
import { db } from './config';
import { logActivity } from './databaseService';

// ===== خدمة إدارة الكورسات =====

/**
 * إنشاء كورس جديد
 */
export const createCourse = async (courseData, adminId) => {
  try {
    const newCourse = {
      ...courseData,
      instructor: courseData.instructor || 'علاء عبد الحميد',
      isPublished: false,
      enrolledStudents: 0,
      totalVideos: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, 'courses'), newCourse);
    
    // تسجيل النشاط
    await logActivity(adminId, 'course_created', {
      courseId: docRef.id,
      courseName: courseData.title
    });

    return {
      id: docRef.id,
      ...newCourse
    };
  } catch (error) {
    console.error('خطأ في إنشاء الكورس:', error);
    throw error;
  }
};

/**
 * تحديث كورس
 */
export const updateCourse = async (courseId, updateData, adminId) => {
  try {
    const courseRef = doc(db, 'courses', courseId);
    
    const updatedData = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(courseRef, updatedData);
    
    // تسجيل النشاط
    await logActivity(adminId, 'course_updated', {
      courseId,
      courseName: updateData.title
    });

    return true;
  } catch (error) {
    console.error('خطأ في تحديث الكورس:', error);
    throw error;
  }
};

/**
 * حذف كورس
 */
export const deleteCourse = async (courseId, adminId) => {
  try {
    const batch = writeBatch(db);
    
    // حذف الكورس
    const courseRef = doc(db, 'courses', courseId);
    batch.delete(courseRef);
    
    // حذف الفيديوهات المرتبطة
    const videosQuery = query(
      collection(db, 'videos'),
      where('courseId', '==', courseId)
    );
    const videosSnapshot = await getDocs(videosQuery);
    
    videosSnapshot.docs.forEach(videoDoc => {
      batch.delete(videoDoc.ref);
    });
    
    // حذف التسجيلات المرتبطة
    const enrollmentsQuery = query(
      collection(db, 'enrollments'),
      where('courseId', '==', courseId)
    );
    const enrollmentsSnapshot = await getDocs(enrollmentsQuery);
    
    enrollmentsSnapshot.docs.forEach(enrollmentDoc => {
      batch.delete(enrollmentDoc.ref);
    });

    await batch.commit();
    
    // تسجيل النشاط
    await logActivity(adminId, 'course_deleted', {
      courseId
    });

    return true;
  } catch (error) {
    console.error('خطأ في حذف الكورس:', error);
    throw error;
  }
};

/**
 * نشر/إلغاء نشر كورس
 */
export const toggleCoursePublication = async (courseId, isPublished, adminId) => {
  try {
    const courseRef = doc(db, 'courses', courseId);
    
    await updateDoc(courseRef, {
      isPublished,
      updatedAt: serverTimestamp()
    });
    
    // تسجيل النشاط
    await logActivity(adminId, isPublished ? 'course_published' : 'course_unpublished', {
      courseId
    });

    return true;
  } catch (error) {
    console.error('خطأ في تغيير حالة النشر:', error);
    throw error;
  }
};

/**
 * جلب جميع الكورسات
 */
export const getAllCourses = async () => {
  try {
    const coursesQuery = query(
      collection(db, 'courses'),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(coursesQuery);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
    }));
  } catch (error) {
    console.error('خطأ في جلب الكورسات:', error);
    throw error;
  }
};

/**
 * جلب الكورسات المنشورة فقط
 */
export const getPublishedCourses = async () => {
  try {
    const coursesQuery = query(
      collection(db, 'courses'),
      where('isPublished', '==', true),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(coursesQuery);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
    }));
  } catch (error) {
    console.error('خطأ في جلب الكورسات المنشورة:', error);
    throw error;
  }
};

/**
 * جلب كورس محدد
 */
export const getCourse = async (courseId) => {
  try {
    const courseDoc = await getDoc(doc(db, 'courses', courseId));
    
    if (!courseDoc.exists()) {
      throw new Error('الكورس غير موجود');
    }

    return {
      id: courseDoc.id,
      ...courseDoc.data(),
      createdAt: courseDoc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: courseDoc.data().updatedAt?.toDate?.() || new Date()
    };
  } catch (error) {
    console.error('خطأ في جلب الكورس:', error);
    throw error;
  }
};

/**
 * إضافة فيديو لكورس
 */
export const addVideoToCourse = async (courseId, videoData, adminId) => {
  try {
    const newVideo = {
      ...videoData,
      courseId,
      isPublished: false,
      createdAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, 'videos'), newVideo);
    
    // تحديث عدد الفيديوهات في الكورس
    const courseRef = doc(db, 'courses', courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (courseDoc.exists()) {
      const currentVideos = courseDoc.data().totalVideos || 0;
      await updateDoc(courseRef, {
        totalVideos: currentVideos + 1,
        updatedAt: serverTimestamp()
      });
    }
    
    // تسجيل النشاط
    await logActivity(adminId, 'video_added', {
      courseId,
      videoId: docRef.id,
      videoTitle: videoData.title
    });

    return {
      id: docRef.id,
      ...newVideo
    };
  } catch (error) {
    console.error('خطأ في إضافة الفيديو:', error);
    throw error;
  }
};

/**
 * جلب فيديوهات كورس
 */
export const getCourseVideos = async (courseId) => {
  try {
    const videosQuery = query(
      collection(db, 'videos'),
      where('courseId', '==', courseId),
      orderBy('order', 'asc')
    );
    
    const snapshot = await getDocs(videosQuery);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.() || new Date()
    }));
  } catch (error) {
    console.error('خطأ في جلب فيديوهات الكورس:', error);
    throw error;
  }
};

/**
 * الاستماع للتحديثات الفورية للكورسات
 */
export const subscribeToCoursesUpdates = (callback) => {
  try {
    const coursesQuery = query(
      collection(db, 'courses'),
      orderBy('createdAt', 'desc')
    );
    
    return onSnapshot(coursesQuery, (snapshot) => {
      const courses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));
      
      callback(courses);
    });
  } catch (error) {
    console.error('خطأ في الاستماع لتحديثات الكورسات:', error);
    throw error;
  }
};

export default {
  createCourse,
  updateCourse,
  deleteCourse,
  toggleCoursePublication,
  getAllCourses,
  getPublishedCourses,
  getCourse,
  addVideoToCourse,
  getCourseVideos,
  subscribeToCoursesUpdates
};
