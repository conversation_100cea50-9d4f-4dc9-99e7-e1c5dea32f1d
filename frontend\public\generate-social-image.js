// سكريبت لإنشاء صورة المشاركة الاجتماعية
function createSocialShareImage() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // تحديد أبعاد الصورة (1200x630 هو الحجم المثالي للمشاركة الاجتماعية)
    canvas.width = 1200;
    canvas.height = 630;
    
    // إنشاء التدرج الخلفي
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, '#0000FF');
    gradient.addColorStop(0.5, '#4169E1');
    gradient.addColorStop(1, '#6366F1');
    
    // رسم الخلفية
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // إضافة دوائر زخرفية
    ctx.fillStyle = 'rgba(255, 215, 0, 0.1)';
    ctx.beginPath();
    ctx.arc(1100, 100, 100, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(100, 530, 75, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(150, 315, 50, 0, 2 * Math.PI);
    ctx.fill();
    
    // رسم دائرة الشعار
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.arc(600, 180, 60, 0, 2 * Math.PI);
    ctx.fill();
    
    // إضافة حدود للشعار
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 4;
    ctx.stroke();
    
    // إعداد النصوص
    ctx.textAlign = 'center';
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;
    
    // العنوان الرئيسي
    const titleGradient = ctx.createLinearGradient(200, 280, 1000, 280);
    titleGradient.addColorStop(0, '#FFD700');
    titleGradient.addColorStop(1, '#FFA500');
    
    ctx.fillStyle = titleGradient;
    ctx.font = 'bold 48px Arial, sans-serif';
    ctx.fillText('SKILLS WORLD ACADEMY', 600, 280);
    
    // العنوان الفرعي بالعربية
    ctx.fillStyle = 'white';
    ctx.font = '600 24px Arial, sans-serif';
    ctx.fillText('منصة التعلم والتطوير المهني العالمية', 600, 320);
    
    // الوصف
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = '400 18px Arial, sans-serif';
    ctx.fillText('تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية', 600, 360);
    
    // إزالة الظل للعناصر التالية
    ctx.shadowColor = 'transparent';
    
    // رسم مربعات الميزات
    const features = [
        { x: 300, text: '📚 كورسات متنوعة' },
        { x: 500, text: '🎓 شهادات معتمدة' },
        { x: 700, text: '👨‍🏫 مدربين خبراء' }
    ];
    
    features.forEach(feature => {
        // رسم المربع
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(feature.x, 420, 180, 40);
        
        // رسم الحدود
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.lineWidth = 1;
        ctx.strokeRect(feature.x, 420, 180, 40);
        
        // النص
        ctx.fillStyle = 'white';
        ctx.font = '500 14px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(feature.text, feature.x + 90, 445);
    });
    
    // رسم مربع الرابط
    ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';
    ctx.fillRect(400, 520, 400, 35);
    
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    ctx.strokeRect(400, 520, 400, 35);
    
    // نص الرابط
    ctx.fillStyle = 'white';
    ctx.font = '500 16px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('marketwise-academy-qhizq.web.app', 600, 542);
    
    return canvas;
}

// تحويل Canvas إلى Blob
function canvasToBlob(canvas) {
    return new Promise(resolve => {
        canvas.toBlob(resolve, 'image/png', 1.0);
    });
}

// تحميل الصورة
async function downloadSocialImage() {
    const canvas = createSocialShareImage();
    const blob = await canvasToBlob(canvas);
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'skills-world-academy-social-share.png';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// تشغيل تلقائي عند تحميل الصفحة
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        setTimeout(downloadSocialImage, 1000);
    });
}
