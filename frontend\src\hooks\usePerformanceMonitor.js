import { useState, useEffect, useRef } from 'react';

/**
 * Hook لمراقبة أداء التحديثات الفورية
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    updateCount: 0,
    averageUpdateTime: 0,
    lastUpdateTime: null,
    connectionQuality: 'good', // good, fair, poor
    dataTransferRate: 0,
    errorCount: 0,
    totalDataReceived: 0
  });

  const updateTimes = useRef([]);
  const startTime = useRef(Date.now());
  const lastDataSize = useRef(0);

  // تسجيل تحديث جديد
  const recordUpdate = (dataSize = 0, updateDuration = 0) => {
    const now = Date.now();
    
    setMetrics(prev => {
      const newUpdateTimes = [...updateTimes.current, updateDuration].slice(-10); // الاحتفاظ بآخر 10 تحديثات
      updateTimes.current = newUpdateTimes;
      
      const averageTime = newUpdateTimes.reduce((sum, time) => sum + time, 0) / newUpdateTimes.length;
      
      return {
        ...prev,
        updateCount: prev.updateCount + 1,
        averageUpdateTime: averageTime,
        lastUpdateTime: now,
        totalDataReceived: prev.totalDataReceived + dataSize,
        dataTransferRate: calculateDataTransferRate(prev.totalDataReceived + dataSize)
      };
    });

    // تحديث جودة الاتصال بناءً على زمن التحديث
    updateConnectionQuality(updateDuration);
  };

  // تسجيل خطأ
  const recordError = () => {
    setMetrics(prev => ({
      ...prev,
      errorCount: prev.errorCount + 1
    }));
  };

  // حساب معدل نقل البيانات (بايت/ثانية)
  const calculateDataTransferRate = (totalData) => {
    const elapsedTime = (Date.now() - startTime.current) / 1000; // بالثواني
    return elapsedTime > 0 ? totalData / elapsedTime : 0;
  };

  // تحديث جودة الاتصال
  const updateConnectionQuality = (updateDuration) => {
    let quality = 'good';
    
    if (updateDuration > 5000) { // أكثر من 5 ثوان
      quality = 'poor';
    } else if (updateDuration > 2000) { // أكثر من ثانيتين
      quality = 'fair';
    }

    setMetrics(prev => ({
      ...prev,
      connectionQuality: quality
    }));
  };

  // إعادة تعيين المقاييس
  const resetMetrics = () => {
    setMetrics({
      updateCount: 0,
      averageUpdateTime: 0,
      lastUpdateTime: null,
      connectionQuality: 'good',
      dataTransferRate: 0,
      errorCount: 0,
      totalDataReceived: 0
    });
    updateTimes.current = [];
    startTime.current = Date.now();
  };

  // حساب الإحصائيات المتقدمة
  const getAdvancedMetrics = () => {
    const now = Date.now();
    const uptime = (now - startTime.current) / 1000; // بالثواني
    const updatesPerMinute = metrics.updateCount > 0 ? (metrics.updateCount / uptime) * 60 : 0;
    const errorRate = metrics.updateCount > 0 ? (metrics.errorCount / metrics.updateCount) * 100 : 0;
    
    return {
      uptime: Math.floor(uptime),
      updatesPerMinute: Math.round(updatesPerMinute * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      dataTransferRateKB: Math.round((metrics.dataTransferRate / 1024) * 100) / 100
    };
  };

  return {
    metrics,
    recordUpdate,
    recordError,
    resetMetrics,
    getAdvancedMetrics
  };
};

/**
 * Hook لمراقبة أداء مكون معين
 */
export const useComponentPerformance = (componentName) => {
  const [renderCount, setRenderCount] = useState(0);
  const [renderTimes, setRenderTimes] = useState([]);
  const renderStartTime = useRef(null);

  // بدء قياس وقت الرندر
  const startRenderMeasurement = () => {
    renderStartTime.current = performance.now();
  };

  // انتهاء قياس وقت الرندر
  const endRenderMeasurement = () => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      
      setRenderCount(prev => prev + 1);
      setRenderTimes(prev => [...prev.slice(-9), renderTime]); // الاحتفاظ بآخر 10 قياسات
      
      // تسجيل في console للتطوير
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }
      
      renderStartTime.current = null;
    }
  };

  // حساب متوسط وقت الرندر
  const getAverageRenderTime = () => {
    if (renderTimes.length === 0) return 0;
    return renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length;
  };

  // تشغيل قياس الرندر تلقائياً
  useEffect(() => {
    startRenderMeasurement();
    return () => {
      endRenderMeasurement();
    };
  });

  return {
    renderCount,
    averageRenderTime: getAverageRenderTime(),
    lastRenderTime: renderTimes[renderTimes.length - 1] || 0
  };
};

/**
 * Hook لمراقبة استخدام الذاكرة
 */
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = useState({
    usedJSHeapSize: 0,
    totalJSHeapSize: 0,
    jsHeapSizeLimit: 0
  });

  useEffect(() => {
    const updateMemoryInfo = () => {
      if (performance.memory) {
        setMemoryInfo({
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        });
      }
    };

    // تحديث كل 5 ثوان
    const interval = setInterval(updateMemoryInfo, 5000);
    updateMemoryInfo(); // تحديث فوري

    return () => clearInterval(interval);
  }, []);

  // تحويل البايتات إلى ميجابايت
  const formatBytes = (bytes) => {
    return Math.round((bytes / 1024 / 1024) * 100) / 100;
  };

  return {
    usedMemoryMB: formatBytes(memoryInfo.usedJSHeapSize),
    totalMemoryMB: formatBytes(memoryInfo.totalJSHeapSize),
    memoryLimitMB: formatBytes(memoryInfo.jsHeapSizeLimit),
    memoryUsagePercentage: memoryInfo.totalJSHeapSize > 0 
      ? Math.round((memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100)
      : 0
  };
};

export default {
  usePerformanceMonitor,
  useComponentPerformance,
  useMemoryMonitor
};
