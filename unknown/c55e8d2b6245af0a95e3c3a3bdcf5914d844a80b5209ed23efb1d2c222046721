# دليل إعداد قاعدة البيانات SQL 🗄️

## خيارات قاعدة البيانات المتاحة

### 1. Google Cloud SQL (موصى به مع Firebase)
- **المميزات:** تكامل مثالي مع Firebase، أداء عالي، نسخ احتياطي تلقائي
- **التكلفة:** حوالي $10-50 شهرياً حسب الاستخدام
- **الإعداد:** سهل ومتكامل

### 2. PlanetScale (MySQL بدون خادم)
- **المميزات:** مجاني للمشاريع الصغيرة، سهل الإعداد
- **التكلفة:** مجاني حتى 1GB، ثم $29 شهرياً
- **الإعداد:** سريع جداً

### 3. Railway (PostgreSQL/MySQL)
- **المميزات:** مجاني للبداية، سهل النشر
- **التكلفة:** مجاني حتى $5 شهرياً استخدام
- **الإعداد:** بسيط

### 4. Supabase (PostgreSQL مع واجهة برمجية)
- **المميزات:** مجاني للمشاريع الصغيرة، واجهة برمجية جاهزة
- **التكلفة:** مجاني حتى 500MB، ثم $25 شهرياً
- **الإعداد:** سريع

## 🚀 الإعداد السريع - PlanetScale (موصى به)

### الخطوة 1: إنشاء حساب
1. اذهب إلى [PlanetScale](https://planetscale.com)
2. أنشئ حساب جديد
3. أنشئ قاعدة بيانات جديدة باسم `skills-world-academy`

### الخطوة 2: تشغيل السكريبت
```sql
-- انسخ والصق محتوى ملف schema.sql في PlanetScale Console
```

### الخطوة 3: الحصول على بيانات الاتصال
```env
# أضف هذه المتغيرات إلى ملف .env
DATABASE_URL=mysql://username:password@host:port/database_name
DB_HOST=your_host
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=skills_world_academy
DB_PORT=3306
```

## 🔧 إعداد Google Cloud SQL

### الخطوة 1: إنشاء قاعدة البيانات
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com)
2. فعّل Cloud SQL API
3. أنشئ instance جديد (MySQL 8.0)
4. اختر المنطقة القريبة منك

### الخطوة 2: إعداد الأمان
```sql
-- إنشاء مستخدم للتطبيق
CREATE USER 'academy_user'@'%' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON skills_world_academy.* TO 'academy_user'@'%';
FLUSH PRIVILEGES;
```

### الخطوة 3: ربط Firebase
1. في Firebase Console، اذهب إلى Extensions
2. ثبّت "Cloud SQL Proxy"
3. أدخل بيانات الاتصال

## 📱 ربط قاعدة البيانات بالتطبيق

### إنشاء خدمة قاعدة البيانات
```javascript
// frontend/src/services/database.js
import mysql from 'mysql2/promise';

const dbConfig = {
  host: process.env.REACT_APP_DB_HOST,
  user: process.env.REACT_APP_DB_USER,
  password: process.env.REACT_APP_DB_PASSWORD,
  database: process.env.REACT_APP_DB_NAME,
  port: process.env.REACT_APP_DB_PORT || 3306,
  ssl: {
    rejectUnauthorized: false
  }
};

export const createConnection = async () => {
  try {
    const connection = await mysql.createConnection(dbConfig);
    return connection;
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
};
```

### تحديث متغيرات البيئة
```env
# أضف إلى ملف .env
REACT_APP_DB_HOST=your_database_host
REACT_APP_DB_USER=academy_user
REACT_APP_DB_PASSWORD=your_password
REACT_APP_DB_NAME=skills_world_academy
REACT_APP_DB_PORT=3306
REACT_APP_USE_SQL=true
```

## 🔄 ترحيل البيانات من Firebase إلى SQL

### سكريبت الترحيل
```javascript
// scripts/migrate-to-sql.js
const admin = require('firebase-admin');
const mysql = require('mysql2/promise');

// إعداد Firebase Admin
const serviceAccount = require('./serviceAccountKey.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

// إعداد MySQL
const sqlConnection = mysql.createConnection({
  host: 'your_host',
  user: 'your_user',
  password: 'your_password',
  database: 'skills_world_academy'
});

async function migrateUsers() {
  const usersSnapshot = await db.collection('users').get();
  
  for (const doc of usersSnapshot.docs) {
    const userData = doc.data();
    
    await sqlConnection.execute(
      `INSERT INTO users (id, name, email, phone, role, student_code, is_active, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        doc.id,
        userData.name,
        userData.email || null,
        userData.phone || null,
        userData.role,
        userData.studentCode || null,
        userData.isActive || true,
        userData.createdAt?.toDate() || new Date()
      ]
    );
  }
  
  console.log('Users migrated successfully');
}

// تشغيل الترحيل
migrateUsers().catch(console.error);
```

## 🌐 النشر مع قاعدة البيانات

### تحديث Firebase Functions
```javascript
// functions/index.js
const functions = require('firebase-functions');
const mysql = require('mysql2/promise');

const dbConfig = {
  host: functions.config().db.host,
  user: functions.config().db.user,
  password: functions.config().db.password,
  database: functions.config().db.name
};

exports.getStudents = functions.https.onRequest(async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    const [rows] = await connection.execute(
      'SELECT * FROM users WHERE role = "student" ORDER BY created_at DESC'
    );
    
    res.json({ success: true, students: rows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  } finally {
    await connection.end();
  }
});
```

### إعداد متغيرات Firebase Functions
```bash
firebase functions:config:set db.host="your_host"
firebase functions:config:set db.user="your_user"
firebase functions:config:set db.password="your_password"
firebase functions:config:set db.name="skills_world_academy"
```

## 📊 مراقبة الأداء

### إعداد المراقبة
```sql
-- إنشاء فهارس للأداء
CREATE INDEX idx_user_role_active ON users(role, is_active);
CREATE INDEX idx_enrollment_student_course ON enrollments(student_id, course_id);
CREATE INDEX idx_video_progress_student ON video_progress(student_id, course_id);
CREATE INDEX idx_activity_user_date ON activity_logs(user_id, created_at);

-- إحصائيات الاستخدام
CREATE VIEW student_stats AS
SELECT 
    u.id,
    u.name,
    u.student_code,
    COUNT(e.id) as enrolled_courses,
    COUNT(c.id) as completed_courses,
    SUM(vp.watch_time_seconds) / 60 as total_watch_minutes
FROM users u
LEFT JOIN enrollments e ON u.id = e.student_id
LEFT JOIN certificates c ON u.id = c.student_id
LEFT JOIN video_progress vp ON u.id = vp.student_id
WHERE u.role = 'student'
GROUP BY u.id;
```

## 🔒 الأمان والنسخ الاحتياطي

### إعداد النسخ الاحتياطي التلقائي
```sql
-- جدولة نسخ احتياطي يومي
CREATE EVENT daily_backup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL backup_database();
```

### حماية البيانات الحساسة
```sql
-- تشفير كلمات المرور
ALTER TABLE users ADD COLUMN password_salt VARCHAR(255);
ALTER TABLE users ADD COLUMN password_hash VARCHAR(255);

-- إخفاء البيانات الحساسة في العروض
CREATE VIEW public_users AS
SELECT id, name, role, student_code, is_active, created_at
FROM users;
```

## 🎯 الخطوات التالية

1. **اختر مزود قاعدة البيانات** (PlanetScale موصى به للبداية)
2. **أنشئ قاعدة البيانات** باستخدام schema.sql
3. **حدّث متغيرات البيئة** في المشروع
4. **اختبر الاتصال** باستخدام سكريبت الاختبار
5. **رحّل البيانات** من Firebase (اختياري)
6. **انشر التحديثات** على Firebase Hosting

هل تريد المساعدة في أي من هذه الخطوات؟
