const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Category = require('../models/Category');
const Course = require('../models/Course');

const initializeDatabase = async () => {
  try {
    console.log('🔧 بدء تهيئة قاعدة البيانات...');
    
    // Connect to database
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/alaa-courses';
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // Check if admin user exists
    const adminExists = await User.findOne({ role: 'admin' });
    
    if (adminExists) {
      console.log('ℹ️ المدير موجود بالفعل، تم تخطي التهيئة');
      return;
    }
    
    console.log('🔧 إنشاء البيانات الافتراضية...');
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('Admin123!', 12);
    const adminUser = new User({
      name: 'علاء عبد الحميد',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isActive: true
    });
    await adminUser.save();
    console.log('👨‍💼 تم إنشاء حساب المدير');
    
    // Create default categories
    const categories = [
      {
        name: 'التسويق الرقمي',
        description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
        icon: 'digital-marketing',
        color: '#2196F3',
        order: 1,
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'وسائل التواصل الاجتماعي',
        description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
        icon: 'social-media',
        color: '#4CAF50',
        order: 2,
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'التسويق بالمحتوى',
        description: 'إنشاء وتسويق المحتوى الفعال',
        icon: 'content-marketing',
        color: '#FF9800',
        order: 3,
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'التجارة الإلكترونية',
        description: 'بناء وإدارة المتاجر الإلكترونية',
        icon: 'ecommerce',
        color: '#9C27B0',
        order: 4,
        isActive: true,
        createdBy: adminUser._id
      }
    ];
    
    const createdCategories = await Category.insertMany(categories);
    console.log('📂 تم إنشاء الأقسام الافتراضية');
    
    // Create sample students
    const sampleStudents = [
      {
        name: 'أحمد محمد علي',
        studentCode: '123456',
        role: 'student',
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'فاطمة علي حسن',
        studentCode: '789012',
        role: 'student',
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'محمد عبدالله',
        studentCode: '345678',
        role: 'student',
        isActive: true,
        createdBy: adminUser._id
      }
    ];
    
    await User.insertMany(sampleStudents);
    console.log('👥 تم إنشاء الطلاب التجريبيين');
    
    // Create sample course
    const sampleCourse = new Course({
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف مع أحدث الاستراتيجيات والأدوات',
      shortDescription: 'كورس شامل في أساسيات التسويق الرقمي',
      category: createdCategories[0]._id,
      instructor: 'علاء عبد الحميد',
      thumbnail: '/uploads/courses/digital-marketing-basics.jpg',
      level: 'مبتدئ',
      language: 'العربية',
      tags: ['تسويق', 'رقمي', 'مبتدئ', 'أساسيات'],
      requirements: [
        'لا توجد متطلبات مسبقة',
        'جهاز كمبيوتر أو هاتف ذكي',
        'اتصال بالإنترنت'
      ],
      whatYouWillLearn: [
        'فهم أساسيات التسويق الرقمي',
        'إنشاء استراتيجية تسويقية فعالة',
        'استخدام وسائل التواصل الاجتماعي للتسويق',
        'تحليل البيانات وقياس النتائج',
        'إنشاء حملات إعلانية ناجحة'
      ],
      lessons: [
        {
          title: 'مقدمة في التسويق الرقمي',
          description: 'تعرف على أساسيات التسويق الرقمي وأهميته',
          duration: 900, // 15 minutes
          order: 1,
          isPreview: true
        },
        {
          title: 'الفرق بين التسويق التقليدي والرقمي',
          description: 'مقارنة بين أساليب التسويق التقليدية والرقمية',
          duration: 765, // 12:45
          order: 2,
          isPreview: false
        }
      ],
      isActive: true,
      createdBy: adminUser._id
    });
    
    await sampleCourse.save();
    console.log('📚 تم إنشاء الكورس التجريبي');
    
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح!');
    console.log('👨‍💼 بيانات المدير: <EMAIL> / Admin123!');
    console.log('👨‍🎓 أكواد الطلاب: 123456, 789012, 345678');
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
  }
};

// Run if called directly
if (require.main === module) {
  initializeDatabase();
}

module.exports = initializeDatabase;
