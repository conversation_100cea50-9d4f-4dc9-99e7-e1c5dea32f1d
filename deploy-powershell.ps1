# SKILLS WORLD ACADEMY - PowerShell Deployment Script
# سكربت نشر محسن لـ PowerShell

Write-Host "🚀 بدء عملية النشر - SKILLS WORLD ACADEMY" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════" -ForegroundColor Cyan

# التحقق من وجود Firebase CLI
Write-Host "🔍 التحقق من Firebase CLI..." -ForegroundColor Yellow
try {
    $firebaseVersion = firebase --version
    Write-Host "✅ Firebase CLI متوفر: $firebaseVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Firebase CLI غير مثبت. يرجى تثبيته أولاً." -ForegroundColor Red
    exit 1
}

# التحقق من المشروع الحالي
Write-Host "🔍 التحقق من مشروع Firebase..." -ForegroundColor Yellow
$currentProject = firebase use --project marketwise-academy-qhizq
Write-Host "✅ المشروع: marketwise-academy-qhizq" -ForegroundColor Green

# الانتقال إلى مجلد Frontend
Write-Host "📁 الانتقال إلى مجلد Frontend..." -ForegroundColor Yellow
Set-Location -Path ".\frontend"

# تثبيت التبعيات
Write-Host "📦 تثبيت التبعيات..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تثبيت التبعيات" -ForegroundColor Red
    Set-Location -Path ".."
    exit 1
}

# بناء المشروع
Write-Host "🔨 بناء المشروع..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
    Set-Location -Path ".."
    exit 1
}

# العودة إلى المجلد الرئيسي
Set-Location -Path ".."

# النشر على Firebase
Write-Host "🔥 النشر على Firebase..." -ForegroundColor Yellow
firebase deploy --only hosting
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في النشر" -ForegroundColor Red
    exit 1
}

Write-Host "═══════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "🎉 تم النشر بنجاح!" -ForegroundColor Green
Write-Host "🌐 الموقع: https://marketwise-academy-qhizq.web.app" -ForegroundColor Cyan
Write-Host "═══════════════════════════════════════════════════════" -ForegroundColor Cyan
