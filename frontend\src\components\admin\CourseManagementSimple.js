import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  People,
  VideoLibrary,
  AccessTime,
  Category
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import { courseManagementService } from '../../firebase/adminServices';

const CourseManagementSimple = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [dialogType, setDialogType] = useState('add');

  const [courseForm, setCourseForm] = useState({
    title: '',
    description: '',
    price: '',
    level: 'مبتدئ',
    category: 'التسويق الرقمي',
    duration: ''
  });

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const coursesData = await courseManagementService.getAllCourses();
      setCourses(coursesData);
    } catch (error) {
      console.error('خطأ في جلب الكورسات:', error);
      toast.error('فشل في جلب الكورسات');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (type, course = null) => {
    setDialogType(type);
    setSelectedCourse(course);
    if (course && type === 'edit') {
      setCourseForm({
        title: course.title,
        description: course.description,
        price: course.price,
        level: course.level,
        category: course.category,
        duration: course.duration
      });
    } else {
      setCourseForm({
        title: '',
        description: '',
        price: '',
        level: 'مبتدئ',
        category: 'التسويق الرقمي',
        duration: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCourse(null);
  };

  const handleSaveCourse = async () => {
    try {
      if (dialogType === 'add') {
        await courseManagementService.createCourse(courseForm);
        const updatedCourses = await courseManagementService.getAllCourses();
        setCourses(updatedCourses);
        toast.success('تم إضافة الكورس بنجاح');
      } else if (dialogType === 'edit') {
        await courseManagementService.updateCourse(selectedCourse.id, courseForm);
        const updatedCourses = await courseManagementService.getAllCourses();
        setCourses(updatedCourses);
        toast.success('تم تحديث الكورس بنجاح');
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الكورس:', error);
      toast.error('فشل في حفظ الكورس');
    }
  };

  const handleDeleteCourse = async (courseId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الكورس؟')) {
      try {
        await courseManagementService.deleteCourse(courseId);
        setCourses(courses.filter(c => c.id !== courseId));
        toast.success('تم حذف الكورس بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الكورس:', error);
        toast.error('فشل في حذف الكورس');
      }
    }
  };

  const CourseCard = ({ course }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ position: 'relative' }}>
        <Box
          sx={{
            height: 200,
            backgroundImage: `url(${course.thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundColor: '#f5f5f5'
          }}
        />
        <Chip
          label={course.isActive ? 'نشط' : 'غير نشط'}
          color={course.isActive ? 'success' : 'default'}
          size="small"
          sx={{ position: 'absolute', top: 8, right: 8 }}
        />
      </Box>
      
      <CardContent sx={{ flexGrow: 1, p: 2 }}>
        <Typography variant="h6" gutterBottom noWrap>
          {course.title}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, height: 40, overflow: 'hidden' }}>
          {course.description}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Rating value={course.rating || 4.5} readOnly size="small" />
          <Typography variant="body2" sx={{ ml: 1 }}>
            ({course.reviewsCount || 0})
          </Typography>
        </Box>

        <Grid container spacing={1} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <People fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {course.enrolledStudents?.length || 0}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <VideoLibrary fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {course.totalVideos || 0}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AccessTime fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {course.duration}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Category fontSize="small" color="action" />
              <Typography variant="body2" sx={{ ml: 0.5 }} noWrap>
                {course.level}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" color="primary">
            {course.price} ريال
          </Typography>
        </Box>
      </CardContent>

      <Box sx={{ p: 2, pt: 0 }}>
        <Grid container spacing={1}>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Visibility />}
              onClick={() => handleOpenDialog('view', course)}
            >
              عرض
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Edit />}
              onClick={() => handleOpenDialog('edit', course)}
            >
              تعديل
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              color="error"
              startIcon={<Delete />}
              onClick={() => handleDeleteCourse(course._id)}
            >
              حذف
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>جاري تحميل الكورسات...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">إدارة الكورسات</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog('add')}
        >
          إضافة كورس جديد
        </Button>
      </Box>

      <Grid container spacing={3}>
        {courses.map((course) => (
          <Grid item xs={12} sm={6} md={4} key={course._id}>
            <CourseCard course={course} />
          </Grid>
        ))}
      </Grid>

      {/* Dialog for Add/Edit/View Course */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'add' && 'إضافة كورس جديد'}
          {dialogType === 'edit' && 'تعديل الكورس'}
          {dialogType === 'view' && 'تفاصيل الكورس'}
        </DialogTitle>
        
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="عنوان الكورس"
                value={courseForm.title}
                onChange={(e) => setCourseForm({...courseForm, title: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="وصف الكورس"
                value={courseForm.description}
                onChange={(e) => setCourseForm({...courseForm, description: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="السعر"
                type="number"
                value={courseForm.price}
                onChange={(e) => setCourseForm({...courseForm, price: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="المدة"
                value={courseForm.duration}
                onChange={(e) => setCourseForm({...courseForm, duration: e.target.value})}
                disabled={dialogType === 'view'}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth disabled={dialogType === 'view'}>
                <InputLabel>المستوى</InputLabel>
                <Select
                  value={courseForm.level}
                  label="المستوى"
                  onChange={(e) => setCourseForm({...courseForm, level: e.target.value})}
                >
                  <MenuItem value="مبتدئ">مبتدئ</MenuItem>
                  <MenuItem value="متوسط">متوسط</MenuItem>
                  <MenuItem value="متقدم">متقدم</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth disabled={dialogType === 'view'}>
                <InputLabel>الفئة</InputLabel>
                <Select
                  value={courseForm.category}
                  label="الفئة"
                  onChange={(e) => setCourseForm({...courseForm, category: e.target.value})}
                >
                  <MenuItem value="التسويق الرقمي">التسويق الرقمي</MenuItem>
                  <MenuItem value="وسائل التواصل الاجتماعي">وسائل التواصل الاجتماعي</MenuItem>
                  <MenuItem value="التسويق الإلكتروني">التسويق الإلكتروني</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          {dialogType !== 'view' && (
            <Button variant="contained" onClick={handleSaveCourse}>
              {dialogType === 'add' ? 'إضافة' : 'حفظ'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseManagementSimple;
