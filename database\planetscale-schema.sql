-- قاعدة بيانات منصة كورسات علاء عبد الحميد - PlanetScale
-- Skills World Academy Database Schema for PlanetScale

-- جدول المستخدمين (المدراء والطلاب)
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('admin', 'student') NOT NULL,
    student_code VARCHAR(6),
    password_hash VARCHAR(255),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_email (email),
    UNIQUE KEY unique_student_code (student_code),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- جدول الكورسات
CREATE TABLE courses (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    level ENUM('مبتدئ', 'متوسط', 'متقدم') DEFAULT 'مبتدئ',
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    enrolled_students INT DEFAULT 0,
    total_videos INT DEFAULT 0,
    total_duration_minutes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_instructor (instructor)
);

-- جدول فيديوهات الكورسات
CREATE TABLE course_videos (
    id VARCHAR(36) PRIMARY KEY,
    course_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT,
    duration_minutes INT DEFAULT 0,
    video_order INT NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_course_order (course_id, video_order),
    INDEX idx_course (course_id)
);

-- جدول تسجيل الطلاب في الكورسات
CREATE TABLE enrollments (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    completed_videos JSON,
    last_watched_video_id VARCHAR(36),
    total_watch_time_minutes INT DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_completed (is_completed)
);

-- جدول تقدم مشاهدة الفيديوهات
CREATE TABLE video_progress (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    video_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    watch_time_seconds INT DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    last_position_seconds INT DEFAULT 0,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_progress (student_id, video_id),
    INDEX idx_student_course (student_id, course_id),
    INDEX idx_video (video_id)
);

-- جدول الشهادات
CREATE TABLE certificates (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    certificate_number VARCHAR(50) NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    certificate_url TEXT,
    is_valid BOOLEAN DEFAULT TRUE,
    
    UNIQUE KEY unique_certificate (student_id, course_id),
    UNIQUE KEY unique_cert_number (certificate_number),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id)
);

-- جدول الأسئلة الشائعة
CREATE TABLE faqs (
    id VARCHAR(36) PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_active (is_active),
    INDEX idx_order (display_order)
);

-- جدول الدردشة والدعم
CREATE TABLE chat_messages (
    id VARCHAR(36) PRIMARY KEY,
    sender_id VARCHAR(36) NOT NULL,
    receiver_id VARCHAR(36),
    message TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file') DEFAULT 'text',
    file_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_unread (is_read, receiver_id)
);

-- جدول الأنشطة والسجلات
CREATE TABLE activity_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    metadata JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- جدول الإعدادات العامة
CREATE TABLE settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_setting_key (setting_key),
    INDEX idx_public (is_public)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user (user_id),
    INDEX idx_unread (is_read, user_id),
    INDEX idx_created_at (created_at)
);

-- إدراج البيانات الأساسية
INSERT INTO settings (id, setting_key, setting_value, setting_type, description, is_public) VALUES
('set-001', 'academy_name', 'SKILLS WORLD ACADEMY', 'string', 'اسم الأكاديمية', TRUE),
('set-002', 'admin_name', 'علاء عبد الحميد', 'string', 'اسم المدير', TRUE),
('set-003', 'admin_email', 'ALAA <EMAIL>', 'string', 'بريد المدير', FALSE),
('set-004', 'admin_phone', '0506747770', 'string', 'هاتف المدير', FALSE),
('set-005', 'academy_version', '1.0.0', 'string', 'إصدار النظام', TRUE),
('set-006', 'max_students_per_course', '1000', 'number', 'الحد الأقصى للطلاب في الكورس', FALSE);

-- إنشاء المدير الافتراضي
INSERT INTO users (id, name, email, phone, role, is_active, created_at, updated_at) VALUES
('admin-001', 'علاء عبد الحميد', '<EMAIL>', '0506747770', 'admin', TRUE, NOW(), NOW());

-- إنشاء طلاب تجريبيين
INSERT INTO users (id, name, email, phone, role, student_code, is_active, created_at, updated_at) VALUES
('student-001', 'أحمد محمد علي', '<EMAIL>', '0501234567', 'student', '123456', TRUE, NOW(), NOW()),
('student-002', 'فاطمة أحمد حسن', '<EMAIL>', '0507654321', 'student', '654321', TRUE, NOW(), NOW()),
('student-003', 'محمد عبد الله', '<EMAIL>', '0509876543', 'student', '111111', TRUE, NOW(), NOW());

-- إنشاء أسئلة شائعة أساسية
INSERT INTO faqs (id, question, answer, category, display_order, is_active, created_at, updated_at) VALUES
('faq-001', 'كيف يمكنني التسجيل في الكورسات؟', 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير', 'التسجيل', 1, TRUE, NOW(), NOW()),
('faq-002', 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟', 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات', 'المشاهدة', 2, TRUE, NOW(), NOW()),
('faq-003', 'كيف أحصل على شهادة إتمام الكورس؟', 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس', 'الشهادات', 3, TRUE, NOW(), NOW()),
('faq-004', 'ماذا أفعل إذا نسيت كود الطالب؟', 'تواصل مع المدير للحصول على كود الطالب الخاص بك', 'الدعم', 4, TRUE, NOW(), NOW()),
('faq-005', 'هل يمكنني تحميل الفيديوهات؟', 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط', 'المشاهدة', 5, TRUE, NOW(), NOW());

-- إنشاء كورس تجريبي
INSERT INTO courses (id, title, description, instructor, duration, level, is_active, total_videos, created_at, updated_at) VALUES
('course-001', 'مقدمة في التسويق الرقمي', 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين', 'علاء عبد الحميد', '4 ساعات', 'مبتدئ', TRUE, 3, NOW(), NOW());

-- إدراج فيديوهات الكورس التجريبي
INSERT INTO course_videos (id, course_id, title, description, duration_minutes, video_order, is_free, created_at, updated_at) VALUES
('video-001', 'course-001', 'مقدمة في التسويق الرقمي', 'نظرة عامة على التسويق الرقمي وأهميته', 15, 1, TRUE, NOW(), NOW()),
('video-002', 'course-001', 'استراتيجيات التسويق', 'تعلم أهم استراتيجيات التسويق الرقمي', 23, 2, FALSE, NOW(), NOW()),
('video-003', 'course-001', 'قياس النتائج', 'كيفية قياس نجاح حملاتك التسويقية', 18, 3, FALSE, NOW(), NOW());
