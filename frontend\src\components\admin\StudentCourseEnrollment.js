import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Alert,
  CircularProgress,
  Autocomplete
} from '@mui/material';
import {
  PersonAdd,
  School,
  Search,
  CheckCircle,
  Cancel,
  Refresh
} from '@mui/icons-material';
import { db } from '../../firebase/config';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import realtimeService from '../../firebase/realtimeService';
import toast from 'react-hot-toast';

const StudentCourseEnrollment = () => {
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [enrollments, setEnrollments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchStudents(),
        fetchCourses(),
        fetchEnrollments()
      ]);
    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
      toast.error('فشل في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const fetchStudents = async () => {
    try {
      const studentsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'student'),
        orderBy('name', 'asc')
      );
      
      const snapshot = await getDocs(studentsQuery);
      const studentsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setStudents(studentsData);
      console.log('👥 تم جلب الطلاب:', studentsData.length);
    } catch (error) {
      console.error('❌ خطأ في جلب الطلاب:', error);
    }
  };

  const fetchCourses = async () => {
    try {
      const coursesQuery = query(
        collection(db, 'courses'),
        where('isActive', '==', true),
        orderBy('title', 'asc')
      );
      
      const snapshot = await getDocs(coursesQuery);
      const coursesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setCourses(coursesData);
      console.log('📚 تم جلب الكورسات:', coursesData.length);
    } catch (error) {
      console.error('❌ خطأ في جلب الكورسات:', error);
    }
  };

  const fetchEnrollments = async () => {
    try {
      const enrollmentsQuery = query(
        collection(db, 'enrollments'),
        where('status', '==', 'active'),
        orderBy('enrolledAt', 'desc')
      );
      
      const snapshot = await getDocs(enrollmentsQuery);
      const enrollmentsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setEnrollments(enrollmentsData);
      console.log('📝 تم جلب التسجيلات:', enrollmentsData.length);
    } catch (error) {
      console.error('❌ خطأ في جلب التسجيلات:', error);
    }
  };

  const handleEnrollStudent = async () => {
    if (!selectedStudent || !selectedCourse) {
      toast.error('يرجى اختيار الطالب والكورس');
      return;
    }

    // التحقق من التسجيل المسبق
    const existingEnrollment = enrollments.find(
      e => e.studentId === selectedStudent.id && e.courseId === selectedCourse.id
    );

    if (existingEnrollment) {
      toast.error('الطالب مسجل بالفعل في هذا الكورس');
      return;
    }

    try {
      setLoading(true);
      
      // تسجيل الطالب مع تحديث فوري
      const result = await realtimeService.enrollStudentInCourse(
        selectedStudent.id,
        selectedCourse.id,
        {
          enrolledBy: 'admin',
          enrollmentType: 'manual'
        }
      );

      if (result.success) {
        toast.success(`تم تسجيل ${selectedStudent.name} في ${selectedCourse.title} بنجاح - سيظهر فوراً في لوحة الطالب`);
        
        // تحديث قائمة التسجيلات
        await fetchEnrollments();
        
        // إغلاق النافذة
        setOpenDialog(false);
        setSelectedStudent(null);
        setSelectedCourse(null);
      }
    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      toast.error('فشل في تسجيل الطالب');
    } finally {
      setLoading(false);
    }
  };

  const getStudentName = (studentId) => {
    const student = students.find(s => s.id === studentId);
    return student ? student.name : 'غير معروف';
  };

  const getCourseName = (courseId) => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.title : 'غير معروف';
  };

  const isStudentEnrolled = (studentId, courseId) => {
    return enrollments.some(e => e.studentId === studentId && e.courseId === courseId);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <PersonAdd color="primary" />
          تسجيل الطلاب في الكورسات
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchData}
            disabled={loading}
          >
            تحديث البيانات
          </Button>
          
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={() => setOpenDialog(true)}
            disabled={loading}
          >
            تسجيل طالب جديد
          </Button>
        </Box>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {students.length}
              </Typography>
              <Typography variant="body2">إجمالي الطلاب</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {courses.length}
              </Typography>
              <Typography variant="body2">الكورسات المتاحة</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {enrollments.length}
              </Typography>
              <Typography variant="body2">إجمالي التسجيلات</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {new Set(enrollments.map(e => e.studentId)).size}
              </Typography>
              <Typography variant="body2">الطلاب النشطون</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* جدول التسجيلات */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            التسجيلات الحالية
          </Typography>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>الطالب</TableCell>
                    <TableCell>الكورس</TableCell>
                    <TableCell>تاريخ التسجيل</TableCell>
                    <TableCell>التقدم</TableCell>
                    <TableCell>الحالة</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {enrollments.slice(0, 10).map((enrollment) => (
                    <TableRow key={enrollment.id}>
                      <TableCell>{getStudentName(enrollment.studentId)}</TableCell>
                      <TableCell>{getCourseName(enrollment.courseId)}</TableCell>
                      <TableCell>
                        {enrollment.enrolledAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={`${enrollment.progress || 0}%`}
                          color={enrollment.progress > 50 ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={enrollment.status === 'active' ? 'نشط' : 'غير نشط'}
                          color={enrollment.status === 'active' ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* نافذة تسجيل طالب جديد */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <PersonAdd color="primary" />
            تسجيل طالب في كورس
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
            <Autocomplete
              options={students}
              getOptionLabel={(option) => `${option.name} (${option.studentCode})`}
              value={selectedStudent}
              onChange={(event, newValue) => setSelectedStudent(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="اختر الطالب"
                  variant="outlined"
                  fullWidth
                />
              )}
            />
            
            <Autocomplete
              options={courses}
              getOptionLabel={(option) => option.title}
              value={selectedCourse}
              onChange={(event, newValue) => setSelectedCourse(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="اختر الكورس"
                  variant="outlined"
                  fullWidth
                />
              )}
            />
            
            {selectedStudent && selectedCourse && isStudentEnrolled(selectedStudent.id, selectedCourse.id) && (
              <Alert severity="warning">
                هذا الطالب مسجل بالفعل في هذا الكورس
              </Alert>
            )}
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} startIcon={<Cancel />}>
            إلغاء
          </Button>
          <Button
            onClick={handleEnrollStudent}
            variant="contained"
            startIcon={<CheckCircle />}
            disabled={loading || !selectedStudent || !selectedCourse || 
                     (selectedStudent && selectedCourse && isStudentEnrolled(selectedStudent.id, selectedCourse.id))}
          >
            {loading ? <CircularProgress size={20} /> : 'تسجيل الطالب'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentCourseEnrollment;
