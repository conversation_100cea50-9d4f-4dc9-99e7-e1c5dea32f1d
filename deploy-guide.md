# دليل النشر - منصة كورسات علاء عبد الحميد

## 🚀 النشر على Render.com

### 1. إعداد قاعدة البيانات MongoDB Atlas

1. اذهب إلى [MongoDB Atlas](https://cloud.mongodb.com)
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ Cluster جديد (اختر المجاني)
4. أنشئ Database User:
   - Username: `admin`
   - Password: `كلمة مرور قوية`
5. أضف IP Address: `0.0.0.0/0` (للسماح لجميع الاتصالات)
6. احصل على Connection String:
   ```
   mongodb+srv://admin:<password>@cluster0.xxxxx.mongodb.net/alaa-courses?retryWrites=true&w=majority
   ```

### 2. النشر على Render

1. اذهب إلى [Render.com](https://render.com)
2. أنشئ حساب جديد أو سجل دخول
3. اضغط "New" → "Web Service"
4. اربط مستودع GitHub الخاص بك
5. اختر المستودع
6. املأ الإعدادات:
   - **Name**: `alaa-courses`
   - **Environment**: `Node`
   - **Build Command**: `npm install`
   - **Start Command**: `node production-server.js`

### 3. متغيرات البيئة في Render

أضف المتغيرات التالية في قسم Environment Variables:

```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://admin:<EMAIL>/alaa-courses?retryWrites=true&w=majority
JWT_SECRET=your-super-secure-jwt-secret-here
PORT=5000
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=Admin123!
```

### 4. نشر الواجهة الأمامية

1. أنشئ خدمة جديدة في Render
2. اختر "Static Site"
3. اربط نفس المستودع
4. املأ الإعدادات:
   - **Build Command**: `cd frontend && npm install && npm run build`
   - **Publish Directory**: `frontend/build`

## 🚀 النشر على Heroku

### 1. تثبيت Heroku CLI

```bash
# Windows
# تحميل من https://devcenter.heroku.com/articles/heroku-cli

# macOS
brew tap heroku/brew && brew install heroku

# Ubuntu
sudo snap install --classic heroku
```

### 2. إعداد المشروع

```bash
# تسجيل الدخول
heroku login

# إنشاء تطبيق
heroku create alaa-courses

# إضافة MongoDB Atlas
heroku config:set MONGODB_URI="mongodb+srv://admin:<EMAIL>/alaa-courses"

# إضافة متغيرات البيئة
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-super-secure-jwt-secret
heroku config:set DEFAULT_ADMIN_EMAIL=<EMAIL>
heroku config:set DEFAULT_ADMIN_PASSWORD=Admin123!

# نشر التطبيق
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

## 🚀 النشر على DigitalOcean App Platform

### 1. إعداد المشروع

1. اذهب إلى [DigitalOcean](https://cloud.digitalocean.com)
2. اختر "App Platform"
3. أنشئ تطبيق جديد
4. اربط مستودع GitHub

### 2. إعداد الخدمة

```yaml
name: alaa-courses
services:
- name: backend
  source_dir: /
  github:
    repo: your-username/alaa-courses
    branch: main
  run_command: node production-server.js
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: MONGODB_URI
    value: mongodb+srv://admin:<EMAIL>/alaa-courses
  - key: JWT_SECRET
    value: your-super-secure-jwt-secret
```

## 🚀 النشر باستخدام Docker

### 1. بناء الصورة

```bash
# بناء صورة الإنتاج
docker build -t alaa-courses .

# تشغيل الحاوية
docker run -p 5000:5000 \
  -e NODE_ENV=production \
  -e MONGODB_URI="mongodb+srv://admin:<EMAIL>/alaa-courses" \
  -e JWT_SECRET="your-super-secure-jwt-secret" \
  alaa-courses
```

### 2. استخدام Docker Compose

```bash
# للإنتاج
docker-compose -f docker-compose.prod.yml up -d

# مع متغيرات البيئة
cp .env.production .env
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 اختبار النشر

بعد النشر، تأكد من:

1. **صحة الخادم**: `https://your-app.com/api/health`
2. **اختبار API**: `https://your-app.com/api/test`
3. **تسجيل دخول المدير**: 
   - البريد: `<EMAIL>`
   - كلمة المرور: `Admin123!`
4. **اختبار كود طالب**: `123456` أو `789012`

## 🛡️ أمان الإنتاج

### متغيرات البيئة المهمة:

```env
# أمان قوي
JWT_SECRET=generate-a-very-strong-random-secret-key-here
BCRYPT_ROUNDS=12

# قاعدة بيانات آمنة
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# CORS محدود
CORS_ORIGIN=https://your-domain.com

# تسجيل محدود
LOG_LEVEL=warn
DEBUG=false
```

## 📊 مراقبة الأداء

1. **Logs**: تحقق من سجلات الخادم
2. **Health Check**: `/api/health`
3. **Database**: مراقبة MongoDB Atlas
4. **Performance**: استخدم أدوات مراقبة مثل New Relic

## 🔄 التحديثات

```bash
# تحديث الكود
git add .
git commit -m "Update: description"
git push origin main

# Render سيقوم بالنشر تلقائياً
# Heroku: git push heroku main
```

## 📞 الدعم

في حالة وجود مشاكل:

1. تحقق من السجلات (Logs)
2. تأكد من متغيرات البيئة
3. اختبر الاتصال بقاعدة البيانات
4. تحقق من صحة الكود

---

**🎉 تهانينا! المشروع جاهز للنشر والاستخدام**
