#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 نشر المشروع على Firebase...');

// Check if Firebase CLI is installed
try {
  execSync('firebase --version', { stdio: 'ignore' });
  console.log('✅ Firebase CLI متوفر');
} catch (error) {
  console.log('📦 تثبيت Firebase CLI...');
  execSync('npm install -g firebase-tools', { stdio: 'inherit' });
}

// Build frontend
console.log('🔨 بناء الواجهة الأمامية...');
try {
  execSync('cd frontend && npm install && npm run build', { stdio: 'inherit' });
  console.log('✅ تم بناء الواجهة الأمامية بنجاح');
} catch (error) {
  console.error('❌ خطأ في بناء الواجهة الأمامية:', error.message);
  process.exit(1);
}

// Install functions dependencies
console.log('📦 تثبيت dependencies للـ Functions...');
try {
  execSync('cd functions && npm install', { stdio: 'inherit' });
  console.log('✅ تم تثبيت dependencies بنجاح');
} catch (error) {
  console.error('❌ خطأ في تثبيت dependencies:', error.message);
  process.exit(1);
}

// Create .firebaserc if it doesn't exist
const firebaserc = {
  "projects": {
    "default": "alaa-courses-platform"
  }
};

if (!fs.existsSync('.firebaserc')) {
  fs.writeFileSync('.firebaserc', JSON.stringify(firebaserc, null, 2));
  console.log('✅ تم إنشاء ملف .firebaserc');
}

// Update firebase.json for better configuration
const firebaseConfig = {
  "hosting": {
    "public": "frontend/build",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "/api/**",
        "function": "api"
      },
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/api/**",
        "headers": [
          {
            "key": "Access-Control-Allow-Origin",
            "value": "*"
          },
          {
            "key": "Access-Control-Allow-Methods",
            "value": "GET, POST, PUT, DELETE, OPTIONS"
          },
          {
            "key": "Access-Control-Allow-Headers",
            "value": "Content-Type, Authorization"
          }
        ]
      },
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      }
    ]
  },
  "functions": {
    "source": "functions",
    "runtime": "nodejs18",
    "predeploy": [
      "npm --prefix \"$RESOURCE_DIR\" run build"
    ]
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  }
};

fs.writeFileSync('firebase.json', JSON.stringify(firebaseConfig, null, 2));
console.log('✅ تم تحديث firebase.json');

// Create Firestore rules
const firestoreRules = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // Categories collection
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Courses collection
    match /courses/{courseId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Progress collection
    match /progress/{progressId} {
      allow read, write: if request.auth != null;
    }
    
    // Certificates collection
    match /certificates/{certificateId} {
      allow read, write: if request.auth != null;
    }
  }
}`;

fs.writeFileSync('firestore.rules', firestoreRules);
console.log('✅ تم إنشاء قواعد Firestore');

// Create Firestore indexes
const firestoreIndexes = {
  "indexes": [
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "role",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "courses",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
};

fs.writeFileSync('firestore.indexes.json', JSON.stringify(firestoreIndexes, null, 2));
console.log('✅ تم إنشاء فهارس Firestore');

console.log('');
console.log('🎯 المشروع جاهز للنشر على Firebase!');
console.log('');
console.log('📋 الخطوات التالية:');
console.log('1. اذهب إلى https://console.firebase.google.com');
console.log('2. أنشئ مشروع جديد باسم: alaa-courses-platform');
console.log('3. فعل Firestore Database');
console.log('4. فعل Authentication');
console.log('5. شغل الأمر: firebase login');
console.log('6. شغل الأمر: firebase use --add');
console.log('7. اختر المشروع الذي أنشأته');
console.log('8. شغل الأمر: firebase deploy');
console.log('');
console.log('🔗 بعد النشر ستحصل على:');
console.log('   📱 الموقع: https://alaa-courses-platform.web.app');
console.log('   🔍 API: https://alaa-courses-platform.web.app/api/health');
console.log('');
console.log('👨‍💼 بيانات المدير الافتراضية:');
console.log('   البريد: <EMAIL>');
console.log('   كلمة المرور: Admin123!');
console.log('');
console.log('👨‍🎓 أكواد الطلاب التجريبية:');
console.log('   123456, 789012');
console.log('');
console.log('🎉 حظاً موفقاً في النشر!');
