#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية النشر السريع...\n');

// التحقق من وجود Git
try {
  execSync('git --version', { stdio: 'ignore' });
} catch (error) {
  console.error('❌ Git غير مثبت. يرجى تثبيت Git أولاً.');
  process.exit(1);
}

// إنشاء .gitignore إذا لم يكن موجوداً
const gitignoreContent = `
# Dependencies
node_modules/
frontend/node_modules/

# Production builds
frontend/build/
dist/

# Environment variables
.env
.env.local
.env.production
.env.development

# Logs
logs/
*.log
npm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Uploads
uploads/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Database
data/
`;

if (!fs.existsSync('.gitignore')) {
  fs.writeFileSync('.gitignore', gitignoreContent.trim());
  console.log('✅ تم إنشاء ملف .gitignore');
}

// التحقق من وجود مستودع Git
try {
  execSync('git status', { stdio: 'ignore' });
} catch (error) {
  console.log('📁 تهيئة مستودع Git...');
  execSync('git init');
  execSync('git add .');
  execSync('git commit -m "Initial commit - منصة كورسات علاء عبد الحميد"');
  console.log('✅ تم تهيئة مستودع Git');
}

console.log('\n📋 خطوات النشر:');
console.log('1. أنشئ حساب على MongoDB Atlas: https://cloud.mongodb.com');
console.log('2. أنشئ Cluster مجاني');
console.log('3. احصل على Connection String');
console.log('4. أنشئ حساب على Render: https://render.com');
console.log('5. اربط مع GitHub repository');
console.log('6. أضف متغيرات البيئة في Render Dashboard');

console.log('\n🔧 متغيرات البيئة المطلوبة:');
console.log('MONGODB_URI=mongodb+srv://username:<EMAIL>/alaa-courses');
console.log('JWT_SECRET=your-super-secret-key');
console.log('NODE_ENV=production');
console.log('FRONTEND_URL=https://your-app.onrender.com');

console.log('\n🌐 رفع الكود على GitHub:');
console.log('git remote add origin https://github.com/username/alaa-courses.git');
console.log('git push -u origin main');

console.log('\n✨ بعد النشر، ستكون المنصة متاحة على:');
console.log('https://your-app-name.onrender.com');

console.log('\n👨‍💼 بيانات المدير الافتراضية:');
console.log('البريد: <EMAIL>');
console.log('كلمة المرور: Admin123!');

console.log('\n👨‍🎓 أكواد الطلاب التجريبية:');
console.log('123456, 789012');

console.log('\n🎉 النشر جاهز! اتبع الخطوات أعلاه لإكمال العملية.');
