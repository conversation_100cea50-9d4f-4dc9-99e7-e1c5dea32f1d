import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import AdminDashboard from '../components/AdminDashboard';
import { AuthProvider } from '../contexts/AuthContext';
import { LanguageProvider } from '../contexts/LanguageContext';

// Mock للمكونات الفرعية
jest.mock('../components/admin/DashboardOverview', () => {
  return function MockDashboardOverview() {
    return <div data-testid="dashboard-overview">Dashboard Overview</div>;
  };
});

jest.mock('../components/admin/CourseManagement', () => {
  return function MockCourseManagement() {
    return <div data-testid="course-management">Course Management</div>;
  };
});

jest.mock('../components/admin/StudentManagement', () => {
  return function MockStudentManagement() {
    return <div data-testid="student-management">Student Management</div>;
  };
});

// Mock للخدمات
jest.mock('../services/hybridCourses', () => ({
  watchCourses: jest.fn(() => () => {}),
}));

jest.mock('../services/hybridStudents', () => ({
  watchStudents: jest.fn(() => () => {}),
}));

jest.mock('../services/hybridEnrollments', () => ({
  watchEnrollments: jest.fn(() => () => {}),
}));

jest.mock('../services/hybridFAQs', () => ({
  watchFAQs: jest.fn(() => () => {}),
}));

const theme = createTheme();

const mockUser = {
  uid: 'test-admin-id',
  email: '<EMAIL>',
  displayName: 'Test Admin'
};

const MockAuthProvider = ({ children }) => {
  const authValue = {
    user: mockUser,
    logout: jest.fn(),
    loading: false
  };
  
  return (
    <AuthProvider value={authValue}>
      {children}
    </AuthProvider>
  );
};

const MockLanguageProvider = ({ children }) => {
  const languageValue = {
    language: 'ar',
    t: (key) => key,
    isRTL: true,
    changeLanguage: jest.fn()
  };
  
  return (
    <LanguageProvider value={languageValue}>
      {children}
    </LanguageProvider>
  );
};

const renderAdminDashboard = (screenWidth = 1024) => {
  // محاكاة عرض الشاشة
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: screenWidth,
  });

  // محاكاة media queries
  window.matchMedia = jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  }));

  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <MockAuthProvider>
          <MockLanguageProvider>
            <AdminDashboard />
          </MockLanguageProvider>
        </MockAuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Admin Dashboard Tablet Responsiveness', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('displays fixed sidebar on tablet devices (768px-1199px)', () => {
    renderAdminDashboard(1024); // عرض جهاز لوحي
    
    // التحقق من وجود القائمة الجانبية الثابتة
    const sidebar = screen.getByText('SKILLS WORLD ACADEMY');
    expect(sidebar).toBeInTheDocument();
    
    // التحقق من عدم وجود زر القائمة على الأجهزة اللوحية
    const menuButton = screen.queryByLabelText(/open drawer/i);
    expect(menuButton).not.toBeInTheDocument();
  });

  test('displays temporary drawer on mobile devices (<768px)', () => {
    renderAdminDashboard(600); // عرض جهاز محمول
    
    // التحقق من وجود زر القائمة على الأجهزة المحمولة
    const menuButton = screen.getByLabelText(/open drawer/i);
    expect(menuButton).toBeInTheDocument();
  });

  test('content area adjusts properly for tablet layout', () => {
    renderAdminDashboard(1024);
    
    // التحقق من أن المحتوى الرئيسي يتكيف مع وجود القائمة الجانبية
    const mainContent = screen.getByRole('main');
    expect(mainContent).toBeInTheDocument();
    
    // التحقق من وجود المحتوى
    expect(screen.getByTestId('dashboard-overview')).toBeInTheDocument();
  });

  test('navigation works correctly on tablet devices', async () => {
    renderAdminDashboard(1024);
    
    // النقر على قسم إدارة الكورسات
    const coursesMenuItem = screen.getByText('الكورسات والفيديوهات');
    fireEvent.click(coursesMenuItem);
    
    await waitFor(() => {
      expect(screen.getByTestId('course-management')).toBeInTheDocument();
    });
  });

  test('applies tablet-specific CSS classes', () => {
    renderAdminDashboard(1024);
    
    // التحقق من تطبيق الفئات المناسبة للأجهزة اللوحية
    const container = document.querySelector('.admin-dashboard-tablet');
    expect(container).toBeInTheDocument();
    
    const mobileOptimized = document.querySelector('.mobile-optimized');
    expect(mobileOptimized).toBeInTheDocument();
  });

  test('touch interactions work properly on tablet', () => {
    renderAdminDashboard(1024);
    
    // محاكاة لمسة على عنصر في القائمة
    const menuItem = screen.getByText('إدارة الطلاب');
    
    // محاكاة أحداث اللمس
    fireEvent.touchStart(menuItem);
    fireEvent.touchEnd(menuItem);
    fireEvent.click(menuItem);
    
    // التحقق من أن التفاعل يعمل
    expect(menuItem).toBeInTheDocument();
  });

  test('responsive breakpoints work correctly', () => {
    // اختبار نقاط الكسر المختلفة
    const breakpoints = [
      { width: 600, description: 'mobile' },
      { width: 768, description: 'tablet start' },
      { width: 1024, description: 'tablet' },
      { width: 1200, description: 'desktop' }
    ];

    breakpoints.forEach(({ width, description }) => {
      renderAdminDashboard(width);
      
      // التحقق من أن المكون يتم عرضه بدون أخطاء
      expect(screen.getByText('SKILLS WORLD ACADEMY')).toBeInTheDocument();
      
      console.log(`✅ ${description} (${width}px) - يعمل بشكل صحيح`);
    });
  });

  test('sidebar positioning is correct for RTL layout', () => {
    renderAdminDashboard(1024);
    
    // التحقق من أن القائمة الجانبية في الموضع الصحيح للغة العربية
    const sidebar = screen.getByText('SKILLS WORLD ACADEMY').closest('[role="navigation"]');
    expect(sidebar).toBeInTheDocument();
  });

  test('content does not overlap with sidebar on tablet', () => {
    renderAdminDashboard(1024);
    
    // التحقق من أن المحتوى لا يتداخل مع القائمة الجانبية
    const mainContent = screen.getByRole('main');
    const sidebar = screen.getByText('SKILLS WORLD ACADEMY');
    
    expect(mainContent).toBeInTheDocument();
    expect(sidebar).toBeInTheDocument();
    
    // التحقق من أن كلاهما مرئي
    expect(mainContent).toBeVisible();
    expect(sidebar).toBeVisible();
  });
});
