const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    sparse: true, // للمديرين فقط
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    // مطلوب للمديرين فقط
  },
  role: {
    type: String,
    enum: ['admin', 'student'],
    default: 'student'
  },
  studentCode: {
    type: String,
    unique: true,
    sparse: true // للطلاب فقط
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User' // المدير الذي أنشأ الطالب
  },
  enrolledCourses: [{
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    enrolledAt: {
      type: Date,
      default: Date.now
    }
  }],
  completedCourses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  certificates: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Certificate'
  }]
}, {
  timestamps: true
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  if (this.password) {
    this.password = await bcrypt.hash(this.password, 12);
  }
  next();
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

// Generate student code
userSchema.statics.generateStudentCode = async function() {
  let code;
  let isUnique = false;
  
  while (!isUnique) {
    code = Math.floor(100000 + Math.random() * 900000).toString();
    const existingUser = await this.findOne({ studentCode: code });
    if (!existingUser) {
      isUnique = true;
    }
  }
  
  return code;
};

module.exports = mongoose.model('User', userSchema);
