// استخدام العميل الموحد من config
export { supabase } from '../supabase/config';

/**
 * خدمة إدارة الكورسات مع Supabase
 */
export const supabaseCourseService = {
  // إضافة كورس جديد
  async addCourse(courseData) {
    try {
      console.log('📚 إضافة كورس جديد:', courseData.title);
      
      const { data, error } = await supabase
        .from('courses')
        .insert([{
          ...courseData,
          instructor: 'علاء عبد الحميد',
          enrolled_students: 0,
          total_videos: 0,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select();

      if (error) throw error;

      console.log('✅ تم إضافة الكورس بنجاح:', data[0].id);
      return { success: true, id: data[0].id, data: data[0] };
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      throw error;
    }
  },

  // جلب جميع الكورسات
  async getAllCourses() {
    try {
      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(course => ({
        id: course.id,
        ...course,
        createdAt: new Date(course.created_at),
        updatedAt: new Date(course.updated_at)
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الكورسات:', error);
      throw error;
    }
  },

  // تحديث كورس
  async updateCourse(courseId, updateData) {
    try {
      const { data, error } = await supabase
        .from('courses')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', courseId)
        .select();

      if (error) throw error;

      console.log('✅ تم تحديث الكورس بنجاح');
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('❌ خطأ في تحديث الكورس:', error);
      throw error;
    }
  },

  // حذف كورس
  async deleteCourse(courseId) {
    try {
      const { error } = await supabase
        .from('courses')
        .delete()
        .eq('id', courseId);

      if (error) throw error;

      console.log('✅ تم حذف الكورس بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف الكورس:', error);
      throw error;
    }
  },

  // مراقبة الكورسات مع تحديثات فورية
  watchCourses(callback) {
    console.log('🔄 بدء مراقبة الكورسات...');
    
    // جلب البيانات الأولية
    this.getAllCourses().then(callback);

    // إعداد المراقبة الفورية
    const subscription = supabase
      .channel('courses_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'courses' },
        (payload) => {
          console.log('🔄 تغيير في الكورسات:', payload);
          // إعادة جلب البيانات عند حدوث تغيير
          this.getAllCourses().then(callback);
        }
      )
      .subscribe();

    // إرجاع دالة إلغاء الاشتراك
    return () => {
      console.log('🛑 إيقاف مراقبة الكورسات');
      supabase.removeChannel(subscription);
    };
  }
};

/**
 * خدمة إدارة الطلاب مع Supabase
 */
export const supabaseStudentService = {
  // إضافة طالب جديد
  async addStudent(studentData) {
    try {
      console.log('👨‍🎓 إضافة طالب جديد:', studentData.name);
      
      // توليد كود طالب فريد
      const studentCode = await this.generateUniqueStudentCode();
      
      const { data, error } = await supabase
        .from('users')
        .insert([{
          ...studentData,
          role: 'student',
          student_code: studentCode,
          is_active: true,
          enrolled_courses: [],
          total_progress: 0,
          certificates_earned: 0,
          last_login: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select();

      if (error) throw error;

      console.log('✅ تم إضافة الطالب بنجاح:', data[0].id);
      return { 
        success: true, 
        id: data[0].id, 
        studentCode: studentCode,
        data: data[0]
      };
    } catch (error) {
      console.error('❌ خطأ في إضافة الطالب:', error);
      throw error;
    }
  },

  // توليد كود طالب فريد
  async generateUniqueStudentCode() {
    let isUnique = false;
    let code = '';
    
    while (!isUnique) {
      // توليد كود من 6 أرقام
      code = Math.floor(100000 + Math.random() * 900000).toString();
      
      // التحقق من عدم وجود الكود
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('student_code', code)
        .limit(1);
      
      if (error) throw error;
      
      if (data.length === 0) {
        isUnique = true;
      }
    }
    
    return code;
  },

  // جلب جميع الطلاب
  async getAllStudents() {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'student')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(student => ({
        id: student.id,
        ...student,
        createdAt: new Date(student.created_at),
        updatedAt: new Date(student.updated_at),
        lastLogin: student.last_login ? new Date(student.last_login) : null
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الطلاب:', error);
      throw error;
    }
  },

  // تحديث بيانات طالب
  async updateStudent(studentId, updateData) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', studentId)
        .select();

      if (error) throw error;

      console.log('✅ تم تحديث بيانات الطالب بنجاح');
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('❌ خطأ في تحديث الطالب:', error);
      throw error;
    }
  },

  // حذف طالب
  async deleteStudent(studentId) {
    try {
      // حذف جميع تسجيلات الطالب أولاً
      await supabase
        .from('enrollments')
        .delete()
        .eq('student_id', studentId);
      
      // حذف الطالب
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', studentId);

      if (error) throw error;
      
      console.log('✅ تم حذف الطالب وجميع تسجيلاته بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف الطالب:', error);
      throw error;
    }
  },

  // مراقبة الطلاب مع تحديثات فورية
  watchStudents(callback) {
    console.log('🔄 بدء مراقبة الطلاب...');
    
    // جلب البيانات الأولية
    this.getAllStudents().then(callback);

    // إعداد المراقبة الفورية
    const subscription = supabase
      .channel('students_changes')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'users',
          filter: 'role=eq.student'
        },
        (payload) => {
          console.log('🔄 تغيير في الطلاب:', payload);
          // إعادة جلب البيانات عند حدوث تغيير
          this.getAllStudents().then(callback);
        }
      )
      .subscribe();

    // إرجاع دالة إلغاء الاشتراك
    return () => {
      console.log('🛑 إيقاف مراقبة الطلاب');
      supabase.removeChannel(subscription);
    };
  }
};
