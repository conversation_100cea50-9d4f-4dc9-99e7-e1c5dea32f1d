import { collection, addDoc, serverTimestamp, getDocs, deleteDoc } from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * إعداد سريع للنظام مع بيانات تجريبية
 */
export const quickSetup = async () => {
  try {
    console.log('🚀 بدء الإعداد السريع...');

    // مسح البيانات الموجودة
    await clearAllData();

    // إنشاء المدير
    const adminData = {
      name: 'علاء عبد الحميد',
      email: '<EMAIL>',
      phone: '0506747770',
      role: 'admin',
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const adminRef = await addDoc(collection(db, 'users'), adminData);
    console.log('👨‍💼 تم إنشاء المدير:', adminRef.id);

    // إنشاء طلاب تجريبيين
    const testStudents = [
      {
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        phone: '0501234567',
        studentCode: '123456',
        role: 'student',
        isActive: true,
        enrolledCourses: 0,
        completedCourses: 0,
        totalWatchTime: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        name: 'فاطمة أحمد حسن',
        email: '<EMAIL>',
        phone: '0507654321',
        studentCode: '654321',
        role: 'student',
        isActive: true,
        enrolledCourses: 0,
        completedCourses: 0,
        totalWatchTime: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        name: 'محمد عبد الله',
        email: '<EMAIL>',
        phone: '0509876543',
        studentCode: '111111',
        role: 'student',
        isActive: true,
        enrolledCourses: 0,
        completedCourses: 0,
        totalWatchTime: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }
    ];

    for (const student of testStudents) {
      const studentRef = await addDoc(collection(db, 'users'), student);
      console.log(`👨‍🎓 تم إنشاء الطالب: ${student.name} - كود: ${student.studentCode} - ID: ${studentRef.id}`);
    }

    // إنشاء كورس تجريبي
    const courseData = {
      title: 'مقدمة في التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين',
      instructor: 'علاء عبد الحميد',
      duration: '4 ساعات',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 3,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      videos: [
        {
          id: 1,
          title: 'مقدمة في التسويق الرقمي',
          description: 'نظرة عامة على التسويق الرقمي وأهميته',
          duration: '15:30',
          videoUrl: '',
          order: 1
        },
        {
          id: 2,
          title: 'استراتيجيات التسويق',
          description: 'تعلم أهم استراتيجيات التسويق الرقمي',
          duration: '22:45',
          videoUrl: '',
          order: 2
        },
        {
          id: 3,
          title: 'قياس النتائج',
          description: 'كيفية قياس نجاح حملاتك التسويقية',
          duration: '18:20',
          videoUrl: '',
          order: 3
        }
      ]
    };

    const courseRef = await addDoc(collection(db, 'courses'), courseData);
    console.log('📚 تم إنشاء الكورس:', courseRef.id);

    console.log('✅ تم الإعداد السريع بنجاح!');
    console.log('');
    console.log('📧 بيانات تسجيل دخول المدير:');
    console.log('البريد الإلكتروني: <EMAIL>');
    console.log('كلمة المرور: Admin123!');
    console.log('');
    console.log('👨‍🎓 أكواد الطلاب للاختبار:');
    testStudents.forEach(student => {
      console.log(`- ${student.name}: ${student.studentCode}`);
    });

    return {
      adminId: adminRef.id,
      courseId: courseRef.id,
      students: testStudents,
      message: 'تم الإعداد السريع بنجاح'
    };

  } catch (error) {
    console.error('❌ خطأ في الإعداد السريع:', error);
    throw error;
  }
};

/**
 * مسح جميع البيانات
 */
const clearAllData = async () => {
  try {
    console.log('🗑️ مسح البيانات الموجودة...');

    const collections = ['users', 'courses', 'enrollments', 'progress', 'activities', 'certificates', 'faqs', 'chats'];
    
    for (const collectionName of collections) {
      try {
        const snapshot = await getDocs(collection(db, collectionName));
        for (const doc of snapshot.docs) {
          await deleteDoc(doc.ref);
        }
        console.log(`✅ تم مسح مجموعة ${collectionName}`);
      } catch (error) {
        console.log(`⚠️ تحذير: ${collectionName} - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ خطأ في مسح البيانات:', error);
    throw error;
  }
};

/**
 * اختبار تسجيل دخول طالب
 */
export const testStudentLogin = async (studentCode) => {
  try {
    console.log(`🔍 اختبار تسجيل دخول الطالب بالكود: ${studentCode}`);
    
    const { loginStudent } = await import('../firebase/authServiceNew');
    const result = await loginStudent(studentCode);
    
    if (result.success) {
      console.log('✅ نجح تسجيل الدخول!');
      console.log('بيانات الطالب:', result.user);
    } else {
      console.log('❌ فشل تسجيل الدخول:', result.message);
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تسجيل الدخول:', error);
    return { success: false, message: error.message };
  }
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.quickSetup = quickSetup;
window.testStudentLogin = testStudentLogin;

export default {
  quickSetup,
  testStudentLogin
};
