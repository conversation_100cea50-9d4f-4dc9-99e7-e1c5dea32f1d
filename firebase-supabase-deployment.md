# دليل النشر الاحترافي - SKILLS WORLD ACADEMY
## Firebase + Supabase Integration Deployment Guide

### المتطلبات الأساسية

1. **حساب Firebase**
   - مشروع Firebase موجود: `marketwise-academy-qhizq`
   - Firebase CLI مثبت
   - صلاحيات النشر

2. **حساب Supabase**
   - مشروع Supabase جديد
   - قاعدة بيانات PostgreSQL
   - API Keys

### خطوات النشر

#### الخطوة 1: إعداد Supabase

1. **إنشاء مشروع Supabase جديد**
   ```bash
   # زيارة https://supabase.com
   # إنشاء مشروع جديد باسم "skills-world-academy"
   ```

2. **تنفيذ SQL Schema**
   ```sql
   -- في Supabase SQL Editor، تنفيذ الملفات بالترتيب:
   -- 1. database/supabase-schema.sql
   -- 2. database/supabase-functions.sql
   ```

3. **إعداد Row Level Security**
   ```sql
   -- تم تضمينها في schema.sql
   -- التحقق من تفعيل RLS على جميع الجداول
   ```

#### الخطوة 2: تكوين متغيرات البيئة

1. **إنشاء ملف .env**
   ```bash
   cp frontend/.env.example frontend/.env
   ```

2. **تعبئة متغيرات Firebase**
   ```env
   REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
   REACT_APP_FIREBASE_AUTH_DOMAIN=marketwise-academy-qhizq.firebaseapp.com
   REACT_APP_FIREBASE_PROJECT_ID=marketwise-academy-qhizq
   REACT_APP_FIREBASE_STORAGE_BUCKET=marketwise-academy-qhizq.appspot.com
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   REACT_APP_FIREBASE_APP_ID=your_firebase_app_id
   ```

3. **تعبئة متغيرات Supabase**
   ```env
   REACT_APP_SUPABASE_URL=https://your-project.supabase.co
   REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

#### الخطوة 3: تثبيت التبعيات

```bash
cd frontend
npm install @supabase/supabase-js
npm install
```

#### الخطوة 4: اختبار التكامل محلياً

```bash
# تشغيل المشروع محلياً
npm start

# اختبار الاتصال مع Supabase
# التحقق من console.log في المتصفح
```

#### الخطوة 5: بناء المشروع للإنتاج

```bash
# بناء المشروع
npm run build

# اختبار البناء محلياً
npx serve -s build
```

#### الخطوة 6: النشر على Firebase

```bash
# تسجيل الدخول في Firebase CLI
firebase login

# تهيئة المشروع (إذا لم يكن مهيأ)
firebase init hosting

# النشر
firebase deploy --only hosting
```

### إعدادات الأمان

#### Firebase Security Rules

```javascript
// Firestore Rules (للبيانات المؤقتة فقط)
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}

// Storage Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### Supabase RLS Policies

```sql
-- سياسات الأمان موجودة في supabase-schema.sql
-- التحقق من تفعيلها في Supabase Dashboard
```

### مراقبة الأداء

#### Firebase Analytics

```javascript
// في src/firebase/config.js
import { getAnalytics } from "firebase/analytics";
const analytics = getAnalytics(app);
```

#### Supabase Monitoring

```javascript
// مراقبة الاستعلامات في Supabase Dashboard
// تفعيل Real-time subscriptions monitoring
```

### النسخ الاحتياطي

#### قاعدة بيانات Supabase

```bash
# النسخ الاحتياطي التلقائي مفعل في Supabase
# يمكن تحميل النسخ من Dashboard
```

#### Firebase Backup

```bash
# نسخ احتياطي لـ Firestore (للبيانات المؤقتة)
firebase firestore:export gs://your-bucket/backup
```

### استكشاف الأخطاء

#### مشاكل شائعة

1. **خطأ في الاتصال مع Supabase**
   ```javascript
   // التحقق من صحة URL و API Key
   console.log('Supabase URL:', process.env.REACT_APP_SUPABASE_URL);
   ```

2. **مشاكل RLS**
   ```sql
   -- التحقق من سياسات الأمان
   SELECT * FROM pg_policies WHERE tablename = 'users';
   ```

3. **مشاكل Real-time**
   ```javascript
   // التحقق من تفعيل Real-time في Supabase
   // مراجعة console.log للاشتراكات
   ```

### الصيانة

#### تحديثات دورية

```bash
# تحديث التبعيات
npm update

# تحديث Firebase CLI
npm install -g firebase-tools

# مراجعة أمان Supabase
# زيارة Supabase Dashboard > Settings > API
```

#### مراقبة الأداء

- مراجعة Firebase Performance Monitoring
- مراقبة Supabase Database Performance
- تحليل Real-time Subscriptions Usage

### معلومات الاتصال

**المطور:** علاء عبد الحميد  
**البريد الإلكتروني:** ALAA <EMAIL>  
**الهاتف:** 0506747770  

**روابط المشروع:**
- الموقع المباشر: https://marketwise-academy-qhizq.web.app
- Firebase Console: https://console.firebase.google.com/project/marketwise-academy-qhizq
- Supabase Dashboard: [سيتم إضافته بعد الإعداد]

### ملاحظات مهمة

1. **الأمان أولاً**: تأكد من تفعيل جميع سياسات الأمان
2. **النسخ الاحتياطي**: جدولة نسخ احتياطية دورية
3. **المراقبة**: مراجعة الأداء والأخطاء بانتظام
4. **التحديثات**: تحديث التبعيات والأمان دورياً

---

**تم إنشاء هذا الدليل بواسطة نظام التكامل الاحترافي Firebase + Supabase**
