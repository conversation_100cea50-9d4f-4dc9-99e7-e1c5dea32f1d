import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Chip,
  Tooltip,
  Badge,
  IconButton,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Wifi as ConnectedIcon,
  WifiOff as DisconnectedIcon,
  Sync as SyncIcon,
  Notifications as NotificationIcon
} from '@mui/icons-material';
import { useRealtimeUpdates } from '../../hooks/useRealtimeData';
import { useRealtimeNotifications, useDataChangeNotifications } from '../../hooks/useRealtimeNotifications';
import { usePerformanceMonitor } from '../../hooks/usePerformanceMonitor';

const RealtimeIndicator = () => {
  const { courses, students, isLoading, hasError } = useRealtimeUpdates();
  const { unreadCount, notifications } = useRealtimeNotifications();
  const { metrics, recordUpdate, recordError } = usePerformanceMonitor();
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');

  // مراقبة التغييرات في البيانات وإرسال إشعارات
  useDataChangeNotifications(courses.courses, 'courses');
  useDataChangeNotifications(students.students, 'students');

  // تتبع التحديثات الجديدة ومراقبة الأداء
  useEffect(() => {
    if (courses.courses && courses.courses.length > 0) {
      const updateTime = Date.now() - lastUpdate.getTime();
      recordUpdate(JSON.stringify(courses.courses).length, updateTime);
      setLastUpdate(new Date());
      setNotificationMessage('تم تحديث بيانات الكورسات');
      setShowNotification(true);
    }
  }, [courses.courses, recordUpdate, lastUpdate]);

  useEffect(() => {
    if (students.students && students.students.length > 0) {
      const updateTime = Date.now() - lastUpdate.getTime();
      recordUpdate(JSON.stringify(students.students).length, updateTime);
      setLastUpdate(new Date());
      setNotificationMessage('تم تحديث بيانات الطلاب');
      setShowNotification(true);
    }
  }, [students.students, recordUpdate, lastUpdate]);

  // تسجيل الأخطاء
  useEffect(() => {
    if (hasError) {
      recordError();
    }
  }, [hasError, recordError]);

  const getConnectionStatus = () => {
    if (hasError) {
      return {
        status: 'error',
        label: `خطأ في الاتصال (${metrics.errorCount} أخطاء)`,
        color: 'error',
        icon: <DisconnectedIcon />
      };
    }

    if (isLoading) {
      return {
        status: 'loading',
        label: 'جاري التحديث...',
        color: 'warning',
        icon: <SyncIcon className="animate-spin" />
      };
    }

    // تحديد جودة الاتصال بناءً على الأداء
    let qualityLabel = '';
    if (metrics.connectionQuality === 'good') {
      qualityLabel = 'ممتاز';
    } else if (metrics.connectionQuality === 'fair') {
      qualityLabel = 'جيد';
    } else {
      qualityLabel = 'ضعيف';
    }

    return {
      status: 'connected',
      label: `متصل - ${qualityLabel} (${metrics.updateCount} تحديث)`,
      color: metrics.connectionQuality === 'good' ? 'success' :
             metrics.connectionQuality === 'fair' ? 'warning' : 'error',
      icon: <ConnectedIcon />
    };
  };

  const connectionInfo = getConnectionStatus();

  const formatLastUpdate = () => {
    const now = new Date();
    const diff = now - lastUpdate;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (seconds < 60) {
      return 'الآن';
    } else if (minutes < 60) {
      return `منذ ${minutes} دقيقة`;
    } else if (hours < 24) {
      return `منذ ${hours} ساعة`;
    } else {
      return lastUpdate.toLocaleDateString('ar-SA');
    }
  };

  return (
    <Box display="flex" alignItems="center" gap={1}>
      {/* مؤشر حالة الاتصال */}
      <Tooltip title={`${connectionInfo.label} - آخر تحديث: ${formatLastUpdate()}`}>
        <Chip
          icon={connectionInfo.icon}
          label={connectionInfo.label}
          color={connectionInfo.color}
          size="small"
          variant="outlined"
        />
      </Tooltip>

      {/* مؤشر الإشعارات */}
      <Tooltip title={`إشعارات التحديثات (${unreadCount} غير مقروءة)`}>
        <IconButton size="small">
          <Badge badgeContent={unreadCount} color="error">
            <NotificationIcon fontSize="small" />
          </Badge>
        </IconButton>
      </Tooltip>

      {/* إشعار التحديثات */}
      <Snackbar
        open={showNotification}
        autoHideDuration={3000}
        onClose={() => setShowNotification(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowNotification(false)}
          severity="info"
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notificationMessage}
        </Alert>
      </Snackbar>

      {/* معلومات إضافية للمطورين */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
          <div>الكورسات: {courses.courses?.length || 0}</div>
          <div>الطلاب: {students.students?.length || 0}</div>
        </Box>
      )}
    </Box>
  );
};

export default RealtimeIndicator;
