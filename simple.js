const http = require('http');
const url = require('url');

const PORT = 5000;

// بيانات تجريبية
let students = [
  {
    _id: 'student1',
    studentCode: '123456',
    name: 'أحم<PERSON> محمد',
    email: '<EMAIL>',
    isActive: true,
    enrolledCourses: ['course1'],
    certificates: []
  }
];

let courses = [
  {
    _id: 'course1',
    title: 'أساسيات التسويق الرقمي',
    description: 'تعلم أساسيات التسويق الرقمي من الصفر',
    instructor: 'علاء عبد الحميد',
    isActive: true,
    enrolledStudents: ['student1'],
    videos: [
      { id: 1, title: 'مقدمة في التسويق الرقمي', duration: '10:30' },
      { id: 2, title: 'استراتيجيات التسويق', duration: '15:45' },
      { id: 3, title: 'وسائل التواصل الاجتماعي', duration: '12:20' }
    ]
  },
  {
    _id: 'course2',
    title: 'إدارة وسائل التواصل الاجتماعي',
    description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي',
    instructor: 'علاء عبد الحميد',
    isActive: true,
    enrolledStudents: [],
    videos: [
      { id: 1, title: 'مقدمة في وسائل التواصل', duration: '8:30' },
      { id: 2, title: 'استراتيجية المحتوى', duration: '16:20' }
    ]
  }
];

const admin = {
  email: '<EMAIL>',
  password: 'Admin123!',
  name: 'علاء عبد الحميد'
};

function generateId() {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

function generateStudentCode() {
  let code;
  do {
    code = Math.floor(100000 + Math.random() * 900000).toString();
  } while (students.find(s => s.studentCode === code));
  return code;
}

const server = http.createServer((req, res) => {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;
  
  res.setHeader('Content-Type', 'application/json');

  // Helper function to get request body
  const getBody = () => {
    return new Promise((resolve) => {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      req.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch {
          resolve({});
        }
      });
    });
  };

  // Routes
  if (path === '/api/test') {
    res.writeHead(200);
    res.end(JSON.stringify({ message: 'الخادم يعمل!' }));
    return;
  }

  // تسجيل دخول المدير
  if (path === '/api/admin/login' && method === 'POST') {
    getBody().then(({ email, password }) => {
      console.log('🔐 طلب تسجيل دخول مدير:', { email });
      
      if (email === admin.email && password === admin.password) {
        res.writeHead(200);
        res.end(JSON.stringify({
          token: 'fake-admin-token',
          user: { _id: 'admin1', name: admin.name, email: admin.email, role: 'admin' }
        }));
      } else {
        res.writeHead(401);
        res.end(JSON.stringify({ message: 'بيانات تسجيل الدخول غير صحيحة' }));
      }
    });
    return;
  }

  // تسجيل دخول الطالب
  if (path === '/api/student/login' && method === 'POST') {
    getBody().then(({ code }) => {
      console.log('🎓 طلب تسجيل دخول طالب:', { code });
      
      const student = students.find(s => s.studentCode === code && s.isActive);
      if (student) {
        res.writeHead(200);
        res.end(JSON.stringify({
          token: 'fake-student-token',
          user: { _id: student._id, name: student.name, studentCode: student.studentCode, role: 'student' }
        }));
      } else {
        res.writeHead(401);
        res.end(JSON.stringify({ message: 'كود الطالب غير صحيح أو الحساب غير مفعل' }));
      }
    });
    return;
  }

  // جلب الكورسات (للمدير)
  if (path === '/api/admin/courses' && method === 'GET') {
    console.log('📚 طلب جلب الكورسات من المدير');
    res.writeHead(200);
    res.end(JSON.stringify(courses));
    return;
  }

  // إضافة كورس جديد
  if (path === '/api/admin/courses' && method === 'POST') {
    getBody().then((body) => {
      console.log('➕ إضافة كورس جديد:', body);
      
      const newCourse = {
        _id: generateId(),
        ...body,
        enrolledStudents: [],
        videos: [],
        createdAt: new Date().toISOString()
      };
      
      courses.push(newCourse);
      res.writeHead(200);
      res.end(JSON.stringify(newCourse));
    });
    return;
  }

  // تحديث كورس
  if (path.startsWith('/api/admin/courses/') && method === 'PUT') {
    const courseId = path.split('/')[4];
    getBody().then((body) => {
      console.log('✏️ تحديث كورس:', courseId, body);
      
      const courseIndex = courses.findIndex(c => c._id === courseId);
      if (courseIndex !== -1) {
        courses[courseIndex] = { ...courses[courseIndex], ...body };
        res.writeHead(200);
        res.end(JSON.stringify(courses[courseIndex]));
      } else {
        res.writeHead(404);
        res.end(JSON.stringify({ message: 'الكورس غير موجود' }));
      }
    });
    return;
  }

  // حذف كورس
  if (path.startsWith('/api/admin/courses/') && method === 'DELETE') {
    const courseId = path.split('/')[4];
    console.log('🗑️ حذف كورس:', courseId);
    
    const courseIndex = courses.findIndex(c => c._id === courseId);
    if (courseIndex !== -1) {
      courses.splice(courseIndex, 1);
      res.writeHead(200);
      res.end(JSON.stringify({ message: 'تم حذف الكورس بنجاح' }));
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({ message: 'الكورس غير موجود' }));
    }
    return;
  }

  // جلب الطلاب (للمدير)
  if (path === '/api/admin/students' && method === 'GET') {
    console.log('👥 طلب جلب الطلاب من المدير');
    
    const studentsWithCourseNames = students.map(student => ({
      ...student,
      enrolledCoursesNames: student.enrolledCourses.map(courseId => {
        const course = courses.find(c => c._id === courseId);
        return course ? course.title : 'كورس محذوف';
      })
    }));
    
    res.writeHead(200);
    res.end(JSON.stringify(studentsWithCourseNames));
    return;
  }

  // إضافة طالب جديد
  if (path === '/api/admin/students' && method === 'POST') {
    getBody().then((body) => {
      console.log('➕ إضافة طالب جديد:', body);
      
      const studentCode = generateStudentCode();
      const newStudent = {
        _id: generateId(),
        studentCode,
        name: body.name,
        email: body.email || '',
        isActive: true,
        enrolledCourses: [],
        certificates: [],
        createdAt: new Date().toISOString()
      };
      
      students.push(newStudent);
      res.writeHead(200);
      res.end(JSON.stringify({ ...newStudent, enrolledCoursesNames: [] }));
    });
    return;
  }

  // إحصائيات لوحة التحكم
  if (path === '/api/admin/dashboard-stats' && method === 'GET') {
    console.log('📊 طلب إحصائيات لوحة التحكم');
    
    const totalVideos = courses.reduce((total, course) => total + course.videos.length, 0);
    const totalEnrollments = students.reduce((total, student) => total + student.enrolledCourses.length, 0);
    
    res.writeHead(200);
    res.end(JSON.stringify({
      totalStudents: students.length,
      activeStudents: students.filter(s => s.isActive).length,
      totalCourses: courses.length,
      activeCourses: courses.filter(c => c.isActive).length,
      totalVideos,
      totalCertificates: totalEnrollments
    }));
    return;
  }

  // جلب كورسات الطالب
  if (path === '/api/student/courses' && method === 'GET') {
    console.log('📚 طلب جلب كورسات الطالب');
    
    const studentCourses = courses.filter(course => 
      course.enrolledStudents.includes('student1')
    ).map(course => ({
      ...course,
      progress: { completedVideos: 2, totalVideos: course.videos.length, progress: 67 }
    }));

    res.writeHead(200);
    res.end(JSON.stringify(studentCourses));
    return;
  }

  // 404
  res.writeHead(404);
  res.end(JSON.stringify({ message: 'الصفحة غير موجودة' }));
});

server.listen(PORT, () => {
  console.log(`🚀 الخادم البسيط يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الموقع متاح على: http://localhost:${PORT}`);
  console.log(`📱 API متاح على: http://localhost:${PORT}/api/test`);
  console.log(`👨‍💼 بيانات المدير: <EMAIL> / Admin123!`);
  console.log(`👨‍🎓 كود طالب تجريبي: 123456`);
});
