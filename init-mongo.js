// إنشاء قاعدة البيانات والمستخدم الافتراضي
db = db.getSiblingDB('alaa-courses');

// إنشاء مستخدم المدير الافتراضي
db.users.insertOne({
  name: 'علاء عبد الحميد',
  email: '<EMAIL>',
  password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcQjmu/Ue', // Admin123!
  role: 'admin',
  isActive: true,
  avatar: '/uploads/avatars/admin.jpg',
  bio: 'خبير في التسويق الرقمي مع أكثر من 10 سنوات من الخبرة',
  socialLinks: {
    facebook: 'https://facebook.com/alaa.marketing',
    instagram: 'https://instagram.com/alaa.marketing',
    linkedin: 'https://linkedin.com/in/alaa-marketing',
    youtube: 'https://youtube.com/c/alaamarketing'
  },
  createdAt: new Date(),
  updatedAt: new Date()
});

// إنشاء أقسام افتراضية
db.categories.insertMany([
  {
    name: 'التسويق الرقمي',
    description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
    icon: 'digital-marketing',
    color: '#2196F3',
    order: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'وسائل التواصل الاجتماعي',
    description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
    icon: 'social-media',
    color: '#4CAF50',
    order: 2,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'التسويق بالمحتوى',
    description: 'إنشاء وتسويق المحتوى الفعال',
    icon: 'content-marketing',
    color: '#FF9800',
    order: 3,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'التجارة الإلكترونية',
    description: 'بناء وإدارة المتاجر الإلكترونية',
    icon: 'ecommerce',
    color: '#9C27B0',
    order: 4,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// إنشاء كورسات شاملة
db.courses.insertMany([
  {
    title: 'أساسيات التسويق الرقمي',
    description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف مع أحدث الاستراتيجيات والأدوات',
    price: 299,
    originalPrice: 499,
    discount: 40,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.8,
    reviewsCount: 156,
    level: 'مبتدئ',
    language: 'العربية',
    duration: '8 ساعات',
    totalVideos: 24,
    category: 'التسويق الرقمي',
    tags: ['تسويق', 'رقمي', 'مبتدئ', 'أساسيات'],
    thumbnail: '/uploads/courses/digital-marketing-basics.jpg',
    trailer: '/uploads/videos/trailer-digital-marketing.mp4',
    requirements: [
      'لا توجد متطلبات مسبقة',
      'جهاز كمبيوتر أو هاتف ذكي',
      'اتصال بالإنترنت'
    ],
    whatYouWillLearn: [
      'فهم أساسيات التسويق الرقمي',
      'إنشاء استراتيجية تسويقية فعالة',
      'استخدام وسائل التواصل الاجتماعي للتسويق',
      'تحليل البيانات وقياس النتائج',
      'إنشاء حملات إعلانية ناجحة'
    ],
    modules: [
      {
        title: 'مقدمة في التسويق الرقمي',
        description: 'تعرف على أساسيات التسويق الرقمي وأهميته',
        order: 1,
        videos: [
          {
            title: 'ما هو التسويق الرقمي؟',
            description: 'مقدمة شاملة عن التسويق الرقمي وأهميته في العصر الحديث',
            duration: '15:30',
            videoUrl: '/uploads/videos/module1-video1.mp4',
            order: 1,
            isPreview: true
          },
          {
            title: 'الفرق بين التسويق التقليدي والرقمي',
            description: 'مقارنة بين أساليب التسويق التقليدية والرقمية',
            duration: '12:45',
            videoUrl: '/uploads/videos/module1-video2.mp4',
            order: 2,
            isPreview: false
          }
        ]
      },
      {
        title: 'استراتيجيات التسويق الرقمي',
        description: 'تعلم كيفية وضع استراتيجية تسويقية فعالة',
        order: 2,
        videos: [
          {
            title: 'تحديد الجمهور المستهدف',
            description: 'كيفية تحديد وفهم جمهورك المستهدف',
            duration: '18:20',
            videoUrl: '/uploads/videos/module2-video1.mp4',
            order: 1,
            isPreview: false
          },
          {
            title: 'وضع الأهداف التسويقية',
            description: 'تعلم كيفية وضع أهداف SMART للتسويق',
            duration: '16:15',
            videoUrl: '/uploads/videos/module2-video2.mp4',
            order: 2,
            isPreview: false
          }
        ]
      }
    ],
    enrolledStudents: [],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'إدارة وسائل التواصل الاجتماعي',
    description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية وزيادة المتابعين والتفاعل',
    price: 399,
    originalPrice: 599,
    discount: 33,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.9,
    reviewsCount: 89,
    level: 'متوسط',
    language: 'العربية',
    duration: '6 ساعات',
    totalVideos: 18,
    category: 'وسائل التواصل الاجتماعي',
    tags: ['سوشيال ميديا', 'إدارة', 'محتوى', 'تفاعل'],
    thumbnail: '/uploads/courses/social-media-management.jpg',
    trailer: '/uploads/videos/trailer-social-media.mp4',
    requirements: [
      'معرفة أساسية بوسائل التواصل الاجتماعي',
      'حساب على منصات التواصل الاجتماعي',
      'جهاز كمبيوتر أو هاتف ذكي'
    ],
    whatYouWillLearn: [
      'إنشاء استراتيجية محتوى فعالة',
      'جدولة المنشورات وإدارة الوقت',
      'زيادة التفاعل والمتابعين',
      'تحليل الأداء وقياس النتائج',
      'التعامل مع التعليقات والرسائل'
    ],
    modules: [
      {
        title: 'أساسيات إدارة وسائل التواصل',
        description: 'تعلم الأساسيات المهمة لإدارة حسابات التواصل الاجتماعي',
        order: 1,
        videos: [
          {
            title: 'اختيار المنصات المناسبة',
            description: 'كيفية اختيار منصات التواصل المناسبة لعملك',
            duration: '20:15',
            videoUrl: '/uploads/videos/social-module1-video1.mp4',
            order: 1,
            isPreview: true
          }
        ]
      }
    ],
    enrolledStudents: [],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'التسويق عبر البريد الإلكتروني',
    description: 'استراتيجيات فعالة للتسويق عبر البريد الإلكتروني وزيادة المبيعات',
    price: 249,
    originalPrice: 399,
    discount: 38,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.7,
    reviewsCount: 67,
    level: 'مبتدئ',
    language: 'العربية',
    duration: '5 ساعات',
    totalVideos: 15,
    category: 'التسويق الإلكتروني',
    tags: ['إيميل ماركتنج', 'مبيعات', 'أتمتة'],
    thumbnail: '/uploads/courses/email-marketing.jpg',
    trailer: '/uploads/videos/trailer-email-marketing.mp4',
    requirements: [
      'لا توجد متطلبات مسبقة',
      'حساب بريد إلكتروني',
      'رغبة في تعلم التسويق'
    ],
    whatYouWillLearn: [
      'بناء قائمة بريدية فعالة',
      'كتابة رسائل تسويقية مؤثرة',
      'أتمتة حملات البريد الإلكتروني',
      'تحليل معدلات الفتح والنقر',
      'تجنب مجلد الرسائل المزعجة'
    ],
    modules: [
      {
        title: 'أساسيات التسويق عبر البريد الإلكتروني',
        description: 'تعلم الأساسيات والمفاهيم المهمة',
        order: 1,
        videos: [
          {
            title: 'مقدمة في التسويق عبر البريد الإلكتروني',
            description: 'أهمية التسويق عبر البريد الإلكتروني في العصر الرقمي',
            duration: '18:30',
            videoUrl: '/uploads/videos/email-module1-video1.mp4',
            order: 1,
            isPreview: true
          }
        ]
      }
    ],
    enrolledStudents: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// إنشاء طلاب تجريبيين
db.students.insertMany([
  {
    studentCode: '123456',
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '+966501234567',
    isActive: true,
    enrolledCourses: [],
    completedCourses: [],
    certificates: [],
    totalWatchTime: 0,
    joinDate: new Date('2024-01-15'),
    lastActivity: new Date(),
    avatar: '/uploads/avatars/student1.jpg',
    notes: 'طالب مجتهد ومتفاعل',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    studentCode: '789012',
    name: 'فاطمة علي حسن',
    email: '<EMAIL>',
    phone: '+966507654321',
    isActive: true,
    enrolledCourses: [],
    completedCourses: [],
    certificates: [],
    totalWatchTime: 0,
    joinDate: new Date('2024-01-10'),
    lastActivity: new Date(),
    avatar: '/uploads/avatars/student2.jpg',
    notes: 'طالبة متميزة وسريعة التعلم',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    studentCode: '345678',
    name: 'محمد عبدالله',
    email: '<EMAIL>',
    phone: '+966509876543',
    isActive: true,
    enrolledCourses: [],
    completedCourses: [],
    certificates: [],
    totalWatchTime: 0,
    joinDate: new Date('2024-02-01'),
    lastActivity: new Date(),
    avatar: '/uploads/avatars/student3.jpg',
    notes: 'يحتاج إلى متابعة إضافية',
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// إنشاء جدول التقدم
db.progress.insertMany([
  {
    studentId: ObjectId(),
    courseId: ObjectId(),
    completedVideos: [],
    totalVideos: 24,
    progressPercentage: 0,
    lastWatchedVideo: null,
    totalWatchTime: 0,
    startDate: new Date(),
    lastActivity: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// إنشاء الشهادات
db.certificates.insertMany([
  {
    studentId: ObjectId(),
    courseId: ObjectId(),
    certificateNumber: 'CERT-2024-001',
    issuedDate: new Date(),
    studentName: 'فاطمة علي حسن',
    courseName: 'أساسيات التسويق الرقمي',
    instructorName: 'علاء عبد الحميد',
    grade: 'ممتاز',
    certificateUrl: '/uploads/certificates/cert-001.pdf',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// إنشاء الإعدادات العامة
db.settings.insertOne({
  siteName: 'منصة كورسات علاء عبد الحميد',
  siteDescription: 'منصة تعليمية متخصصة في التسويق الرقمي',
  logo: '/uploads/logo.png',
  favicon: '/uploads/favicon.ico',
  contactEmail: '<EMAIL>',
  supportEmail: '<EMAIL>',
  phone: '+966501234567',
  address: 'الرياض، المملكة العربية السعودية',
  socialLinks: {
    facebook: 'https://facebook.com/alaa.courses',
    instagram: 'https://instagram.com/alaa.courses',
    twitter: 'https://twitter.com/alaa_courses',
    linkedin: 'https://linkedin.com/company/alaa-courses',
    youtube: 'https://youtube.com/c/alaacourses'
  },
  paymentMethods: ['visa', 'mastercard', 'mada', 'applepay'],
  currency: 'SAR',
  timezone: 'Asia/Riyadh',
  language: 'ar',
  maintenanceMode: false,
  registrationEnabled: true,
  emailVerificationRequired: false,
  createdAt: new Date(),
  updatedAt: new Date()
});

// إنشاء الإشعارات
db.notifications.insertMany([
  {
    title: 'مرحباً بك في المنصة',
    message: 'نرحب بك في منصة كورسات علاء عبد الحميد. نتمنى لك تجربة تعليمية ممتعة ومفيدة.',
    type: 'welcome',
    isGlobal: true,
    isRead: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'كورس جديد متاح',
    message: 'تم إضافة كورس جديد: "التسويق عبر البريد الإلكتروني". سجل الآن واحصل على خصم 30%!',
    type: 'course',
    isGlobal: true,
    isRead: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

print('تم إنشاء قاعدة البيانات والبيانات الافتراضية بنجاح!');
