/**
 * سكريبت إعداد Supabase التلقائي
 * SKILLS WORLD ACADEMY - Supabase Auto Setup
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// إعدادات Supabase (يجب تحديثها بالقيم الحقيقية)
const SUPABASE_CONFIG = {
  url: process.env.SUPABASE_URL || 'https://your-project.supabase.co',
  key: process.env.SUPABASE_SERVICE_KEY || 'your-service-key',
  anonKey: process.env.SUPABASE_ANON_KEY || 'your-anon-key'
};

// إنشاء عميل Supabase
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

/**
 * قراءة ملف SQL
 */
function readSQLFile(filename) {
  const filePath = path.join(__dirname, 'database', filename);
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * تنفيذ SQL Schema
 */
async function setupDatabase() {
  try {
    console.log('🚀 بدء إعداد قاعدة البيانات...');

    // قراءة ملفات SQL
    const schema = readSQLFile('supabase-schema.sql');
    const functions = readSQLFile('supabase-functions.sql');

    console.log('📄 تنفيذ Schema...');
    // تقسيم الـ schema إلى استعلامات منفصلة
    const schemaQueries = schema.split(';').filter(query => query.trim());
    
    for (const query of schemaQueries) {
      if (query.trim()) {
        try {
          await supabase.rpc('exec_sql', { sql: query.trim() });
          console.log('✅ تم تنفيذ استعلام بنجاح');
        } catch (error) {
          console.log('⚠️ تحذير في الاستعلام:', error.message);
        }
      }
    }

    console.log('🔧 تنفيذ Functions...');
    // تنفيذ الدوال
    const functionQueries = functions.split(';').filter(query => query.trim());
    
    for (const query of functionQueries) {
      if (query.trim()) {
        try {
          await supabase.rpc('exec_sql', { sql: query.trim() });
          console.log('✅ تم تنفيذ دالة بنجاح');
        } catch (error) {
          console.log('⚠️ تحذير في الدالة:', error.message);
        }
      }
    }

    console.log('✅ تم إعداد قاعدة البيانات بنجاح!');
    return true;

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
    return false;
  }
}

/**
 * اختبار الاتصال
 */
async function testConnection() {
  try {
    console.log('🔗 اختبار الاتصال مع Supabase...');
    
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .limit(1);

    if (error) {
      console.log('⚠️ لم يتم العثور على جدول settings، سيتم إنشاؤه');
      return false;
    }

    console.log('✅ الاتصال ناجح!');
    return true;

  } catch (error) {
    console.error('❌ فشل الاتصال:', error);
    return false;
  }
}

/**
 * إنشاء ملف البيئة
 */
function createEnvFile() {
  const envContent = `
# Supabase Configuration - SKILLS WORLD ACADEMY
REACT_APP_SUPABASE_URL=${SUPABASE_CONFIG.url}
REACT_APP_SUPABASE_ANON_KEY=${SUPABASE_CONFIG.anonKey}

# Firebase Configuration (موجود مسبقاً)
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=marketwise-academy-qhizq.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=marketwise-academy-qhizq
REACT_APP_FIREBASE_STORAGE_BUCKET=marketwise-academy-qhizq.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
REACT_APP_FIREBASE_APP_ID=your_firebase_app_id

# Application Configuration
REACT_APP_ACADEMY_NAME=SKILLS WORLD ACADEMY
REACT_APP_ADMIN_NAME=علاء عبد الحميد
REACT_APP_ADMIN_EMAIL=ALAA <EMAIL>
REACT_APP_ADMIN_PHONE=0506747770
`;

  fs.writeFileSync(path.join(__dirname, 'frontend', '.env'), envContent.trim());
  console.log('✅ تم إنشاء ملف .env');
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🏫 SKILLS WORLD ACADEMY - إعداد Supabase');
  console.log('==========================================');

  // التحقق من وجود المتغيرات
  if (!SUPABASE_CONFIG.url.includes('supabase.co')) {
    console.log('⚠️ يرجى تحديث متغيرات Supabase في بداية الملف');
    console.log('📋 الخطوات المطلوبة:');
    console.log('1. إنشاء مشروع على https://supabase.com');
    console.log('2. نسخ Project URL و Service Key');
    console.log('3. تحديث المتغيرات في هذا الملف');
    console.log('4. تشغيل السكريبت مرة أخرى');
    return;
  }

  // اختبار الاتصال
  const connected = await testConnection();
  
  if (!connected) {
    // إعداد قاعدة البيانات
    const setupSuccess = await setupDatabase();
    
    if (setupSuccess) {
      console.log('🎉 تم إعداد Supabase بنجاح!');
    } else {
      console.log('❌ فشل في إعداد قاعدة البيانات');
      return;
    }
  }

  // إنشاء ملف البيئة
  createEnvFile();

  console.log('');
  console.log('🎯 الخطوات التالية:');
  console.log('1. تحديث ملف frontend/.env بالقيم الصحيحة');
  console.log('2. تشغيل: cd frontend && npm run build');
  console.log('3. تشغيل: firebase deploy --only hosting');
  console.log('');
  console.log('🌐 الموقع: https://marketwise-academy-qhizq.web.app');
}

// تشغيل السكريبت
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  setupDatabase,
  testConnection,
  createEnvFile
};
