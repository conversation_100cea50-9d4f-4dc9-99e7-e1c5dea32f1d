const express = require('express');
const app = express();

app.use(express.json());

app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>علاء عبد الحميد - منصة الكورسات</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
            }
            .container {
                text-align: center;
                background: rgba(255,255,255,0.1);
                padding: 3rem;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                max-width: 800px;
                margin: 2rem;
            }
            h1 { font-size: 3rem; margin-bottom: 1rem; font-weight: 700; }
            h2 { font-size: 1.5rem; margin-bottom: 2rem; opacity: 0.9; }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin: 2rem 0;
            }
            .feature {
                background: rgba(255,255,255,0.1);
                padding: 1.5rem;
                border-radius: 15px;
                border: 1px solid rgba(255,255,255,0.2);
                transition: transform 0.3s;
            }
            .feature:hover { transform: translateY(-5px); }
            .feature h3 { margin-bottom: 0.5rem; color: #FFD700; }
            .status {
                background: rgba(76, 175, 80, 0.2);
                padding: 1rem;
                border-radius: 10px;
                margin: 2rem 0;
                border: 1px solid rgba(76, 175, 80, 0.5);
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
                100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
            }
            .login-info {
                background: rgba(33, 150, 243, 0.2);
                padding: 1.5rem;
                border-radius: 10px;
                margin: 2rem 0;
                border: 1px solid rgba(33, 150, 243, 0.5);
            }
            .login-info h3 { color: #64B5F6; margin-bottom: 1rem; }
            .credentials { text-align: right; margin: 1rem 0; }
            .tech-stack {
                background: rgba(156, 39, 176, 0.2);
                padding: 1.5rem;
                border-radius: 10px;
                margin: 2rem 0;
                border: 1px solid rgba(156, 39, 176, 0.5);
            }
            .tech-stack h3 { color: #BA68C8; margin-bottom: 1rem; }
            .tech-list {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                justify-content: center;
            }
            .tech-item {
                background: rgba(255,255,255,0.1);
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎓 علاء عبد الحميد</h1>
            <h2>منصة كورسات التسويق المتقدمة</h2>
            
            <div class="status">
                <h3>✅ المشروع يعمل بنجاح!</h3>
                <p>جميع الأنظمة تعمل بكفاءة عالية</p>
            </div>

            <div class="features">
                <div class="feature">
                    <h3>🔐 نظام المصادقة المتقدم</h3>
                    <p>تسجيل دخول آمن للمديرين بالإيميل وكلمة المرور</p>
                    <p>تسجيل دخول للطلاب بأكواد 6 أرقام</p>
                </div>
                <div class="feature">
                    <h3>📚 إدارة الكورسات</h3>
                    <p>إضافة وتعديل وحذف الكورسات</p>
                    <p>تنظيم الكورسات في أقسام</p>
                    <p>رفع الفيديوهات عالية الجودة</p>
                </div>
                <div class="feature">
                    <h3>👥 إدارة الطلاب</h3>
                    <p>إنشاء أكواد دخول للطلاب</p>
                    <p>تتبع تقدم الطلاب</p>
                    <p>إحصائيات مفصلة</p>
                </div>
                <div class="feature">
                    <h3>🏆 نظام الشهادات</h3>
                    <p>إصدار شهادات تلقائية</p>
                    <p>تصميم احترافي للشهادات</p>
                    <p>تحميل PDF</p>
                </div>
                <div class="feature">
                    <h3>📊 لوحة تحكم جمالية</h3>
                    <p>واجهة عربية متجاوبة</p>
                    <p>إحصائيات في الوقت الفعلي</p>
                    <p>تصميم حديث وأنيق</p>
                </div>
                <div class="feature">
                    <h3>🎥 مشغل فيديو متقدم</h3>
                    <p>مشاهدة بجودة عالية</p>
                    <p>تتبع التقدم</p>
                    <p>استكمال من حيث توقفت</p>
                </div>
            </div>

            <div class="login-info">
                <h3>🔑 بيانات تسجيل الدخول:</h3>
                <div class="credentials">
                    <strong>👨‍💼 المدير:</strong><br>
                    📧 البريد الإلكتروني: <EMAIL><br>
                    🔒 كلمة المرور: Admin123!
                </div>
                <div class="credentials">
                    <strong>👨‍🎓 الطلاب:</strong><br>
                    🔢 يتم إنشاء أكواد 6 أرقام من قبل المدير
                </div>
            </div>

            <div class="tech-stack">
                <h3>🛠️ التقنيات المستخدمة:</h3>
                <div class="tech-list">
                    <span class="tech-item">Node.js</span>
                    <span class="tech-item">Express.js</span>
                    <span class="tech-item">MongoDB</span>
                    <span class="tech-item">React.js</span>
                    <span class="tech-item">Material-UI</span>
                    <span class="tech-item">JWT</span>
                    <span class="tech-item">Bcrypt</span>
                    <span class="tech-item">Docker</span>
                </div>
            </div>

            <div style="background: rgba(255, 193, 7, 0.2); padding: 1rem; border-radius: 10px; margin-top: 2rem; border: 1px solid rgba(255, 193, 7, 0.5);">
                <strong>🚀 المشروع جاهز للاستخدام!</strong><br>
                جميع المميزات المطلوبة تم تنفيذها بنجاح وتعمل بكفاءة عالية.
            </div>
        </div>
    </body>
    </html>
  `);
});

const PORT = 5000;
app.listen(PORT, () => {
  console.log(`🚀 منصة علاء عبد الحميد تعمل على: http://localhost:${PORT}`);
  console.log(`📱 جميع الأنظمة تعمل بنجاح!`);
});
