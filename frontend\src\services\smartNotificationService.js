/**
 * نظام الإشعارات الذكي - Skills World Academy
 * Smart Notification System with Real-time Updates
 */

import { db } from '../firebase/config';
import {
  collection,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  updateDoc,
  doc,
  serverTimestamp,
  getDocs,
  writeBatch
} from 'firebase/firestore';
import toast from 'react-hot-toast';

/**
 * خدمة الإشعارات الذكية
 */
export class SmartNotificationService {
  constructor() {
    this.listeners = new Map();
    this.notificationQueue = [];
    this.isProcessing = false;
  }

  /**
   * إرسال إشعار ذكي للطالب
   */
  async sendSmartNotification(userId, notification) {
    try {
      console.log('📱 إرسال إشعار ذكي للطالب:', userId);

      const notificationData = {
        userId,
        type: notification.type || 'info',
        title: notification.title,
        message: notification.message,
        data: notification.data || {},
        
        // خصائص الإشعار الذكي
        priority: notification.priority || 'normal',
        category: notification.category || 'general',
        actionRequired: notification.actionRequired || false,
        actionText: notification.actionText || null,
        actionUrl: notification.actionUrl || null,
        
        // خصائص التوقيت
        createdAt: serverTimestamp(),
        scheduledFor: notification.scheduledFor || null,
        expiresAt: notification.expiresAt || null,
        
        // خصائص الحالة
        isRead: false,
        isDelivered: false,
        isClicked: false,
        
        // خصائص التخصيص
        icon: notification.icon || '📚',
        color: notification.color || 'primary',
        sound: notification.sound || 'default',
        
        // بيانات التتبع
        deviceType: this.detectDeviceType(),
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      };

      // إضافة الإشعار إلى قاعدة البيانات
      const notificationRef = await addDoc(collection(db, 'notifications'), notificationData);
      
      // إرسال إشعار فوري إذا كان الطالب متصلاً
      await this.sendRealtimeNotification(userId, {
        ...notificationData,
        id: notificationRef.id
      });

      console.log('✅ تم إرسال الإشعار الذكي بنجاح:', notificationRef.id);
      
      return {
        success: true,
        notificationId: notificationRef.id,
        data: notificationData
      };

    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعار الذكي:', error);
      throw error;
    }
  }

  /**
   * مراقبة إشعارات الطالب في الوقت الفعلي
   */
  watchUserNotifications(userId, callback) {
    const listenerId = `notifications_${userId}`;
    
    // إيقاف المراقبة السابقة إن وجدت
    if (this.listeners.has(listenerId)) {
      this.listeners.get(listenerId)();
    }

    console.log('🔔 بدء مراقبة إشعارات الطالب:', userId);

    const q = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        scheduledFor: doc.data().scheduledFor?.toDate?.() || null,
        expiresAt: doc.data().expiresAt?.toDate?.() || null
      }));

      // فلترة الإشعارات غير المنتهية الصلاحية
      const validNotifications = notifications.filter(notification => {
        if (!notification.expiresAt) return true;
        return notification.expiresAt > new Date();
      });

      // ترتيب الإشعارات (غير المقروءة أولاً)
      validNotifications.sort((a, b) => {
        if (a.isRead !== b.isRead) {
          return a.isRead ? 1 : -1;
        }
        return b.createdAt - a.createdAt;
      });

      console.log('🔔 تحديث إشعارات الطالب:', validNotifications.length);
      
      // عرض الإشعارات الجديدة
      this.showNewNotifications(validNotifications, callback);
      
      callback(validNotifications);
    }, (error) => {
      console.error('❌ خطأ في مراقبة الإشعارات:', error);
      callback([]);
    });

    this.listeners.set(listenerId, unsubscribe);
    return unsubscribe;
  }

  /**
   * عرض الإشعارات الجديدة
   */
  showNewNotifications(notifications, callback) {
    const newNotifications = notifications.filter(n => 
      !n.isRead && 
      !n.isDelivered && 
      n.createdAt > new Date(Date.now() - 5000) // آخر 5 ثوان
    );

    newNotifications.forEach(notification => {
      // عرض toast notification
      toast.success(
        <div style={{ direction: 'rtl', textAlign: 'right' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
            {notification.icon} {notification.title}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.9 }}>
            {notification.message}
          </div>
          {notification.actionText && (
            <div style={{ marginTop: '8px' }}>
              <button 
                style={{
                  background: '#1976d2',
                  color: 'white',
                  border: 'none',
                  padding: '4px 12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  if (notification.actionUrl) {
                    window.location.href = notification.actionUrl;
                  }
                  this.markAsClicked(notification.id);
                }}
              >
                {notification.actionText}
              </button>
            </div>
          )}
        </div>,
        {
          duration: notification.priority === 'high' ? 8000 : 5000,
          position: 'top-right',
          style: {
            background: notification.color === 'success' ? '#4caf50' : 
                       notification.color === 'warning' ? '#ff9800' : 
                       notification.color === 'error' ? '#f44336' : '#1976d2',
            color: 'white',
            direction: 'rtl'
          }
        }
      );

      // تحديث حالة التسليم
      this.markAsDelivered(notification.id);
    });
  }

  /**
   * تحديد نوع الجهاز
   */
  detectDeviceType() {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  }

  /**
   * إرسال إشعار فوري
   */
  async sendRealtimeNotification(userId, notification) {
    try {
      // يمكن إضافة WebSocket أو Server-Sent Events هنا
      console.log('⚡ إرسال إشعار فوري:', userId, notification.title);
      
      // محاكاة إرسال فوري
      if (window.realtimeNotificationCallback) {
        window.realtimeNotificationCallback(notification);
      }
    } catch (error) {
      console.warn('تحذير في الإرسال الفوري:', error);
    }
  }

  /**
   * تحديد الإشعار كمقروء
   */
  async markAsRead(notificationId) {
    try {
      await updateDoc(doc(db, 'notifications', notificationId), {
        isRead: true,
        readAt: serverTimestamp()
      });
    } catch (error) {
      console.error('خطأ في تحديد الإشعار كمقروء:', error);
    }
  }

  /**
   * تحديد الإشعار كمُسلم
   */
  async markAsDelivered(notificationId) {
    try {
      await updateDoc(doc(db, 'notifications', notificationId), {
        isDelivered: true,
        deliveredAt: serverTimestamp()
      });
    } catch (error) {
      console.error('خطأ في تحديد الإشعار كمُسلم:', error);
    }
  }

  /**
   * تحديد الإشعار كمُنقر عليه
   */
  async markAsClicked(notificationId) {
    try {
      await updateDoc(doc(db, 'notifications', notificationId), {
        isClicked: true,
        clickedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('خطأ في تحديد الإشعار كمُنقر عليه:', error);
    }
  }

  /**
   * تحديد جميع الإشعارات كمقروءة
   */
  async markAllAsRead(userId) {
    try {
      const q = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        where('isRead', '==', false)
      );

      const snapshot = await getDocs(q);
      const batch = writeBatch(db);

      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          isRead: true,
          readAt: serverTimestamp()
        });
      });

      await batch.commit();
      console.log('✅ تم تحديد جميع الإشعارات كمقروءة');
    } catch (error) {
      console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', error);
    }
  }

  /**
   * إرسال إشعار تسجيل في كورس
   */
  async sendCourseEnrollmentNotification(studentId, courseData, enrollmentData) {
    return await this.sendSmartNotification(studentId, {
      type: 'course_enrollment',
      title: '🎉 تم تسجيلك في كورس جديد!',
      message: `مرحباً! تم تسجيلك بنجاح في كورس "${courseData.title}". ابدأ رحلة التعلم الآن!`,
      data: {
        courseId: courseData.id,
        enrollmentId: enrollmentData.id,
        courseTitle: courseData.title,
        instructor: courseData.instructor
      },
      priority: 'high',
      category: 'enrollment',
      actionRequired: true,
      actionText: 'ابدأ التعلم',
      actionUrl: `/course/${courseData.id}`,
      icon: '📚',
      color: 'success',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // ينتهي بعد أسبوع
    });
  }

  /**
   * إرسال إشعار إنجاز درس
   */
  async sendLessonCompletionNotification(studentId, lessonData, courseData) {
    return await this.sendSmartNotification(studentId, {
      type: 'lesson_completion',
      title: '✅ تم إنجاز الدرس!',
      message: `أحسنت! تم إنجاز درس "${lessonData.title}" في كورس "${courseData.title}"`,
      data: {
        lessonId: lessonData.id,
        courseId: courseData.id,
        progress: lessonData.progress
      },
      priority: 'normal',
      category: 'progress',
      icon: '🎯',
      color: 'success'
    });
  }

  /**
   * إرسال إشعار إنجاز كورس
   */
  async sendCourseCompletionNotification(studentId, courseData) {
    return await this.sendSmartNotification(studentId, {
      type: 'course_completion',
      title: '🏆 تهانينا! تم إنجاز الكورس!',
      message: `مبروك! تم إنجاز كورس "${courseData.title}" بنجاح. يمكنك الآن الحصول على الشهادة!`,
      data: {
        courseId: courseData.id,
        courseTitle: courseData.title,
        completionDate: new Date().toISOString()
      },
      priority: 'high',
      category: 'achievement',
      actionRequired: true,
      actionText: 'احصل على الشهادة',
      actionUrl: `/certificate/${courseData.id}`,
      icon: '🎓',
      color: 'success'
    });
  }

  /**
   * تنظيف المراقبات
   */
  cleanup() {
    this.listeners.forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    });
    this.listeners.clear();
    console.log('🧹 تم تنظيف مراقبات الإشعارات');
  }
}

// إنشاء instance واحد للاستخدام
export const smartNotificationService = new SmartNotificationService();

// تصدير افتراضي
export default smartNotificationService;
