#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 بدء النشر على Firebase...\n');

// التحقق من تثبيت Firebase CLI
try {
  execSync('firebase --version', { stdio: 'ignore' });
} catch (error) {
  console.log('📦 تثبيت Firebase CLI...');
  execSync('npm install -g firebase-tools', { stdio: 'inherit' });
}

// تثبيت المكتبات
console.log('📦 تثبيت المكتبات...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ تم تثبيت مكتبات الخادم');
} catch (error) {
  console.error('❌ فشل في تثبيت مكتبات الخادم');
  process.exit(1);
}

// تثبيت مكتبات Functions
console.log('📦 تثبيت مكتبات Functions...');
try {
  execSync('npm install', { cwd: 'functions', stdio: 'inherit' });
  console.log('✅ تم تثبيت مكتبات Functions');
} catch (error) {
  console.error('❌ فشل في تثبيت مكتبات Functions');
  process.exit(1);
}

// بناء الواجهة الأمامية
console.log('🎨 بناء الواجهة الأمامية...');
try {
  execSync('npm install', { cwd: 'frontend', stdio: 'inherit' });
  execSync('npm run build', { cwd: 'frontend', stdio: 'inherit' });
  console.log('✅ تم بناء الواجهة الأمامية');
} catch (error) {
  console.error('❌ فشل في بناء الواجهة الأمامية');
  process.exit(1);
}

// تسجيل الدخول إلى Firebase
console.log('🔐 تسجيل الدخول إلى Firebase...');
try {
  execSync('firebase login --interactive', { stdio: 'inherit' });
  console.log('✅ تم تسجيل الدخول بنجاح');
} catch (error) {
  console.log('ℹ️ تم تسجيل الدخول مسبقاً أو تم الإلغاء');
}

// إنشاء مشروع Firebase جديد
console.log('🚀 إعداد مشروع Firebase...');
try {
  // التحقق من وجود .firebaserc
  if (!fs.existsSync('.firebaserc')) {
    console.log('📝 إنشاء مشروع Firebase جديد...');
    console.log('يرجى اتباع التعليمات لإنشاء مشروع جديد:');
    console.log('1. اختر "Create a new project"');
    console.log('2. أدخل اسم المشروع: alaa-courses');
    console.log('3. اختر المنطقة الأقرب لك');
    
    execSync('firebase init', { stdio: 'inherit' });
  } else {
    console.log('ℹ️ مشروع Firebase موجود بالفعل');
  }
} catch (error) {
  console.error('❌ فشل في إعداد Firebase');
  process.exit(1);
}

// النشر على Firebase
console.log('🚀 النشر على Firebase...');
try {
  execSync('firebase deploy', { stdio: 'inherit' });
  console.log('✅ تم النشر بنجاح!');
} catch (error) {
  console.error('❌ فشل في النشر');
  process.exit(1);
}

// الحصول على رابط المشروع
try {
  const result = execSync('firebase hosting:channel:list', { encoding: 'utf8' });
  console.log('\n🎉 النشر مكتمل!');
  console.log('🌐 يمكنك الوصول للمنصة على:');
  
  // محاولة الحصول على رابط المشروع
  try {
    const projectInfo = execSync('firebase projects:list', { encoding: 'utf8' });
    const lines = projectInfo.split('\n');
    const projectLine = lines.find(line => line.includes('alaa-courses') || line.includes('(current)'));
    if (projectLine) {
      const projectId = projectLine.split('│')[1]?.trim();
      if (projectId) {
        console.log(`https://${projectId}.web.app`);
        console.log(`https://${projectId}.firebaseapp.com`);
      }
    }
  } catch (e) {
    console.log('https://your-project-id.web.app');
  }
  
  console.log('\n👨‍💼 بيانات المدير الافتراضية:');
  console.log('البريد: <EMAIL>');
  console.log('كلمة المرور: Admin123!');
  
  console.log('\n👨‍🎓 أكواد الطلاب التجريبية:');
  console.log('123456, 789012');
  
  console.log('\n📱 لإدارة المشروع:');
  console.log('Firebase Console: https://console.firebase.google.com');
  
} catch (error) {
  console.log('\n🎉 النشر مكتمل!');
  console.log('تحقق من Firebase Console للحصول على الرابط');
}
