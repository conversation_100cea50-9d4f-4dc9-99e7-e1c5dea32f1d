-- دوال SQL مساعدة لـ Supabase
-- Skills World Academy - Helper Functions

-- دالة زيادة عدد الطلاب في الكورس
CREATE OR REPLACE FUNCTION increment_course_students(course_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE courses 
    SET enrolled_students = enrolled_students + 1,
        updated_at = NOW()
    WHERE id = course_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تقليل عدد الطلاب في الكورس
CREATE OR REPLACE FUNCTION decrement_course_students(course_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE courses 
    SET enrolled_students = GREATEST(enrolled_students - 1, 0),
        updated_at = NOW()
    WHERE id = course_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة توليد رقم شهادة فريد
CREATE OR REPLACE FUNCTION generate_certificate_number()
RETURNS TEXT AS $$
DECLARE
    cert_number TEXT;
    is_unique BOOLEAN := FALSE;
BEGIN
    WHILE NOT is_unique LOOP
        cert_number := 'CERT-' || TO_CHAR(NOW(), 'YYYY') || '-' || 
                      LPAD(FLOOR(RANDOM() * 999999)::TEXT, 6, '0');
        
        SELECT NOT EXISTS(
            SELECT 1 FROM certificates WHERE certificate_number = cert_number
        ) INTO is_unique;
    END LOOP;
    
    RETURN cert_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة إنشاء شهادة للطالب
CREATE OR REPLACE FUNCTION create_certificate(
    p_student_id UUID,
    p_course_id UUID
)
RETURNS UUID AS $$
DECLARE
    cert_id UUID;
    cert_number TEXT;
BEGIN
    -- التحقق من اكتمال الكورس
    IF NOT EXISTS(
        SELECT 1 FROM enrollments 
        WHERE student_id = p_student_id 
        AND course_id = p_course_id 
        AND status = 'completed'
    ) THEN
        RAISE EXCEPTION 'الطالب لم يكمل الكورس بعد';
    END IF;
    
    -- التحقق من عدم وجود شهادة سابقة
    IF EXISTS(
        SELECT 1 FROM certificates 
        WHERE student_id = p_student_id 
        AND course_id = p_course_id
    ) THEN
        RAISE EXCEPTION 'الطالب حاصل على شهادة لهذا الكورس مسبقاً';
    END IF;
    
    -- توليد رقم شهادة
    cert_number := generate_certificate_number();
    
    -- إنشاء الشهادة
    INSERT INTO certificates (student_id, course_id, certificate_number)
    VALUES (p_student_id, p_course_id, cert_number)
    RETURNING id INTO cert_id;
    
    -- تحديث عدد الشهادات للطالب
    UPDATE users 
    SET certificates_earned = certificates_earned + 1
    WHERE id = p_student_id;
    
    RETURN cert_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة حساب تقدم الطالب في الكورس
CREATE OR REPLACE FUNCTION calculate_course_progress(
    p_student_id UUID,
    p_course_id UUID
)
RETURNS DECIMAL AS $$
DECLARE
    total_videos INTEGER;
    completed_videos INTEGER;
    progress DECIMAL;
BEGIN
    -- جلب العدد الكلي للفيديوهات
    SELECT COUNT(*) INTO total_videos
    FROM course_videos
    WHERE course_id = p_course_id;
    
    IF total_videos = 0 THEN
        RETURN 0;
    END IF;
    
    -- جلب عدد الفيديوهات المكتملة
    SELECT COALESCE(array_length(completed_videos, 1), 0) INTO completed_videos
    FROM enrollments
    WHERE student_id = p_student_id AND course_id = p_course_id;
    
    -- حساب النسبة المئوية
    progress := (completed_videos::DECIMAL / total_videos::DECIMAL) * 100;
    
    -- تحديث التقدم في جدول التسجيلات
    UPDATE enrollments 
    SET progress = progress,
        updated_at = NOW(),
        status = CASE 
            WHEN progress >= 100 THEN 'completed'
            ELSE status
        END
    WHERE student_id = p_student_id AND course_id = p_course_id;
    
    RETURN progress;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تحديث إحصائيات الطالب
CREATE OR REPLACE FUNCTION update_student_stats(p_student_id UUID)
RETURNS void AS $$
DECLARE
    total_courses INTEGER;
    avg_progress DECIMAL;
BEGIN
    -- حساب عدد الكورسات المسجل فيها
    SELECT COUNT(*) INTO total_courses
    FROM enrollments
    WHERE student_id = p_student_id AND status = 'active';
    
    -- حساب متوسط التقدم
    SELECT COALESCE(AVG(progress), 0) INTO avg_progress
    FROM enrollments
    WHERE student_id = p_student_id AND status IN ('active', 'completed');
    
    -- تحديث بيانات الطالب
    UPDATE users 
    SET enrolled_courses = total_courses,
        total_progress = avg_progress,
        updated_at = NOW()
    WHERE id = p_student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة البحث في الكورسات
CREATE OR REPLACE FUNCTION search_courses(search_term TEXT)
RETURNS TABLE(
    id UUID,
    title VARCHAR,
    description TEXT,
    instructor VARCHAR,
    level VARCHAR,
    enrolled_students INTEGER,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT c.id, c.title, c.description, c.instructor, c.level, 
           c.enrolled_students, c.created_at
    FROM courses c
    WHERE c.is_active = TRUE
    AND (
        c.title ILIKE '%' || search_term || '%' OR
        c.description ILIKE '%' || search_term || '%' OR
        c.instructor ILIKE '%' || search_term || '%'
    )
    ORDER BY c.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة جلب إحصائيات لوحة التحكم
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_students', (SELECT COUNT(*) FROM users WHERE role = 'student'),
        'active_students', (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = TRUE),
        'total_courses', (SELECT COUNT(*) FROM courses),
        'active_courses', (SELECT COUNT(*) FROM courses WHERE is_active = TRUE),
        'total_enrollments', (SELECT COUNT(*) FROM enrollments),
        'completed_enrollments', (SELECT COUNT(*) FROM enrollments WHERE status = 'completed'),
        'total_certificates', (SELECT COUNT(*) FROM certificates),
        'total_faqs', (SELECT COUNT(*) FROM faqs WHERE is_active = TRUE),
        'recent_enrollments', (
            SELECT COUNT(*) FROM enrollments 
            WHERE created_at >= NOW() - INTERVAL '7 days'
        ),
        'recent_students', (
            SELECT COUNT(*) FROM users 
            WHERE role = 'student' AND created_at >= NOW() - INTERVAL '7 days'
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة جلب النشاط الأخير
CREATE OR REPLACE FUNCTION get_recent_activity(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
    activity_type TEXT,
    title TEXT,
    description TEXT,
    user_name TEXT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    (
        SELECT 'student_registered'::TEXT, 
               'طالب جديد'::TEXT,
               'تم تسجيل الطالب: ' || u.name,
               u.name,
               u.created_at
        FROM users u
        WHERE u.role = 'student'
        ORDER BY u.created_at DESC
        LIMIT limit_count / 3
    )
    UNION ALL
    (
        SELECT 'course_added'::TEXT,
               'كورس جديد'::TEXT,
               'تم إضافة كورس: ' || c.title,
               c.instructor,
               c.created_at
        FROM courses c
        ORDER BY c.created_at DESC
        LIMIT limit_count / 3
    )
    UNION ALL
    (
        SELECT 'enrollment'::TEXT,
               'تسجيل في كورس'::TEXT,
               'تم تسجيل طالب في كورس',
               ''::TEXT,
               e.created_at
        FROM enrollments e
        ORDER BY e.created_at DESC
        LIMIT limit_count / 3
    )
    ORDER BY created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء فهارس إضافية للأداء
CREATE INDEX IF NOT EXISTS idx_enrollments_progress ON enrollments(progress);
CREATE INDEX IF NOT EXISTS idx_enrollments_status_progress ON enrollments(status, progress);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_courses_created_at ON courses(created_at);
CREATE INDEX IF NOT EXISTS idx_enrollments_created_at ON enrollments(created_at);

-- تفعيل إحصائيات الجداول للأداء الأمثل
ANALYZE users;
ANALYZE courses;
ANALYZE course_videos;
ANALYZE enrollments;
ANALYZE faqs;
ANALYZE certificates;
ANALYZE notifications;
