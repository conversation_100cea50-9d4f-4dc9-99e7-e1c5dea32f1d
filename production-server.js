const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

// Import models
const User = require('./models/User');
const Category = require('./models/Category');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Database connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/alaa-courses';
    
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // Initialize database with default data if empty
    await initializeDatabase();
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    // Don't exit in production, continue with limited functionality
    console.log('⚠️ تشغيل الخادم بدون قاعدة بيانات');
  }
};

// Initialize database with default data
const initializeDatabase = async () => {
  try {
    // Check if admin user exists
    const adminExists = await User.findOne({ role: 'admin' });
    
    if (!adminExists) {
      console.log('🔧 تهيئة قاعدة البيانات بالبيانات الافتراضية...');
      
      // Create admin user
      const adminUser = new User({
        name: 'علاء عبد الحميد',
        email: '<EMAIL>',
        password: 'Admin123!',
        role: 'admin',
        isActive: true
      });
      await adminUser.save();
      console.log('👨‍💼 تم إنشاء حساب المدير الافتراضي');
      
      // Create default categories
      const categories = [
        {
          name: 'التسويق الرقمي',
          description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
          icon: 'digital-marketing',
          color: '#2196F3',
          order: 1,
          isActive: true,
          createdBy: adminUser._id
        },
        {
          name: 'وسائل التواصل الاجتماعي',
          description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
          icon: 'social-media',
          color: '#4CAF50',
          order: 2,
          isActive: true,
          createdBy: adminUser._id
        }
      ];
      
      await Category.insertMany(categories);
      console.log('📂 تم إنشاء الأقسام الافتراضية');
      
      // Create sample students
      const sampleStudents = [
        {
          name: 'أحمد محمد علي',
          studentCode: '123456',
          role: 'student',
          isActive: true,
          createdBy: adminUser._id
        },
        {
          name: 'فاطمة علي حسن',
          studentCode: '789012',
          role: 'student',
          isActive: true,
          createdBy: adminUser._id
        }
      ];
      
      await User.insertMany(sampleStudents);
      console.log('👥 تم إنشاء الطلاب التجريبيين');
      
      console.log('✅ تم تهيئة قاعدة البيانات بنجاح!');
    } else {
      console.log('ℹ️ قاعدة البيانات مهيأة بالفعل');
    }
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error.message);
  }
};

// Import routes
try {
  const authRoutes = require('./routes/auth');
  const adminRoutes = require('./routes/admin');
  const studentRoutes = require('./routes/student');
  
  app.use('/api/auth', authRoutes);
  app.use('/api/admin', adminRoutes);
  app.use('/api/student', studentRoutes);
} catch (error) {
  console.warn('⚠️ تحذير: لم يتم تحميل بعض المسارات:', error.message);
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'الخادم يعمل بنجاح',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
  });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'اختبار API ناجح',
    server: 'منصة كورسات علاء عبد الحميد',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Serve frontend in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'frontend/build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend/build', 'index.html'));
  });
} else {
  // Development welcome page
  app.get('/', (req, res) => {
    res.json({
      message: 'مرحباً بك في منصة كورسات علاء عبد الحميد',
      version: '1.0.0',
      environment: 'development',
      endpoints: {
        health: '/api/health',
        test: '/api/test',
        auth: '/api/auth',
        admin: '/api/admin',
        student: '/api/student'
      },
      defaultCredentials: {
        admin: {
          email: '<EMAIL>',
          password: 'Admin123!'
        },
        student: {
          codes: ['123456', '789012']
        }
      }
    });
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err.message);
  
  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'حدث خطأ في الخادم' 
      : err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    path: req.originalUrl
  });
});

// Start server
const startServer = async () => {
  await connectDB();
  
  app.listen(PORT, () => {
    console.log('🚀 ═══════════════════════════════════════════════════════════');
    console.log(`🎓 منصة كورسات علاء عبد الحميد`);
    console.log(`🌐 الخادم يعمل على المنفذ: ${PORT}`);
    console.log(`📱 البيئة: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 الرابط: http://localhost:${PORT}`);
    console.log(`📊 API Health: http://localhost:${PORT}/api/health`);
    console.log(`🧪 API Test: http://localhost:${PORT}/api/test`);
    console.log('👨‍💼 بيانات المدير: <EMAIL> / Admin123!');
    console.log('👨‍🎓 أكواد الطلاب التجريبية: 123456, 789012');
    console.log('🚀 ═══════════════════════════════════════════════════════════');
  });
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.error('Unhandled Promise Rejection:', err.message);
  // Don't exit in production
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err.message);
  // Don't exit in production
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  if (mongoose.connection.readyState === 1) {
    mongoose.connection.close(() => {
      console.log('Database connection closed.');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

startServer();
