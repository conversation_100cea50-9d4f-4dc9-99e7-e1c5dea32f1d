import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AdminDashboard from '../../components/AdminDashboard';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock Firebase
jest.mock('../../firebase/config', () => ({
  db: {},
  auth: {}
}));

// Mock hooks
jest.mock('../../hooks/useRealtimeData', () => ({
  useRealtimeCourses: () => ({
    courses: [],
    loading: false,
    error: null
  }),
  useRealtimeStudents: () => ({
    students: [],
    loading: false,
    error: null
  }),
  useRealtimeUpdates: () => ({
    courses: { courses: [], loading: false },
    students: { students: [], loading: false },
    isLoading: false,
    hasError: false
  })
}));

jest.mock('../../hooks/useRealtimeNotifications', () => ({
  useRealtimeNotifications: () => ({
    notifications: [],
    unreadCount: 0,
    addNotification: jest.fn(),
    markAsRead: jest.fn(),
    markAllAsRead: jest.fn(),
    removeNotification: jest.fn(),
    clearAllNotifications: jest.fn()
  }),
  useDataChangeNotifications: jest.fn()
}));

jest.mock('../../hooks/usePerformanceMonitor', () => ({
  usePerformanceMonitor: () => ({
    metrics: {
      updateCount: 0,
      averageUpdateTime: 0,
      connectionQuality: 'good',
      errorCount: 0
    },
    recordUpdate: jest.fn(),
    recordError: jest.fn()
  })
}));

const theme = createTheme({
  direction: 'rtl',
  typography: {
    fontFamily: 'Cairo, Arial, sans-serif'
  }
});

// Mock user data
const mockUser = {
  id: 'admin1',
  name: 'علاء عبد الحميد',
  email: '<EMAIL>',
  role: 'admin'
};

const MockAuthProvider = ({ children }) => {
  const mockAuthValue = {
    user: mockUser,
    loading: false,
    loginAdmin: jest.fn(),
    loginStudent: jest.fn(),
    logout: jest.fn(),
    updateUser: jest.fn()
  };

  return (
    <AuthProvider value={mockAuthValue}>
      {children}
    </AuthProvider>
  );
};

const renderAdminDashboard = () => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <MockAuthProvider>
          <AdminDashboard />
        </MockAuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('AdminDashboard Component', () => {
  beforeEach(() => {
    // إعادة تعيين جميع المحاكيات قبل كل اختبار
    jest.clearAllMocks();
  });

  test('renders admin dashboard correctly', () => {
    renderAdminDashboard();
    
    // التحقق من وجود العناصر الأساسية
    expect(screen.getByText('SKILLS WORLD ACADEMY')).toBeInTheDocument();
    expect(screen.getByText('لوحة التحكم')).toBeInTheDocument();
  });

  test('displays navigation menu items', () => {
    renderAdminDashboard();
    
    // التحقق من عناصر القائمة
    expect(screen.getByText('لوحة التحكم')).toBeInTheDocument();
    expect(screen.getByText('إدارة الكورسات')).toBeInTheDocument();
    expect(screen.getByText('إدارة الطلاب')).toBeInTheDocument();
    expect(screen.getByText('أكواد التسجيل')).toBeInTheDocument();
    expect(screen.getByText('تسجيل الطلاب')).toBeInTheDocument();
  });

  test('switches between different sections', async () => {
    renderAdminDashboard();
    
    // النقر على إدارة الطلاب
    const studentsMenuItem = screen.getByText('إدارة الطلاب');
    fireEvent.click(studentsMenuItem);
    
    await waitFor(() => {
      expect(screen.getByText('إدارة الطلاب')).toBeInTheDocument();
    });
  });

  test('displays user profile information', () => {
    renderAdminDashboard();
    
    // التحقق من عرض اسم المستخدم
    expect(screen.getByText(mockUser.name.charAt(0))).toBeInTheDocument();
  });

  test('shows realtime indicator', () => {
    renderAdminDashboard();
    
    // التحقق من وجود مؤشر التحديثات الفورية
    expect(screen.getByText(/متصل/)).toBeInTheDocument();
  });

  test('handles mobile drawer toggle', () => {
    // محاكاة شاشة صغيرة
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 600,
    });

    renderAdminDashboard();
    
    // البحث عن زر القائمة في الشاشات الصغيرة
    const menuButton = screen.getByLabelText(/menu/i);
    expect(menuButton).toBeInTheDocument();
    
    // النقر على زر القائمة
    fireEvent.click(menuButton);
  });

  test('displays notifications correctly', () => {
    renderAdminDashboard();
    
    // التحقق من وجود أيقونة الإشعارات
    const notificationIcon = screen.getByLabelText(/notifications/i);
    expect(notificationIcon).toBeInTheDocument();
  });

  test('handles profile menu interactions', async () => {
    renderAdminDashboard();
    
    // النقر على أيقونة الملف الشخصي
    const profileButton = screen.getByRole('button', { name: /الملف الشخصي/i });
    fireEvent.click(profileButton);
    
    await waitFor(() => {
      expect(screen.getByText('تسجيل الخروج')).toBeInTheDocument();
    });
  });

  test('renders different content based on selected section', async () => {
    renderAdminDashboard();
    
    // اختبار التنقل إلى قسم الكورسات والفيديوهات
    const courseVideosMenuItem = screen.getByText('الكورسات والفيديوهات');
    fireEvent.click(courseVideosMenuItem);
    
    await waitFor(() => {
      // التحقق من تغيير المحتوى
      expect(screen.getByText('الكورسات والفيديوهات')).toBeInTheDocument();
    });
  });

  test('handles responsive design correctly', () => {
    // اختبار الشاشات الكبيرة
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });

    renderAdminDashboard();
    
    // التحقق من عدم وجود زر القائمة في الشاشات الكبيرة
    const menuButtons = screen.queryAllByLabelText(/menu/i);
    expect(menuButtons).toHaveLength(0);
  });

  test('displays academy branding correctly', () => {
    renderAdminDashboard();
    
    // التحقق من عرض اسم الأكاديمية
    expect(screen.getByText('SKILLS WORLD ACADEMY')).toBeInTheDocument();
    expect(screen.getByText('ALAA ABD HAMIED')).toBeInTheDocument();
  });

  test('shows correct RTL layout', () => {
    renderAdminDashboard();
    
    // التحقق من اتجاه النص من اليمين إلى اليسار
    const drawer = screen.getByRole('navigation');
    expect(drawer).toHaveStyle('direction: rtl');
  });
});

describe('AdminDashboard Integration Tests', () => {
  test('integrates with auth context correctly', () => {
    renderAdminDashboard();
    
    // التحقق من عرض بيانات المستخدم الصحيحة
    expect(screen.getByText(mockUser.name.charAt(0))).toBeInTheDocument();
  });

  test('integrates with realtime data hooks', () => {
    renderAdminDashboard();
    
    // التحقق من عمل التحديثات الفورية
    expect(screen.getByText(/متصل/)).toBeInTheDocument();
  });

  test('handles error states gracefully', () => {
    // محاكاة حالة خطأ
    jest.mock('../../hooks/useRealtimeData', () => ({
      useRealtimeUpdates: () => ({
        courses: { courses: [], loading: false },
        students: { students: [], loading: false },
        isLoading: false,
        hasError: true
      })
    }));

    renderAdminDashboard();
    
    // التحقق من التعامل مع الأخطاء
    expect(screen.getByText(/خطأ في الاتصال/)).toBeInTheDocument();
  });
});
