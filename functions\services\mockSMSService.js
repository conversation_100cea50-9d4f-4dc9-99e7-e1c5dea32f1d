/**
 * خدمة SMS وهمية للاختبار والتطوير
 * Mock SMS Service for testing and development
 */
class MockSMSService {
  /**
   * التحقق من توفر خدمة SMS (دائماً متاحة في الوضع الوهمي)
   */
  static isAvailable() {
    return true;
  }

  /**
   * إرسال رسالة SMS وهمية
   * @param {string} to - رقم الهاتف المستقبل
   * @param {string} message - نص الرسالة
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  static async sendSMS(to, message) {
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      // تنسيق رقم الهاتف
      const formattedPhone = this.formatPhoneNumber(to);
      
      console.log('📱 [MOCK SMS] إرسال رسالة وهمية');
      console.log(`📞 إلى: ${formattedPhone}`);
      console.log(`📝 النص: ${message}`);
      console.log('✅ [MOCK SMS] تم الإرسال بنجاح (وهمي)');

      // محاكاة نجاح الإرسال بنسبة 95%
      const isSuccess = Math.random() > 0.05;

      if (isSuccess) {
        const mockMessageId = 'MOCK_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        return {
          success: true,
          messageId: mockMessageId,
          status: 'sent',
          to: formattedPhone,
          sentAt: new Date().toISOString(),
          mock: true,
          message: 'تم إرسال الرسالة بنجاح (وضع الاختبار)'
        };
      } else {
        // محاكاة فشل عشوائي
        throw new Error('فشل في الإرسال (خطأ وهمي للاختبار)');
      }

    } catch (error) {
      console.error('❌ [MOCK SMS] خطأ وهمي في إرسال SMS:', error.message);
      
      return {
        success: false,
        error: error.message,
        code: 'MOCK_SMS_ERROR',
        to: to,
        mock: true
      };
    }
  }

  /**
   * إرسال كلمة المرور للمدير (وهمي)
   * @param {string} phoneNumber - رقم هاتف المدير
   * @param {string} password - كلمة المرور
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  static async sendPasswordReset(phoneNumber, password) {
    const message = this.createPasswordResetMessage(password);
    const result = await this.sendSMS(phoneNumber, message);
    
    // في الوضع الوهمي، نعرض كلمة المرور في الكونسول للاختبار
    if (result.success) {
      console.log('🔐 [MOCK SMS] كلمة المرور المرسلة (للاختبار فقط):');
      console.log(`📱 الرقم: ${phoneNumber}`);
      console.log(`🔑 كلمة المرور: ${password}`);
      console.log('📝 نص الرسالة الكامل:');
      console.log(message);
      console.log('─'.repeat(50));
    }
    
    return result;
  }

  /**
   * إنشاء نص رسالة استرداد كلمة المرور
   * @param {string} password - كلمة المرور
   * @returns {string} نص الرسالة
   */
  static createPasswordResetMessage(password) {
    return `🔐 SKILLS WORLD ACADEMY

كلمة المرور الخاصة بك:
${password}

⚠️ لا تشارك كلمة المرور مع أي شخص
🔒 احتفظ بها في مكان آمن

للدعم: ALAA <EMAIL>
📞 0506747770

[رسالة اختبار - Mock SMS]`;
  }

  /**
   * تنسيق رقم الهاتف للصيغة الدولية
   * @param {string} phoneNumber - رقم الهاتف
   * @returns {string} رقم الهاتف المنسق
   */
  static formatPhoneNumber(phoneNumber) {
    // إزالة المسافات والرموز
    let cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    // تحويل للصيغة الدولية
    if (cleanPhone.startsWith('05')) {
      return '+966' + cleanPhone.substring(1);
    } else if (cleanPhone.startsWith('+966')) {
      return cleanPhone;
    } else if (cleanPhone.startsWith('966')) {
      return '+' + cleanPhone;
    } else if (cleanPhone.startsWith('00966')) {
      return '+' + cleanPhone.substring(2);
    }
    
    return cleanPhone;
  }

  /**
   * التحقق من صحة رقم الهاتف السعودي
   * @param {string} phoneNumber - رقم الهاتف
   * @returns {boolean} صحة الرقم
   */
  static validateSaudiPhone(phoneNumber) {
    const cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    const saudiPatterns = [
      /^05\d{8}$/, // 05xxxxxxxx
      /^\+9665\d{8}$/, // +9665xxxxxxxx
      /^9665\d{8}$/, // 9665xxxxxxxx
      /^00966\d{9}$/ // 00966xxxxxxxxx
    ];
    
    return saudiPatterns.some(pattern => pattern.test(cleanPhone));
  }

  /**
   * إرسال رسالة اختبار
   * @param {string} phoneNumber - رقم الهاتف
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  static async sendTestMessage(phoneNumber) {
    const message = `🧪 رسالة اختبار من SKILLS WORLD ACADEMY

هذه رسالة اختبار وهمية للتأكد من عمل النظام.

الوقت: ${new Date().toLocaleString('ar-SA')}

[وضع الاختبار - Mock SMS Service]`;

    return await this.sendSMS(phoneNumber, message);
  }

  /**
   * الحصول على حالة الرسالة (وهمي)
   * @param {string} messageId - معرف الرسالة
   * @returns {Promise<Object>} حالة الرسالة
   */
  static async getMessageStatus(messageId) {
    // محاكاة تأخير
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // محاكاة حالات مختلفة
    const statuses = ['sent', 'delivered', 'pending'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    
    return {
      success: true,
      status: randomStatus,
      errorCode: null,
      errorMessage: null,
      dateCreated: new Date(),
      dateSent: new Date(),
      dateUpdated: new Date(),
      mock: true
    };
  }

  /**
   * إحصائيات الرسائل المرسلة (وهمي)
   * @param {Date} startDate - تاريخ البداية
   * @param {Date} endDate - تاريخ النهاية
   * @returns {Promise<Object>} إحصائيات الرسائل
   */
  static async getMessagesStats(startDate, endDate) {
    // محاكاة تأخير
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // إنشاء إحصائيات وهمية
    const total = Math.floor(Math.random() * 100) + 10;
    const delivered = Math.floor(total * 0.85);
    const sent = Math.floor(total * 0.1);
    const failed = Math.floor(total * 0.03);
    const pending = total - delivered - sent - failed;
    
    return {
      success: true,
      stats: {
        total,
        sent,
        delivered,
        failed,
        pending
      },
      messages: Array.from({ length: Math.min(total, 10) }, (_, i) => ({
        sid: `MOCK_${Date.now()}_${i}`,
        to: '+966501234567',
        status: ['delivered', 'sent', 'pending'][Math.floor(Math.random() * 3)],
        dateSent: new Date(Date.now() - Math.random() * 86400000),
        errorCode: null
      })),
      mock: true
    };
  }

  /**
   * عرض رسالة تحذيرية للوضع الوهمي
   */
  static showMockWarning() {
    console.log('⚠️ '.repeat(20));
    console.log('🧪 تحذير: يتم استخدام خدمة SMS وهمية للاختبار');
    console.log('📱 لن يتم إرسال رسائل حقيقية');
    console.log('🔧 لتفعيل الإرسال الحقيقي، قم بإعداد Twilio');
    console.log('⚠️ '.repeat(20));
  }
}

module.exports = MockSMSService;
