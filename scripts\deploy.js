#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية النشر...');

// Check if git is initialized
try {
  execSync('git status', { stdio: 'ignore' });
} catch (error) {
  console.log('📝 تهيئة Git...');
  execSync('git init');
  execSync('git add .');
  execSync('git commit -m "Initial commit"');
}

// Build frontend
console.log('🔨 بناء الواجهة الأمامية...');
try {
  execSync('cd frontend && npm install && npm run build', { stdio: 'inherit' });
  console.log('✅ تم بناء الواجهة الأمامية بنجاح');
} catch (error) {
  console.error('❌ خطأ في بناء الواجهة الأمامية:', error.message);
  process.exit(1);
}

// Check environment variables
console.log('🔍 فحص متغيرات البيئة...');
const requiredEnvVars = [
  'MONGODB_URI',
  'JWT_SECRET',
  'NODE_ENV'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.warn('⚠️ متغيرات البيئة المفقودة:', missingVars.join(', '));
  console.log('💡 تأكد من إعداد هذه المتغيرات في منصة النشر');
}

// Create deployment info
const deploymentInfo = {
  timestamp: new Date().toISOString(),
  version: require('../package.json').version,
  environment: process.env.NODE_ENV || 'production',
  buildHash: execSync('git rev-parse HEAD').toString().trim().substring(0, 8)
};

fs.writeFileSync(
  path.join(__dirname, '../deployment-info.json'),
  JSON.stringify(deploymentInfo, null, 2)
);

console.log('📦 معلومات النشر:');
console.log(`   الإصدار: ${deploymentInfo.version}`);
console.log(`   البيئة: ${deploymentInfo.environment}`);
console.log(`   التاريخ: ${deploymentInfo.timestamp}`);
console.log(`   Hash: ${deploymentInfo.buildHash}`);

// Test server startup
console.log('🧪 اختبار تشغيل الخادم...');
try {
  const testProcess = execSync('timeout 10s npm start || true', { 
    stdio: 'pipe',
    timeout: 15000 
  });
  console.log('✅ الخادم يعمل بنجاح');
} catch (error) {
  console.warn('⚠️ تحذير: لم يتم اختبار الخادم بالكامل');
}

// Git operations
console.log('📤 رفع التغييرات...');
try {
  execSync('git add .');
  execSync(`git commit -m "Deploy: ${deploymentInfo.timestamp}"`);
  
  // Check if remote exists
  try {
    execSync('git remote get-url origin', { stdio: 'ignore' });
    execSync('git push origin main');
    console.log('✅ تم رفع الكود بنجاح');
  } catch (error) {
    console.log('💡 لم يتم العثور على remote، يرجى إضافة المستودع:');
    console.log('   git remote add origin <repository-url>');
    console.log('   git push -u origin main');
  }
} catch (error) {
  console.warn('⚠️ تحذير: مشكلة في Git operations');
}

console.log('🎉 تم إعداد المشروع للنشر بنجاح!');
console.log('');
console.log('📋 الخطوات التالية:');
console.log('1. إنشاء حساب على MongoDB Atlas');
console.log('2. إنشاء حساب على Render.com');
console.log('3. ربط المستودع بـ Render');
console.log('4. تكوين متغيرات البيئة');
console.log('5. نشر التطبيق');
console.log('');
console.log('🔗 روابط مفيدة:');
console.log('   MongoDB Atlas: https://cloud.mongodb.com');
console.log('   Render: https://render.com');
console.log('   GitHub: https://github.com');
console.log('');
console.log('👨‍💼 بيانات المدير الافتراضية:');
console.log('   البريد: <EMAIL>');
console.log('   كلمة المرور: Admin123!');
console.log('');
console.log('👨‍🎓 أكواد الطلاب التجريبية:');
console.log('   123456, 789012, 345678');
