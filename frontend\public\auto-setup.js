// إعداد قاعدة البيانات التلقائي
// Auto Database Setup Script

window.autoSetupDatabase = async function() {
  console.log('🚀 بدء الإعداد التلقائي لقاعدة البيانات...');
  console.log('مشروع: Skills World Academy');
  console.log('الموقع: https://marketwise-academy-qhizq.web.app');
  
  try {
    // التحقق من وجود Firebase
    if (typeof firebase === 'undefined') {
      throw new Error('Firebase غير متاح');
    }
    
    const db = firebase.firestore();
    
    // الخطوة 1: مسح البيانات القديمة
    console.log('🗑️ مسح البيانات القديمة...');
    const collections = ['users', 'courses', 'faqs', 'enrollments', 'certificates', 'settings'];
    
    for (const collectionName of collections) {
      try {
        const snapshot = await db.collection(collectionName).get();
        const batch = db.batch();
        
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        
        if (snapshot.docs.length > 0) {
          await batch.commit();
          console.log(`✅ تم مسح ${collectionName} - ${snapshot.docs.length} عنصر`);
        }
      } catch (error) {
        console.log(`⚠️ ${collectionName}: ${error.message}`);
      }
    }
    
    // الخطوة 2: إنشاء المدير
    console.log('👨‍💼 إنشاء حساب المدير...');
    await db.collection('users').add({
      name: 'علاء عبد الحميد',
      email: '<EMAIL>',
      phone: '0506747770',
      role: 'admin',
      isActive: true,
      createdAt: firebase.firestore.FieldValue.serverTimestamp(),
      updatedAt: firebase.firestore.FieldValue.serverTimestamp()
    });
    
    // الخطوة 3: إنشاء الطلاب
    console.log('👨‍🎓 إنشاء الطلاب...');
    const students = [
      { name: 'أحمد محمد علي', email: '<EMAIL>', phone: '0501234567', studentCode: '123456' },
      { name: 'فاطمة أحمد حسن', email: '<EMAIL>', phone: '0507654321', studentCode: '654321' },
      { name: 'محمد عبد الله', email: '<EMAIL>', phone: '0509876543', studentCode: '111111' },
      { name: 'سارة أحمد', email: '<EMAIL>', phone: '0502468135', studentCode: '222222' },
      { name: 'يوسف محمد', email: '<EMAIL>', phone: '0508642097', studentCode: '333333' },
      { name: 'نور الهدى', email: '<EMAIL>', phone: '0501357924', studentCode: '444444' },
      { name: 'خالد أحمد', email: '<EMAIL>', phone: '0509753186', studentCode: '555555' }
    ];
    
    const batch = db.batch();
    students.forEach(student => {
      const docRef = db.collection('users').doc();
      batch.set(docRef, {
        ...student,
        role: 'student',
        isActive: true,
        enrolledCourses: 0,
        completedCourses: 0,
        totalWatchTime: 0,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        updatedAt: firebase.firestore.FieldValue.serverTimestamp()
      });
    });
    
    await batch.commit();
    console.log(`✅ تم إنشاء ${students.length} طالب`);
    
    // الخطوة 4: إنشاء الكورسات
    console.log('📚 إنشاء الكورسات...');
    const courses = [
      {
        title: 'مقدمة في التسويق الرقمي',
        description: 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين. يغطي هذا الكورس جميع جوانب التسويق الرقمي من الأساسيات إلى الاستراتيجيات المتقدمة.',
        instructor: 'علاء عبد الحميد',
        duration: '4 ساعات',
        level: 'مبتدئ',
        price: 0,
        isActive: true,
        enrolledStudents: 0,
        totalVideos: 4,
        videos: [
          { id: 1, title: 'مقدمة في التسويق الرقمي', description: 'نظرة عامة على التسويق الرقمي وأهميته', duration: '15:00', order: 1, isFree: true, videoUrl: '' },
          { id: 2, title: 'استراتيجيات التسويق الرقمي', description: 'تعلم أهم استراتيجيات التسويق الرقمي', duration: '25:00', order: 2, isFree: false, videoUrl: '' },
          { id: 3, title: 'أدوات التسويق الرقمي', description: 'استعراض أهم الأدوات المستخدمة', duration: '20:00', order: 3, isFree: false, videoUrl: '' },
          { id: 4, title: 'قياس النتائج والتحليل', description: 'كيفية قياس نجاح حملاتك التسويقية', duration: '18:00', order: 4, isFree: false, videoUrl: '' }
        ]
      },
      {
        title: 'إدارة وسائل التواصل الاجتماعي',
        description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية وإنشاء محتوى جذاب يحقق أهدافك التسويقية.',
        instructor: 'علاء عبد الحميد',
        duration: '6 ساعات',
        level: 'متوسط',
        price: 0,
        isActive: true,
        enrolledStudents: 0,
        totalVideos: 5,
        videos: [
          { id: 1, title: 'مقدمة في وسائل التواصل', description: 'أساسيات إدارة وسائل التواصل الاجتماعي', duration: '12:00', order: 1, isFree: true, videoUrl: '' },
          { id: 2, title: 'استراتيجية المحتوى', description: 'كيفية إنشاء محتوى جذاب ومؤثر', duration: '22:00', order: 2, isFree: false, videoUrl: '' },
          { id: 3, title: 'التفاعل مع الجمهور', description: 'طرق التفاعل الفعال مع المتابعين', duration: '18:00', order: 3, isFree: false, videoUrl: '' },
          { id: 4, title: 'الإعلانات المدفوعة', description: 'كيفية إنشاء وإدارة الإعلانات المدفوعة', duration: '24:00', order: 4, isFree: false, videoUrl: '' },
          { id: 5, title: 'تحليل الأداء', description: 'قياس وتحليل أداء حساباتك', duration: '16:00', order: 5, isFree: false, videoUrl: '' }
        ]
      },
      {
        title: 'التجارة الإلكترونية للمبتدئين',
        description: 'دليل شامل لبدء متجرك الإلكتروني من الصفر. تعلم كيفية اختيار المنتجات وإنشاء المتجر وتسويقه بفعالية.',
        instructor: 'علاء عبد الحميد',
        duration: '8 ساعات',
        level: 'مبتدئ',
        price: 0,
        isActive: true,
        enrolledStudents: 0,
        totalVideos: 6,
        videos: [
          { id: 1, title: 'مقدمة في التجارة الإلكترونية', description: 'أساسيات التجارة الإلكترونية والفرص المتاحة', duration: '10:00', order: 1, isFree: true, videoUrl: '' },
          { id: 2, title: 'اختيار المنتجات المربحة', description: 'كيفية البحث عن المنتجات المربحة', duration: '20:00', order: 2, isFree: false, videoUrl: '' },
          { id: 3, title: 'إنشاء المتجر الإلكتروني', description: 'خطوات إنشاء متجر إلكتروني احترافي', duration: '28:00', order: 3, isFree: false, videoUrl: '' },
          { id: 4, title: 'استراتيجيات التسويق للمتجر', description: 'طرق تسويق المتجر الإلكتروني', duration: '25:00', order: 4, isFree: false, videoUrl: '' },
          { id: 5, title: 'خدمة العملاء والشحن', description: 'أفضل ممارسات خدمة العملاء', duration: '18:00', order: 5, isFree: false, videoUrl: '' },
          { id: 6, title: 'تحليل المبيعات والأرباح', description: 'كيفية تتبع وتحليل مبيعاتك', duration: '15:00', order: 6, isFree: false, videoUrl: '' }
        ]
      },
      {
        title: 'تحليل البيانات التسويقية',
        description: 'تعلم كيفية جمع وتحليل البيانات التسويقية لاتخاذ قرارات مدروسة وتحسين أداء حملاتك التسويقية.',
        instructor: 'علاء عبد الحميد',
        duration: '5 ساعات',
        level: 'متقدم',
        price: 0,
        isActive: true,
        enrolledStudents: 0,
        totalVideos: 4,
        videos: [
          { id: 1, title: 'مقدمة في تحليل البيانات', description: 'أهمية تحليل البيانات في التسويق', duration: '14:00', order: 1, isFree: true, videoUrl: '' },
          { id: 2, title: 'أدوات جمع البيانات', description: 'استعراض أهم أدوات جمع البيانات', duration: '22:00', order: 2, isFree: false, videoUrl: '' },
          { id: 3, title: 'تحليل سلوك العملاء', description: 'كيفية فهم وتحليل سلوك العملاء', duration: '26:00', order: 3, isFree: false, videoUrl: '' },
          { id: 4, title: 'إنشاء التقارير والرؤى', description: 'كيفية إنشاء تقارير مفيدة', duration: '20:00', order: 4, isFree: false, videoUrl: '' }
        ]
      }
    ];
    
    const coursesBatch = db.batch();
    courses.forEach(course => {
      const docRef = db.collection('courses').doc();
      coursesBatch.set(docRef, {
        ...course,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        updatedAt: firebase.firestore.FieldValue.serverTimestamp()
      });
    });
    
    await coursesBatch.commit();
    console.log(`✅ تم إنشاء ${courses.length} كورس مع ${courses.reduce((total, course) => total + course.totalVideos, 0)} فيديو`);
    
    // الخطوة 5: إنشاء الأسئلة الشائعة
    console.log('❓ إنشاء الأسئلة الشائعة...');
    const faqs = [
      { question: 'كيف يمكنني التسجيل في الكورسات؟', answer: 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير. ادخل الكود في صفحة تسجيل الدخول للطلاب.', category: 'التسجيل', order: 1 },
      { question: 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟', answer: 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات. تقدمك محفوظ ويمكنك العودة من حيث توقفت.', category: 'المشاهدة', order: 2 },
      { question: 'كيف أحصل على شهادة إتمام الكورس؟', answer: 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس بنسبة 100%. الشهادة ستظهر في ملفك الشخصي.', category: 'الشهادات', order: 3 },
      { question: 'ماذا أفعل إذا نسيت كود الطالب؟', answer: 'تواصل مع المدير عبر البريد الإلكتروني ALAA <EMAIL> أو الهاتف 0506747770 للحصول على كود الطالب.', category: 'الدعم', order: 4 },
      { question: 'هل يمكنني تحميل الفيديوهات؟', answer: 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط لحماية حقوق الملكية الفكرية.', category: 'المشاهدة', order: 5 },
      { question: 'كيف يمكنني تتبع تقدمي في الكورس؟', answer: 'يمكنك رؤية تقدمك في لوحة التحكم الخاصة بك. ستجد نسبة الإنجاز لكل كورس مسجل به.', category: 'التقدم', order: 6 },
      { question: 'هل هناك مدة محددة لإنهاء الكورس؟', answer: 'لا، يمكنك إنهاء الكورس في الوقت الذي يناسبك. الوصول للكورس مفتوح بدون قيود زمنية.', category: 'المشاهدة', order: 7 },
      { question: 'كيف يمكنني التواصل مع المدرب؟', answer: 'يمكنك استخدام نظام الدردشة المدمج في المنصة أو التواصل مباشرة عبر بيانات الاتصال المتوفرة.', category: 'الدعم', order: 8 }
    ];
    
    const faqsBatch = db.batch();
    faqs.forEach(faq => {
      const docRef = db.collection('faqs').doc();
      faqsBatch.set(docRef, {
        ...faq,
        isActive: true,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        updatedAt: firebase.firestore.FieldValue.serverTimestamp()
      });
    });
    
    await faqsBatch.commit();
    console.log(`✅ تم إنشاء ${faqs.length} سؤال شائع`);
    
    // الخطوة 6: إنشاء الإعدادات
    console.log('⚙️ إنشاء الإعدادات...');
    await db.collection('settings').add({
      academy_name: 'SKILLS WORLD ACADEMY',
      admin_name: 'علاء عبد الحميد',
      admin_email: 'ALAA <EMAIL>',
      admin_phone: '0506747770',
      academy_version: '2.0.0',
      database_type: 'firestore-enhanced',
      project_id: 'marketwise-academy-qhizq',
      website_url: 'https://marketwise-academy-qhizq.web.app',
      setup_date: firebase.firestore.FieldValue.serverTimestamp(),
      createdAt: firebase.firestore.FieldValue.serverTimestamp(),
      updatedAt: firebase.firestore.FieldValue.serverTimestamp()
    });
    
    // الخطوة 7: إنشاء بعض التسجيلات التجريبية
    console.log('📝 إنشاء تسجيلات تجريبية...');
    const enrollments = [
      { studentCode: '123456', courseTitle: 'مقدمة في التسويق الرقمي', progress: 75, isCompleted: false },
      { studentCode: '654321', courseTitle: 'مقدمة في التسويق الرقمي', progress: 50, isCompleted: false },
      { studentCode: '111111', courseTitle: 'إدارة وسائل التواصل الاجتماعي', progress: 25, isCompleted: false },
      { studentCode: '123456', courseTitle: 'إدارة وسائل التواصل الاجتماعي', progress: 100, isCompleted: true },
      { studentCode: '222222', courseTitle: 'التجارة الإلكترونية للمبتدئين', progress: 60, isCompleted: false }
    ];
    
    const enrollmentsBatch = db.batch();
    enrollments.forEach(enrollment => {
      const docRef = db.collection('enrollments').doc();
      enrollmentsBatch.set(docRef, {
        ...enrollment,
        enrolledAt: firebase.firestore.FieldValue.serverTimestamp(),
        lastAccessed: firebase.firestore.FieldValue.serverTimestamp(),
        completedAt: enrollment.isCompleted ? firebase.firestore.FieldValue.serverTimestamp() : null
      });
    });
    
    await enrollmentsBatch.commit();
    console.log(`✅ تم إنشاء ${enrollments.length} تسجيل تجريبي`);
    
    // الخطوة 8: إنشاء شهادة تجريبية
    console.log('📜 إنشاء شهادة تجريبية...');
    await db.collection('certificates').add({
      studentCode: '123456',
      studentName: 'أحمد محمد علي',
      courseTitle: 'إدارة وسائل التواصل الاجتماعي',
      certificateNumber: 'CERT-2024-001',
      issuedAt: firebase.firestore.FieldValue.serverTimestamp(),
      isValid: true,
      instructor: 'علاء عبد الحميد'
    });
    
    console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('');
    console.log('📊 ملخص ما تم إنشاؤه:');
    console.log('👨‍💼 1 مدير');
    console.log('👨‍🎓 7 طلاب');
    console.log('📚 4 كورسات شاملة');
    console.log('🎥 19 فيديو تعليمي');
    console.log('❓ 8 أسئلة شائعة');
    console.log('📝 5 تسجيلات تجريبية');
    console.log('📜 1 شهادة تجريبية');
    console.log('⚙️ إعدادات النظام');
    
    console.log('');
    console.log('🌐 المشروع جاهز على:');
    console.log('https://marketwise-academy-qhizq.web.app');
    
    console.log('');
    console.log('🔑 بيانات تسجيل الدخول:');
    console.log('👨‍💼 المدير:');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: Admin123!');
    
    console.log('');
    console.log('👨‍🎓 أكواد الطلاب للاختبار:');
    console.log('   أحمد محمد علي: 123456');
    console.log('   فاطمة أحمد حسن: 654321');
    console.log('   محمد عبد الله: 111111');
    console.log('   سارة أحمد: 222222');
    console.log('   يوسف محمد: 333333');
    console.log('   نور الهدى: 444444');
    console.log('   خالد أحمد: 555555');
    
    console.log('');
    console.log('🧪 اختبار النظام:');
    console.log('testStudentLogin("123456") - اختبار تسجيل دخول طالب');
    console.log('checkDatabaseData() - فحص البيانات');
    
    console.log('');
    console.log('✅ النظام جاهز للاستخدام!');
    
    return {
      success: true,
      message: 'تم إعداد قاعدة البيانات بنجاح',
      stats: {
        admins: 1,
        students: 7,
        courses: 4,
        videos: 19,
        faqs: 8,
        enrollments: 5,
        certificates: 1
      }
    };
    
  } catch (error) {
    console.error('❌ خطأ في الإعداد التلقائي:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};

// دالة اختبار تسجيل دخول الطالب
window.testStudentLogin = async function(studentCode) {
  try {
    console.log(`🔍 اختبار تسجيل دخول الطالب بالكود: ${studentCode}`);
    
    const db = firebase.firestore();
    const snapshot = await db.collection('users')
      .where('studentCode', '==', studentCode)
      .where('role', '==', 'student')
      .limit(1)
      .get();
    
    if (snapshot.empty) {
      console.log('❌ لم يتم العثور على طالب بهذا الكود');
      return { success: false, message: 'كود التسجيل غير صحيح' };
    }
    
    const studentDoc = snapshot.docs[0];
    const studentData = studentDoc.data();
    
    if (!studentData.isActive) {
      console.log('❌ حساب الطالب غير مفعل');
      return { success: false, message: 'حساب الطالب غير مفعل' };
    }
    
    console.log('✅ نجح تسجيل الدخول!');
    console.log('بيانات الطالب:', {
      name: studentData.name,
      studentCode: studentData.studentCode,
      email: studentData.email,
      phone: studentData.phone
    });
    
    return {
      success: true,
      user: {
        id: studentDoc.id,
        ...studentData
      }
    };
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تسجيل الدخول:', error);
    return { success: false, message: error.message };
  }
};

// دالة فحص البيانات
window.checkDatabaseData = async function() {
  try {
    console.log('📊 فحص بيانات قاعدة البيانات...');
    
    const db = firebase.firestore();
    
    // عد المدراء
    const adminsSnapshot = await db.collection('users').where('role', '==', 'admin').get();
    const adminsCount = adminsSnapshot.size;
    
    // عد الطلاب
    const studentsSnapshot = await db.collection('users').where('role', '==', 'student').get();
    const studentsCount = studentsSnapshot.size;
    
    // عد الكورسات
    const coursesSnapshot = await db.collection('courses').get();
    const coursesCount = coursesSnapshot.size;
    
    // عد الأسئلة الشائعة
    const faqsSnapshot = await db.collection('faqs').get();
    const faqsCount = faqsSnapshot.size;
    
    // عد التسجيلات
    const enrollmentsSnapshot = await db.collection('enrollments').get();
    const enrollmentsCount = enrollmentsSnapshot.size;
    
    // عد الشهادات
    const certificatesSnapshot = await db.collection('certificates').get();
    const certificatesCount = certificatesSnapshot.size;
    
    const stats = {
      admins: adminsCount,
      students: studentsCount,
      courses: coursesCount,
      faqs: faqsCount,
      enrollments: enrollmentsCount,
      certificates: certificatesCount
    };
    
    console.log('✅ إحصائيات قاعدة البيانات:');
    console.log(`👨‍💼 المدراء: ${stats.admins}`);
    console.log(`👨‍🎓 الطلاب: ${stats.students}`);
    console.log(`📚 الكورسات: ${stats.courses}`);
    console.log(`❓ الأسئلة الشائعة: ${stats.faqs}`);
    console.log(`📝 التسجيلات: ${stats.enrollments}`);
    console.log(`📜 الشهادات: ${stats.certificates}`);
    
    return { success: true, stats };
    
  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
    return { success: false, message: error.message };
  }
};

// تشغيل الإعداد تلقائياً عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // انتظار تحميل Firebase
  setTimeout(() => {
    if (typeof firebase !== 'undefined') {
      console.log('🔥 Firebase جاهز - يمكنك تشغيل autoSetupDatabase()');
      console.log('💡 أو شغّل الإعداد التلقائي الآن بكتابة: autoSetupDatabase()');
    }
  }, 2000);
});

console.log('📜 تم تحميل سكريبت الإعداد التلقائي');
console.log('🚀 لبدء الإعداد، اكتب: autoSetupDatabase()');
