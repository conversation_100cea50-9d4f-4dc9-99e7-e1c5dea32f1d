-- قاعدة بيانات منصة كورسات علاء عبد الحميد - إعداد تلقائي
-- Skills World Academy Database - Auto Setup

-- جدول المستخدمين
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('admin', 'student') NOT NULL,
    student_code VARCHAR(6),
    password_hash VARCHAR(255),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_email (email),
    UNIQUE KEY unique_student_code (student_code),
    INDEX idx_role (role),
    INDEX idx_active (is_active),
    INDEX idx_student_code (student_code)
);

-- جدول الكورسات
CREATE TABLE courses (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    level ENUM('مبتدئ', 'متوسط', 'متقدم') DEFAULT 'مبتدئ',
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    enrolled_students INT DEFAULT 0,
    total_videos INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_instructor (instructor)
);

-- جدول فيديوهات الكورسات
CREATE TABLE course_videos (
    id VARCHAR(36) PRIMARY KEY,
    course_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT,
    duration_minutes INT DEFAULT 0,
    video_order INT NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_course_order (course_id, video_order),
    INDEX idx_course (course_id)
);

-- جدول التسجيلات
CREATE TABLE enrollments (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id)
);

-- جدول الشهادات
CREATE TABLE certificates (
    id VARCHAR(36) PRIMARY KEY,
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    certificate_number VARCHAR(50) NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    certificate_url TEXT,
    is_valid BOOLEAN DEFAULT TRUE,
    
    UNIQUE KEY unique_certificate (student_id, course_id),
    UNIQUE KEY unique_cert_number (certificate_number),
    INDEX idx_student (student_id)
);

-- جدول الأسئلة الشائعة
CREATE TABLE faqs (
    id VARCHAR(36) PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_order (display_order)
);

-- جدول الإعدادات
CREATE TABLE settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_setting_key (setting_key)
);

-- إدراج البيانات الأساسية
INSERT INTO settings (id, setting_key, setting_value, setting_type, description, is_public) VALUES
('set-001', 'academy_name', 'SKILLS WORLD ACADEMY', 'string', 'اسم الأكاديمية', TRUE),
('set-002', 'admin_name', 'علاء عبد الحميد', 'string', 'اسم المدير', TRUE),
('set-003', 'admin_email', 'ALAA <EMAIL>', 'string', 'بريد المدير', FALSE),
('set-004', 'admin_phone', '0506747770', 'string', 'هاتف المدير', FALSE),
('set-005', 'academy_version', '2.0.0', 'string', 'إصدار النظام', TRUE);

-- إنشاء المدير
INSERT INTO users (id, name, email, phone, role, is_active, created_at, updated_at) VALUES
('admin-001', 'علاء عبد الحميد', '<EMAIL>', '0506747770', 'admin', TRUE, NOW(), NOW());

-- إنشاء طلاب تجريبيين
INSERT INTO users (id, name, email, phone, role, student_code, is_active, created_at, updated_at) VALUES
('student-001', 'أحمد محمد علي', '<EMAIL>', '0501234567', 'student', '123456', TRUE, NOW(), NOW()),
('student-002', 'فاطمة أحمد حسن', '<EMAIL>', '0507654321', 'student', '654321', TRUE, NOW(), NOW()),
('student-003', 'محمد عبد الله', '<EMAIL>', '0509876543', 'student', '111111', TRUE, NOW(), NOW()),
('student-004', 'سارة أحمد', '<EMAIL>', '0502468135', 'student', '222222', TRUE, NOW(), NOW()),
('student-005', 'يوسف محمد', '<EMAIL>', '0508642097', 'student', '333333', TRUE, NOW(), NOW());

-- إنشاء أسئلة شائعة
INSERT INTO faqs (id, question, answer, category, display_order, is_active, created_at, updated_at) VALUES
('faq-001', 'كيف يمكنني التسجيل في الكورسات؟', 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير', 'التسجيل', 1, TRUE, NOW(), NOW()),
('faq-002', 'هل يمكنني مشاهدة الكورسات أكثر من مرة؟', 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات', 'المشاهدة', 2, TRUE, NOW(), NOW()),
('faq-003', 'كيف أحصل على شهادة إتمام الكورس؟', 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس', 'الشهادات', 3, TRUE, NOW(), NOW()),
('faq-004', 'ماذا أفعل إذا نسيت كود الطالب؟', 'تواصل مع المدير للحصول على كود الطالب الخاص بك', 'الدعم', 4, TRUE, NOW(), NOW()),
('faq-005', 'هل يمكنني تحميل الفيديوهات؟', 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط', 'المشاهدة', 5, TRUE, NOW(), NOW());

-- إنشاء كورسات تجريبية
INSERT INTO courses (id, title, description, instructor, duration, level, is_active, total_videos, created_at, updated_at) VALUES
('course-001', 'مقدمة في التسويق الرقمي', 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين', 'علاء عبد الحميد', '4 ساعات', 'مبتدئ', TRUE, 3, NOW(), NOW()),
('course-002', 'إدارة وسائل التواصل الاجتماعي', 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية', 'علاء عبد الحميد', '6 ساعات', 'متوسط', TRUE, 4, NOW(), NOW()),
('course-003', 'التجارة الإلكترونية للمبتدئين', 'دليل شامل لبدء متجرك الإلكتروني من الصفر', 'علاء عبد الحميد', '8 ساعات', 'مبتدئ', TRUE, 5, NOW(), NOW());

-- إدراج فيديوهات الكورسات
INSERT INTO course_videos (id, course_id, title, description, duration_minutes, video_order, is_free, created_at, updated_at) VALUES
-- كورس التسويق الرقمي
('video-001', 'course-001', 'مقدمة في التسويق الرقمي', 'نظرة عامة على التسويق الرقمي وأهميته', 15, 1, TRUE, NOW(), NOW()),
('video-002', 'course-001', 'استراتيجيات التسويق', 'تعلم أهم استراتيجيات التسويق الرقمي', 23, 2, FALSE, NOW(), NOW()),
('video-003', 'course-001', 'قياس النتائج', 'كيفية قياس نجاح حملاتك التسويقية', 18, 3, FALSE, NOW(), NOW()),

-- كورس وسائل التواصل
('video-004', 'course-002', 'مقدمة في وسائل التواصل', 'أساسيات إدارة وسائل التواصل الاجتماعي', 12, 1, TRUE, NOW(), NOW()),
('video-005', 'course-002', 'استراتيجية المحتوى', 'كيفية إنشاء محتوى جذاب ومؤثر', 20, 2, FALSE, NOW(), NOW()),
('video-006', 'course-002', 'التفاعل مع الجمهور', 'طرق التفاعل الفعال مع المتابعين', 16, 3, FALSE, NOW(), NOW()),
('video-007', 'course-002', 'تحليل الأداء', 'قياس وتحليل أداء حساباتك', 14, 4, FALSE, NOW(), NOW()),

-- كورس التجارة الإلكترونية
('video-008', 'course-003', 'مقدمة في التجارة الإلكترونية', 'أساسيات التجارة الإلكترونية', 10, 1, TRUE, NOW(), NOW()),
('video-009', 'course-003', 'اختيار المنتجات', 'كيفية اختيار المنتجات المربحة', 18, 2, FALSE, NOW(), NOW()),
('video-010', 'course-003', 'إنشاء المتجر', 'خطوات إنشاء متجر إلكتروني احترافي', 25, 3, FALSE, NOW(), NOW()),
('video-011', 'course-003', 'التسويق للمتجر', 'استراتيجيات تسويق المتجر الإلكتروني', 22, 4, FALSE, NOW(), NOW()),
('video-012', 'course-003', 'خدمة العملاء', 'أفضل ممارسات خدمة العملاء', 15, 5, FALSE, NOW(), NOW());

-- تسجيل بعض الطلاب في الكورسات
INSERT INTO enrollments (id, student_id, course_id, enrolled_at, progress_percentage) VALUES
('enroll-001', 'student-001', 'course-001', NOW(), 75.0),
('enroll-002', 'student-002', 'course-001', NOW(), 50.0),
('enroll-003', 'student-003', 'course-002', NOW(), 25.0),
('enroll-004', 'student-001', 'course-002', NOW(), 100.0),
('enroll-005', 'student-004', 'course-003', NOW(), 60.0);

-- إنشاء شهادات للطلاب المكملين
INSERT INTO certificates (id, student_id, course_id, certificate_number, issued_at, is_valid) VALUES
('cert-001', 'student-001', 'course-002', 'CERT-2024-001', NOW(), TRUE);
