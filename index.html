<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>علاء عبد الحميد - منصة الكورسات</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 2rem;
        }
        h1 { font-size: 3.5rem; margin-bottom: 1rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { font-size: 1.8rem; margin-bottom: 2rem; opacity: 0.9; }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .feature:hover { 
            transform: translateY(-10px) scale(1.02);
            background: rgba(255,255,255,0.15);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .feature h3 { 
            margin-bottom: 1rem; 
            color: #FFD700; 
            font-size: 1.3rem;
        }
        .feature p {
            line-height: 1.6;
            opacity: 0.9;
        }
        .status {
            background: rgba(76, 175, 80, 0.2);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 2px solid rgba(76, 175, 80, 0.5);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
            70% { box-shadow: 0 0 0 15px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }
        .login-info {
            background: rgba(33, 150, 243, 0.2);
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 2px solid rgba(33, 150, 243, 0.5);
        }
        .login-info h3 { color: #64B5F6; margin-bottom: 1.5rem; font-size: 1.4rem; }
        .credentials { 
            text-align: right; 
            margin: 1.5rem 0; 
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 10px;
        }
        .tech-stack {
            background: rgba(156, 39, 176, 0.2);
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 2px solid rgba(156, 39, 176, 0.5);
        }
        .tech-stack h3 { color: #BA68C8; margin-bottom: 1.5rem; font-size: 1.4rem; }
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        .tech-item {
            background: rgba(255,255,255,0.15);
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s;
        }
        .tech-item:hover {
            background: rgba(255,255,255,0.25);
            transform: scale(1.1);
        }
        .success-banner {
            background: rgba(255, 193, 7, 0.2);
            padding: 1.5rem;
            border-radius: 15px;
            margin-top: 2rem;
            border: 2px solid rgba(255, 193, 7, 0.5);
            font-size: 1.1rem;
            font-weight: 600;
        }
        .demo-section {
            background: rgba(255, 87, 34, 0.2);
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 2px solid rgba(255, 87, 34, 0.5);
        }
        .demo-section h3 { color: #FF8A65; margin-bottom: 1rem; }
        .demo-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        .btn {
            background: linear-gradient(45deg, #2196F3 30%, #21CBF3 90%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
            font-weight: 600;
        }
        .btn:hover { 
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .btn.admin { background: linear-gradient(45deg, #FF5722 30%, #FF8A65 90%); }
        .btn.student { background: linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 علاء عبد الحميد</h1>
        <h2>منصة كورسات التسويق المتقدمة</h2>
        
        <div class="status">
            <h3>✅ المشروع مكتمل وجاهز للتشغيل!</h3>
            <p>جميع الأنظمة والمميزات تم تطويرها بنجاح</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🔐 نظام المصادقة المتقدم</h3>
                <p>• تسجيل دخول آمن للمديرين بالإيميل وكلمة المرور</p>
                <p>• تسجيل دخول للطلاب بأكواد 6 أرقام</p>
                <p>• حماية JWT وتشفير البيانات</p>
            </div>
            <div class="feature">
                <h3>📚 إدارة الكورسات الشاملة</h3>
                <p>• إضافة وتعديل وحذف الكورسات</p>
                <p>• تنظيم الكورسات في أقسام</p>
                <p>• رفع الفيديوهات عالية الجودة</p>
                <p>• إدارة الدروس والمحتوى</p>
            </div>
            <div class="feature">
                <h3>👥 إدارة الطلاب المتطورة</h3>
                <p>• إنشاء أكواد دخول فريدة للطلاب</p>
                <p>• تتبع تقدم الطلاب في الوقت الفعلي</p>
                <p>• إحصائيات مفصلة ولوحات تحكم</p>
                <p>• إدارة التسجيل والحضور</p>
            </div>
            <div class="feature">
                <h3>🏆 نظام الشهادات الاحترافي</h3>
                <p>• إصدار شهادات تلقائية عند الإكمال</p>
                <p>• تصميم احترافي وجميل للشهادات</p>
                <p>• تحميل PDF عالي الجودة</p>
                <p>• أكواد تحقق للشهادات</p>
            </div>
            <div class="feature">
                <h3>📊 لوحة تحكم جمالية</h3>
                <p>• واجهة عربية متجاوبة وحديثة</p>
                <p>• إحصائيات في الوقت الفعلي</p>
                <p>• تصميم Material-UI أنيق</p>
                <p>• تجربة مستخدم ممتازة</p>
            </div>
            <div class="feature">
                <h3>🎥 مشغل فيديو متقدم</h3>
                <p>• مشاهدة بجودة عالية ومتعددة</p>
                <p>• تتبع التقدم والوقت المشاهد</p>
                <p>• استكمال من حيث توقف الطالب</p>
                <p>• تحكم كامل في السرعة والجودة</p>
            </div>
        </div>

        <div class="login-info">
            <h3>🔑 بيانات تسجيل الدخول التجريبية:</h3>
            <div class="credentials">
                <strong>👨‍💼 المدير:</strong><br>
                📧 البريد الإلكتروني: <EMAIL><br>
                🔒 كلمة المرور: Admin123!
            </div>
            <div class="credentials">
                <strong>👨‍🎓 الطلاب:</strong><br>
                🔢 يتم إنشاء أكواد 6 أرقام من قبل المدير<br>
                📝 مثال: 123456, 789012
            </div>
        </div>

        <div class="demo-section">
            <h3>🚀 تشغيل المشروع:</h3>
            <p>لتشغيل المشروع كاملاً، استخدم الأوامر التالية:</p>
            <div class="demo-buttons">
                <code style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; display: block; margin: 10px 0;">
                    npm install && npm start
                </code>
                <code style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; display: block; margin: 10px 0;">
                    docker-compose up -d
                </code>
            </div>
        </div>

        <div class="tech-stack">
            <h3>🛠️ التقنيات والأدوات المستخدمة:</h3>
            <div class="tech-list">
                <span class="tech-item">Node.js</span>
                <span class="tech-item">Express.js</span>
                <span class="tech-item">MongoDB</span>
                <span class="tech-item">Mongoose</span>
                <span class="tech-item">React.js</span>
                <span class="tech-item">Material-UI</span>
                <span class="tech-item">JWT</span>
                <span class="tech-item">Bcrypt</span>
                <span class="tech-item">Docker</span>
                <span class="tech-item">PDFKit</span>
                <span class="tech-item">Multer</span>
                <span class="tech-item">Helmet</span>
            </div>
        </div>

        <div class="success-banner">
            <strong>🎉 تم إنجاز المشروع بنجاح!</strong><br>
            منصة كورسات علاء عبد الحميد جاهزة للاستخدام مع جميع المميزات المطلوبة:<br>
            ✅ نظام مصادقة آمن | ✅ إدارة كورسات متقدمة | ✅ تتبع تقدم الطلاب | ✅ إصدار شهادات | ✅ واجهة جميلة ومتجاوبة
        </div>
    </div>
</body>
</html>
