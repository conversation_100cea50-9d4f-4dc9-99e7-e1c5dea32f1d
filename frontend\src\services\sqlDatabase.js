// خدمة قاعدة البيانات SQL المتكاملة
// متوافقة مع PlanetScale, Railway, Supabase, Google Cloud SQL

/**
 * إعداد الاتصال بقاعدة البيانات
 */
const DB_CONFIG = {
  // إعدادات قاعدة البيانات من متغيرات البيئة
  host: process.env.REACT_APP_DB_HOST || 'aws.connect.psdb.cloud',
  user: process.env.REACT_APP_DB_USER,
  password: process.env.REACT_APP_DB_PASSWORD,
  database: process.env.REACT_APP_DB_NAME || 'skills-world-academy',
  port: process.env.REACT_APP_DB_PORT || 3306,
  ssl: { rejectUnauthorized: false }, // مطلوب لـ PlanetScale

  // إعدادات إضافية للأداء
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

/**
 * تنفيذ استعلام SQL عبر API
 */
const executeQuery = async (query, params = []) => {
  try {
    const response = await fetch('/api/sql/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({ query, params })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

/**
 * خدمات إدارة الطلاب
 */
export const studentSQLService = {
  // إنشاء طالب جديد
  async createStudent(studentData) {
    const studentCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    const query = `
      INSERT INTO users (name, email, phone, role, student_code, is_active, created_at, updated_at)
      VALUES (?, ?, ?, 'student', ?, TRUE, NOW(), NOW())
    `;
    
    const params = [
      studentData.name,
      studentData.email || null,
      studentData.phone || null,
      studentCode
    ];

    const result = await executeQuery(query, params);
    
    return {
      id: result.insertId,
      ...studentData,
      studentCode,
      role: 'student',
      isActive: true,
      createdAt: new Date()
    };
  },

  // جلب جميع الطلاب
  async getAllStudents() {
    const query = `
      SELECT 
        u.*,
        COUNT(e.id) as enrolled_courses,
        COUNT(c.id) as completed_courses,
        COALESCE(SUM(vp.watch_time_seconds), 0) / 60 as total_watch_minutes
      FROM users u
      LEFT JOIN enrollments e ON u.id = e.student_id
      LEFT JOIN certificates c ON u.id = c.student_id
      LEFT JOIN video_progress vp ON u.id = vp.student_id
      WHERE u.role = 'student'
      GROUP BY u.id
      ORDER BY u.created_at DESC
    `;

    const result = await executeQuery(query);
    return result.data || [];
  },

  // البحث عن طالب بالكود
  async findStudentByCode(studentCode) {
    const query = `
      SELECT * FROM users 
      WHERE student_code = ? AND role = 'student'
      LIMIT 1
    `;

    const result = await executeQuery(query, [studentCode]);
    return result.data && result.data.length > 0 ? result.data[0] : null;
  },

  // تحديث بيانات طالب
  async updateStudent(studentId, updateData) {
    const fields = [];
    const params = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(updateData[key]);
      }
    });

    fields.push('updated_at = NOW()');
    params.push(studentId);

    const query = `
      UPDATE users 
      SET ${fields.join(', ')}
      WHERE id = ? AND role = 'student'
    `;

    await executeQuery(query, params);
    return true;
  },

  // حذف طالب
  async deleteStudent(studentId) {
    // حذف البيانات المرتبطة أولاً (بسبب CASCADE في قاعدة البيانات)
    const query = 'DELETE FROM users WHERE id = ? AND role = "student"';
    await executeQuery(query, [studentId]);
    return true;
  },

  // تفعيل/إلغاء تفعيل طالب
  async toggleStudentStatus(studentId, isActive) {
    const query = `
      UPDATE users 
      SET is_active = ?, updated_at = NOW()
      WHERE id = ? AND role = 'student'
    `;

    await executeQuery(query, [isActive, studentId]);
    return true;
  }
};

/**
 * خدمات إدارة الكورسات
 */
export const courseSQLService = {
  // إنشاء كورس جديد
  async createCourse(courseData) {
    const query = `
      INSERT INTO courses (title, description, instructor, duration, level, price, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
    `;

    const params = [
      courseData.title,
      courseData.description,
      courseData.instructor,
      courseData.duration,
      courseData.level || 'مبتدئ',
      courseData.price || 0
    ];

    const result = await executeQuery(query, params);
    
    return {
      id: result.insertId,
      ...courseData,
      isActive: true,
      enrolledStudents: 0,
      totalVideos: 0,
      createdAt: new Date()
    };
  },

  // جلب جميع الكورسات
  async getAllCourses() {
    const query = `
      SELECT 
        c.*,
        COUNT(DISTINCT e.student_id) as enrolled_students,
        COUNT(DISTINCT cv.id) as total_videos
      FROM courses c
      LEFT JOIN enrollments e ON c.id = e.course_id
      LEFT JOIN course_videos cv ON c.id = cv.course_id
      GROUP BY c.id
      ORDER BY c.created_at DESC
    `;

    const result = await executeQuery(query);
    return result.data || [];
  },

  // إضافة فيديو لكورس
  async addVideoToCourse(courseId, videoData) {
    const query = `
      INSERT INTO course_videos (course_id, title, description, video_url, duration_minutes, video_order, is_free, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const params = [
      courseId,
      videoData.title,
      videoData.description,
      videoData.videoUrl || '',
      videoData.durationMinutes || 0,
      videoData.order || 1,
      videoData.isFree || false
    ];

    const result = await executeQuery(query, params);
    return { id: result.insertId, ...videoData };
  }
};

/**
 * خدمات المصادقة
 */
export const authSQLService = {
  // تسجيل دخول الطالب
  async loginStudent(studentCode) {
    const student = await studentSQLService.findStudentByCode(studentCode);
    
    if (!student) {
      throw new Error('كود التسجيل غير صحيح');
    }

    if (!student.is_active) {
      throw new Error('حساب الطالب غير مفعل');
    }

    // تحديث آخر تسجيل دخول
    await executeQuery(
      'UPDATE users SET last_login = NOW() WHERE id = ?',
      [student.id]
    );

    return {
      success: true,
      user: {
        id: student.id,
        name: student.name,
        email: student.email,
        phone: student.phone,
        role: student.role,
        studentCode: student.student_code,
        isActive: student.is_active
      }
    };
  },

  // تسجيل دخول المدير
  async loginAdmin(email, password) {
    // في النظام الحقيقي، يجب التحقق من كلمة المرور المشفرة
    if (email === '<EMAIL>' && password === 'Admin123!') {
      const query = 'SELECT * FROM users WHERE email = ? AND role = "admin" LIMIT 1';
      const result = await executeQuery(query, [email]);
      
      if (result.data && result.data.length > 0) {
        const admin = result.data[0];
        
        // تحديث آخر تسجيل دخول
        await executeQuery(
          'UPDATE users SET last_login = NOW() WHERE id = ?',
          [admin.id]
        );

        return {
          success: true,
          user: {
            id: admin.id,
            name: admin.name,
            email: admin.email,
            phone: admin.phone,
            role: admin.role,
            isActive: admin.is_active
          }
        };
      }
    }

    throw new Error('بيانات تسجيل الدخول غير صحيحة');
  }
};

/**
 * خدمات الإحصائيات
 */
export const statsSQLService = {
  // إحصائيات عامة
  async getGeneralStats() {
    const queries = [
      'SELECT COUNT(*) as total FROM users WHERE role = "student"',
      'SELECT COUNT(*) as total FROM users WHERE role = "student" AND is_active = TRUE',
      'SELECT COUNT(*) as total FROM courses WHERE is_active = TRUE',
      'SELECT COUNT(*) as total FROM enrollments',
      'SELECT COUNT(*) as total FROM certificates'
    ];

    const results = await Promise.all(
      queries.map(query => executeQuery(query))
    );

    return {
      totalStudents: results[0].data[0].total,
      activeStudents: results[1].data[0].total,
      totalCourses: results[2].data[0].total,
      totalEnrollments: results[3].data[0].total,
      totalCertificates: results[4].data[0].total
    };
  },

  // أحدث الأنشطة
  async getRecentActivities(limit = 10) {
    const query = `
      SELECT 
        al.*,
        u.name as user_name,
        u.role as user_role
      FROM activity_logs al
      JOIN users u ON al.user_id = u.id
      ORDER BY al.created_at DESC
      LIMIT ?
    `;

    const result = await executeQuery(query, [limit]);
    return result.data || [];
  }
};

/**
 * اختبار الاتصال بقاعدة البيانات
 */
export const testDatabaseConnection = async () => {
  try {
    console.log('🔍 اختبار الاتصال بقاعدة البيانات SQL...');
    
    const result = await executeQuery('SELECT 1 as test');
    
    if (result.success) {
      console.log('✅ الاتصال بقاعدة البيانات SQL ناجح');
      
      // اختبار الجداول
      const tablesResult = await executeQuery('SHOW TABLES');
      console.log(`📊 عدد الجداول: ${tablesResult.data.length}`);
      
      return { success: true, message: 'الاتصال ناجح' };
    } else {
      throw new Error('فشل في الاتصال');
    }
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    return { success: false, message: error.message };
  }
};

// تصدير الخدمات للاستخدام في وحدة التحكم
window.studentSQLService = studentSQLService;
window.courseSQLService = courseSQLService;
window.authSQLService = authSQLService;
window.statsSQLService = statsSQLService;
window.testDatabaseConnection = testDatabaseConnection;

export default {
  student: studentSQLService,
  course: courseSQLService,
  auth: authSQLService,
  stats: statsSQLService,
  testConnection: testDatabaseConnection
};
