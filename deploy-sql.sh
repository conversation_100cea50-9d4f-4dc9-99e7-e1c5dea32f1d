#!/bin/bash

# سكريبت النشر التلقائي لقاعدة البيانات SQL
# Auto deployment script for SQL database
# مشروع: marketwise-academy-qhizq

echo "🚀 بدء نشر قاعدة البيانات SQL لمشروع Skills World Academy..."
echo "Starting SQL database deployment for marketwise-academy-qhizq..."

# التحقق من وجود Firebase CLI
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI غير مثبت. يرجى تثبيته أولاً:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيته أولاً"
    exit 1
fi

echo "✅ Firebase CLI و Node.js متوفران"

# الانتقال إلى مجلد functions
echo "📁 الانتقال إلى مجلد functions..."
cd functions

# تثبيت التبعيات
echo "📦 تثبيت تبعيات Functions..."
npm install

# التحقق من وجود mysql2
if ! npm list mysql2 &> /dev/null; then
    echo "📦 تثبيت mysql2..."
    npm install mysql2
fi

echo "✅ تم تثبيت جميع التبعيات"

# العودة إلى المجلد الرئيسي
cd ..

# الانتقال إلى مجلد frontend
echo "📁 الانتقال إلى مجلد frontend..."
cd frontend

# تثبيت تبعيات frontend
echo "📦 تثبيت تبعيات Frontend..."
npm install

# بناء المشروع
echo "🔨 بناء المشروع..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ فشل في بناء المشروع"
    exit 1
fi

echo "✅ تم بناء المشروع بنجاح"

# العودة إلى المجلد الرئيسي
cd ..

# نشر Functions
echo "🚀 نشر Firebase Functions..."
firebase deploy --only functions

if [ $? -ne 0 ]; then
    echo "❌ فشل في نشر Functions"
    exit 1
fi

echo "✅ تم نشر Functions بنجاح"

# نشر Hosting
echo "🌐 نشر Firebase Hosting..."
firebase deploy --only hosting

if [ $? -ne 0 ]; then
    echo "❌ فشل في نشر Hosting"
    exit 1
fi

echo "✅ تم نشر Hosting بنجاح"

# عرض الروابط
echo ""
echo "🎉 تم النشر بنجاح!"
echo "================================"
echo "🌐 رابط الموقع: https://marketwise-academy-qhizq.web.app"
echo "⚡ Functions URL: https://us-central1-marketwise-academy-qhizq.cloudfunctions.net"
echo ""
echo "📋 الخطوات التالية:"
echo "1. افتح الموقع في المتصفح"
echo "2. اضغط F12 لفتح وحدة التحكم"
echo "3. شغّل الأمر: autoSQLSetup()"
echo "4. انتظر حتى يكتمل الإعداد"
echo "5. سجّل دخول كمدير: <EMAIL> / Admin123!"
echo ""
echo "🔑 أكواد الطلاب للاختبار:"
echo "- أحمد محمد علي: 123456"
echo "- فاطمة أحمد حسن: 654321"
echo "- محمد عبد الله: 111111"
echo "- سارة أحمد: 222222"
echo "- يوسف محمد: 333333"
echo ""
echo "✅ النشر مكتمل!"
