rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - السماح بالوصول للجميع مؤقتاً
    match /users/{userId} {
      allow read, write: if true;
    }

    // Categories collection
    match /categories/{categoryId} {
      allow read, write: if true;
    }

    // Courses collection
    match /courses/{courseId} {
      allow read, write: if true;
    }

    // Progress collection
    match /progress/{progressId} {
      allow read, write: if true;
    }

    // User Progress collection
    match /userProgress/{progressId} {
      allow read, write: if true;
    }

    // Certificates collection
    match /certificates/{certificateId} {
      allow read, write: if true;
    }

    // Videos collection
    match /videos/{videoId} {
      allow read, write: if true;
    }

    // Settings collection
    match /settings/{settingId} {
      allow read, write: if true;
    }

    // Stats collection
    match /stats/{statId} {
      allow read, write: if true;
    }

    // Enrollments collection
    match /enrollments/{enrollmentId} {
      allow read, write: if true;
    }

    // Activities collection
    match /activities/{activityId} {
      allow read, write: if true;
    }

    // Default rule for any other collections
    match /{document=**} {
      allow read, write: if true;
    }
  }
}