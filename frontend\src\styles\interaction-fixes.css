/* إصلاحات التفاعل للوحة التحكم الإدارية */

/* إصلاح عام للتفاعل */
.admin-dashboard {
  /* إزالة تأخير النقر على iOS */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;

  /* تحسين الأداء */
  will-change: auto;
  transform: translateZ(0);
  backface-visibility: hidden;

  /* ضمان التجاوب */
  width: 100% !important;
  max-width: 100vw !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

/* إصلاح الأزرار والعناصر التفاعلية */
.admin-dashboard .MuiIconButton-root,
.admin-dashboard .MuiListItemButton-root,
.admin-dashboard .MuiButton-root,
.admin-dashboard .MuiMenuItem-root {
  /* تحسين التفاعل */
  touch-action: manipulation !important;
  user-select: none !important;
  cursor: pointer !important;
  
  /* إزالة تأخير النقر */
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  
  /* تحسين الاستجابة */
  transition: all 0.2s ease !important;
  
  /* ضمان الحد الأدنى للحجم */
  min-height: 44px !important;
  min-width: 44px !important;
}

/* تحسين hover للأجهزة التي تدعمها */
@media (hover: hover) and (pointer: fine) {
  .admin-dashboard .MuiIconButton-root:hover,
  .admin-dashboard .MuiListItemButton-root:hover,
  .admin-dashboard .MuiButton-root:hover {
    transform: scale(1.02) !important;
    transition: transform 0.2s ease !important;
  }
}

/* تحسين active state لجميع الأجهزة */
.admin-dashboard .MuiIconButton-root:active,
.admin-dashboard .MuiListItemButton-root:active,
.admin-dashboard .MuiButton-root:active {
  transform: scale(0.98) !important;
  transition: transform 0.1s ease !important;
  opacity: 0.8 !important;
}

/* إصلاح القائمة الجانبية */
.admin-dashboard .MuiList-root {
  /* تحسين التمرير */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

.admin-dashboard .MuiListItem-root {
  /* تحسين التفاعل */
  touch-action: manipulation !important;
  cursor: pointer !important;
}

/* إصلاح الـ Drawer */
.admin-dashboard .MuiDrawer-paper {
  /* تحسين الأداء */
  will-change: transform !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
  
  /* تحسين التمرير */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* إصلاح الشريط العلوي */
.admin-dashboard .MuiAppBar-root {
  /* تحسين الأداء */
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
}

.admin-dashboard .MuiToolbar-root {
  /* تحسين التفاعل */
  touch-action: manipulation !important;
}

/* إصلاحات للأجهزة المحمولة */
@media (max-width: 899px) {
  .admin-dashboard {
    /* تحسين الأداء للأجهزة المحمولة */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }
  
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام أكبر للأجهزة المحمولة */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
    
    /* تحسين الاستجابة */
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
  }
  
  /* تحسين القائمة للأجهزة المحمولة */
  .admin-dashboard .MuiDrawer-paper {
    width: 280px !important;
    max-width: 85vw !important;
  }
  
  /* تحسين النصوص للأجهزة المحمولة */
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
  }
  
  .admin-dashboard .MuiListItemText-primary {
    font-size: 0.95rem !important;
    line-height: 1.3 !important;
  }
}

/* إصلاحات للأجهزة اللوحية */
@media (min-width: 900px) and (max-width: 1199px) {
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام محسنة للأجهزة اللوحية */
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 14px !important;
  }
  
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.2rem !important;
  }
  
  .admin-dashboard .MuiListItemText-primary {
    font-size: 1rem !important;
  }
}

/* إصلاحات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام مثالية للشاشات الكبيرة */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
  }
  
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.3rem !important;
  }
  
  .admin-dashboard .MuiListItemText-primary {
    font-size: 1rem !important;
  }
}

/* إصلاح مشاكل z-index */
.admin-dashboard .MuiDrawer-root {
  z-index: 1300 !important;
}

.admin-dashboard .MuiAppBar-root {
  z-index: 1200 !important;
}

.admin-dashboard .MuiBackdrop-root {
  z-index: 1250 !important;
}

/* إصلاح التمرير */
.admin-dashboard {
  overflow-x: hidden !important;
}

.admin-dashboard .MuiContainer-root {
  overflow-x: hidden !important;
  width: 100% !important;
  max-width: none !important;
}

/* إصلاح النصوص القابلة للتحديد */
.admin-dashboard .MuiTypography-root,
.admin-dashboard .MuiListItemText-root,
.admin-dashboard input,
.admin-dashboard textarea {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* إصلاح الفوكس */
.admin-dashboard .MuiIconButton-root:focus,
.admin-dashboard .MuiListItemButton-root:focus,
.admin-dashboard .MuiButton-root:focus {
  outline: 2px solid #0000FF !important;
  outline-offset: 2px !important;
}

/* إصلاح الانتقالات */
.admin-dashboard * {
  transition-duration: 0.2s !important;
  transition-timing-function: ease-out !important;
}

/* إصلاح للأجهزة ذات الشاشات اللمسية */
@media (pointer: coarse) {
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام أكبر للأجهزة اللمسية */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px !important;
  }
}

/* إصلاح مشاكل الأداء */
.admin-dashboard .performance-critical {
  will-change: transform !important;
  contain: layout style paint !important;
}

/* إصلاح مشاكل الذاكرة */
.admin-dashboard .memory-optimized {
  will-change: auto !important;
  contain: none !important;
}

/* إصلاح للوضع الليلي */
@media (prefers-color-scheme: dark) {
  .admin-dashboard .MuiIconButton-root:active,
  .admin-dashboard .MuiListItemButton-root:active,
  .admin-dashboard .MuiButton-root:active {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}

/* إصلاح للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .admin-dashboard * {
    animation: none !important;
    transition: none !important;
  }
}

/* إجبار التجاوب على جميع الأجهزة */
@media screen {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    min-height: 100vh !important;
    width: 100% !important;
    position: relative !important;
  }

  /* إجبار إخفاء/إظهار العناصر حسب حجم الشاشة */
  @media (max-width: 899px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-docked {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: 100% !important;
      left: 0 !important;
      right: 0 !important;
    }

    .admin-dashboard main {
      margin-right: 0 !important;
      margin-left: 0 !important;
      width: 100% !important;
    }
  }

  @media (min-width: 900px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-temporary {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: calc(100% - 300px) !important;
      right: 0 !important;
      left: auto !important;
    }

    .admin-dashboard main {
      margin-right: 300px !important;
      margin-left: 0 !important;
      width: calc(100% - 300px) !important;
    }
  }
}
