# تقرير إصلاح مشاكل قاعدة البيانات
# Database Schema Fixes Report

**التاريخ**: 2025-07-11  
**المشروع**: Skills World Academy  
**قاعدة البيانات**: Supabase (auwpeiicfwcysoexoogf)

## 📋 ملخص المشاكل التي تم حلها

### ❌ المشاكل الأصلية:
1. **خطأ في العلاقة بين جدولي enrollments و users**
   - رسالة الخطأ: "Could not find a relationship between 'enrollments' and 'users' in the schema cache"
   - السبب: الكود يبحث عن `enrollments_student_id_fkey` بينما الجدول يحتوي على `enrollments_user_id_fkey`

2. **عمود priority مفقود في جدول faqs**
   - رسالة الخطأ: "column faqs.priority does not exist"
   - السبب: الكود يحاول ترتيب الأسئلة الشائعة حسب الأولوية لكن العمود غير موجود

3. **Storage Buckets مفقودة**
   - المشكلة: 4 buckets مطلوبة لكن bucket واحد مفقود (certificates)
   - التأثير: فشل في رفع ملفات الشهادات

## ✅ الحلول المطبقة

### 1. إصلاح مشكلة العلاقات في جدول enrollments

**المشكلة**: 
- الجدول يحتوي على عمود `user_id` 
- الكود يتوقع عمود `student_id`
- Foreign key constraint اسمه `enrollments_user_id_fkey`
- الكود يبحث عن `enrollments_student_id_fkey`

**الحل المطبق**:
```sql
-- إضافة عمود student_id كمرجع إضافي
ALTER TABLE enrollments ADD COLUMN student_id UUID REFERENCES users(id) ON DELETE CASCADE;

-- تحديث البيانات الموجودة
UPDATE enrollments SET student_id = user_id WHERE user_id IS NOT NULL;
```

**تحديث الكود**:
```javascript
// في ملف hybridDatabaseService.js
// تم تغيير:
student:users!enrollments_student_id_fkey(id, name, email, student_code)
// إلى:
student:users!enrollments_user_id_fkey(id, name, email, student_code)
```

### 2. إضافة عمود priority إلى جدول faqs

**المشكلة**: 
- الكود يحاول ترتيب الأسئلة الشائعة حسب `priority`
- العمود غير موجود في الجدول

**الحل المطبق**:
```sql
ALTER TABLE faqs ADD COLUMN priority INTEGER DEFAULT 1;
```

**النتيجة**:
- جميع الأسئلة الموجودة حصلت على أولوية افتراضية = 1
- يمكن الآن ترتيب الأسئلة حسب الأولوية
- الاستعلامات تعمل بدون أخطاء

### 3. إنشاء Storage Bucket المفقود

**المشكلة**: 
- bucket "certificates" مفقود
- الكود يحاول رفع ملفات الشهادات لكن البucket غير موجود

**الحل المطبق**:
```sql
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types) 
VALUES ('certificates', 'certificates', true, 10485760, ARRAY['application/pdf', 'image/jpeg', 'image/png']);
```

**النتيجة**:
- تم إنشاء bucket "certificates" بنجاح
- حد الحجم: 10MB
- أنواع الملفات المسموحة: PDF, JPEG, PNG
- البucket عام (public) للوصول السهل

## 📊 حالة قاعدة البيانات بعد الإصلاحات

### الجداول الموجودة (9 جداول):
- ✅ users
- ✅ courses  
- ✅ enrollments (مع عمودي user_id و student_id)
- ✅ faqs (مع عمود priority)
- ✅ course_videos
- ✅ course_documents
- ✅ student_video_progress
- ✅ document_downloads
- ✅ settings

### Storage Buckets الموجودة (4 buckets):
- ✅ course-videos (500MB, فيديوهات)
- ✅ course-documents (50MB, PDF)
- ✅ course-images (10MB, صور)
- ✅ certificates (10MB, شهادات PDF/صور)

### Foreign Key Constraints:
- ✅ enrollments_user_id_fkey: enrollments.user_id → users.id
- ✅ enrollments_course_id_fkey: enrollments.course_id → courses.id
- ✅ enrollments_student_id_fkey: enrollments.student_id → users.id (جديد)

## 🧪 اختبار النتائج

### استعلام التسجيلات مع العلاقات:
```javascript
const { data, error } = await supabase
  .from('enrollments')
  .select(`
    *,
    student:users!enrollments_user_id_fkey(id, name, email, student_code),
    course:courses!enrollments_course_id_fkey(id, title, instructor)
  `)
  .order('created_at', { ascending: false });
```
**النتيجة**: ✅ يعمل بدون أخطاء

### استعلام الأسئلة الشائعة مع الأولوية:
```javascript
const { data, error } = await supabase
  .from('faqs')
  .select('*')
  .eq('is_active', true)
  .order('priority', { ascending: false })
  .order('created_at', { ascending: false });
```
**النتيجة**: ✅ يعمل بدون أخطاء

### Storage Buckets:
```javascript
const { data, error } = await supabase.storage.listBuckets();
```
**النتيجة**: ✅ جميع الـ 4 buckets موجودة

## 🎯 التوصيات للمستقبل

1. **استخدام أسماء أعمدة متسقة**: 
   - استخدم إما `user_id` أو `student_id` بشكل ثابت
   - تجنب الخلط بين الاثنين

2. **التحقق من Schema قبل الكود**:
   - تأكد من وجود جميع الأعمدة المطلوبة قبل كتابة الاستعلامات
   - استخدم migration scripts للتغييرات

3. **اختبار Storage Buckets**:
   - تأكد من إنشاء جميع الـ buckets المطلوبة
   - اختبر رفع الملفات بعد إنشاء buckets جديدة

4. **مراقبة الأخطاء**:
   - راقب console logs للأخطاء الجديدة
   - استخدم error handling مناسب

## ✅ الخلاصة

تم حل جميع مشاكل قاعدة البيانات بنجاح:
- ✅ إصلاح العلاقات بين الجداول
- ✅ إضافة الأعمدة المفقودة  
- ✅ إنشاء Storage Buckets المطلوبة
- ✅ اختبار جميع العمليات

**النتيجة**: لوحة التحكم الإدارية يجب أن تعمل الآن بدون أخطاء 400 في قاعدة البيانات.
