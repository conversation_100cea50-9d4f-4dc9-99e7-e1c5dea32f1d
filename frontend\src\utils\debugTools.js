// أدوات التشخيص الآمنة - SKILLS WORLD ACADEMY
// متاحة فقط في بيئة التطوير

class DebugTools {
  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development';
    if (this.isEnabled) {
      console.log('🛠️ أدوات التشخيص مفعلة');
      window.debugTools = this;
    }
  }

  // فحص حالة النظام
  async systemCheck() {
    if (!this.isEnabled) {
      console.warn('⚠️ أدوات التشخيص متاحة فقط في بيئة التطوير');
      return;
    }

    console.log('🔍 فحص حالة النظام...');
    
    // فحص Firebase
    try {
      const { auth, db } = await import('../firebase');
      console.log('✅ Firebase متصل:', { 
        auth: !!auth, 
        db: !!db,
        projectId: db?.app?.options?.projectId 
      });
    } catch (error) {
      console.error('❌ خطأ في Firebase:', error);
    }

    // فحص Supabase
    try {
      const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
      const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;
      console.log('✅ Supabase متصل:', { 
        url: !!supabaseUrl, 
        key: !!supabaseKey?.substring(0, 20) + '...' 
      });
    } catch (error) {
      console.error('❌ خطأ في Supabase:', error);
    }

    // فحص متغيرات البيئة
    const envVars = {
      NODE_ENV: process.env.NODE_ENV,
      REACT_APP_FIREBASE_PROJECT_ID: process.env.REACT_APP_FIREBASE_PROJECT_ID,
      REACT_APP_TITLE: process.env.REACT_APP_TITLE,
      REACT_APP_VERSION: process.env.REACT_APP_VERSION
    };
    console.log('🔧 متغيرات البيئة:', envVars);
  }

  // فحص بيانات الطلاب
  async checkStudentData() {
    if (!this.isEnabled) return;
    
    console.log('👥 فحص بيانات الطلاب...');
    // يمكن إضافة منطق فحص البيانات هنا
    console.log('ℹ️ استخدم هذه الدالة لفحص بيانات الطلاب في بيئة التطوير');
  }

  // إعداد سريع للبيانات التجريبية
  async quickSetup() {
    if (!this.isEnabled) return;
    
    console.log('⚡ إعداد سريع للبيانات التجريبية...');
    console.log('ℹ️ هذه الدالة آمنة ولا تؤثر على بيانات الإنتاج');
  }

  // عرض معلومات المساعدة
  help() {
    if (!this.isEnabled) {
      console.log('أدوات التشخيص متاحة فقط في بيئة التطوير');
      return;
    }

    console.log(`
🛠️ أدوات التشخيص - SKILLS WORLD ACADEMY

الأوامر المتاحة:
• debugTools.systemCheck() - فحص حالة النظام
• debugTools.checkStudentData() - فحص بيانات الطلاب  
• debugTools.quickSetup() - إعداد بيانات تجريبية
• debugTools.help() - عرض هذه المساعدة

ملاحظة: هذه الأدوات متاحة فقط في بيئة التطوير
    `);
  }
}

// إنشاء مثيل واحد
const debugTools = new DebugTools();

export default debugTools;
