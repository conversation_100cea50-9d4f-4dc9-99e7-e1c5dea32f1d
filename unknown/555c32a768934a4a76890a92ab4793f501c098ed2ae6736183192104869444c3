-- قاعدة بيانات منصة كورسات علاء عبد الحميد
-- Skills World Academy Database Schema

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS skills_world_academy 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE skills_world_academy;

-- جد<PERSON><PERSON> المستخدمين (المدراء والطلاب)
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    role ENUM('admin', 'student') NOT NULL,
    student_code VARCHAR(6) UNIQUE NULL,
    password_hash VARCHAR(255) NULL,
    avatar_url TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_role (role),
    INDEX idx_student_code (student_code),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- جدول الكورسات
CREATE TABLE courses (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    level ENUM('مبتدئ', 'متوسط', 'متقدم') DEFAULT 'مبتدئ',
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    enrolled_students INT DEFAULT 0,
    total_videos INT DEFAULT 0,
    total_duration_minutes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_instructor (instructor),
    INDEX idx_level (level)
);

-- جدول فيديوهات الكورسات
CREATE TABLE course_videos (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    course_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT,
    duration_minutes INT DEFAULT 0,
    video_order INT NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_course_order (course_id, video_order),
    INDEX idx_course (course_id)
);

-- جدول تسجيل الطلاب في الكورسات
CREATE TABLE enrollments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    completed_videos JSON,
    last_watched_video_id VARCHAR(36) NULL,
    total_watch_time_minutes INT DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_completed (is_completed)
);

-- جدول تقدم مشاهدة الفيديوهات
CREATE TABLE video_progress (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    student_id VARCHAR(36) NOT NULL,
    video_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    watch_time_seconds INT DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    last_position_seconds INT DEFAULT 0,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES course_videos(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_progress (student_id, video_id),
    INDEX idx_student_course (student_id, course_id),
    INDEX idx_video (video_id)
);

-- جدول الشهادات
CREATE TABLE certificates (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    student_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    certificate_url TEXT,
    is_valid BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_certificate (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_number (certificate_number)
);

-- جدول الأسئلة الشائعة
CREATE TABLE faqs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_active (is_active),
    INDEX idx_order (display_order)
);

-- جدول الدردشة والدعم
CREATE TABLE chat_messages (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    sender_id VARCHAR(36) NOT NULL,
    receiver_id VARCHAR(36) NULL,
    message TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file') DEFAULT 'text',
    file_url TEXT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_unread (is_read, receiver_id)
);

-- جدول الأنشطة والسجلات
CREATE TABLE activity_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    metadata JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- جدول الإعدادات العامة
CREATE TABLE settings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_unread (is_read, user_id),
    INDEX idx_created_at (created_at)
);

-- إدراج البيانات الأساسية
INSERT INTO settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('academy_name', 'SKILLS WORLD ACADEMY', 'string', 'اسم الأكاديمية', TRUE),
('admin_name', 'علاء عبد الحميد', 'string', 'اسم المدير', TRUE),
('admin_email', 'ALAA <EMAIL>', 'string', 'بريد المدير', FALSE),
('admin_phone', '0506747770', 'string', 'هاتف المدير', FALSE),
('academy_version', '1.0.0', 'string', 'إصدار النظام', TRUE),
('max_students_per_course', '1000', 'number', 'الحد الأقصى للطلاب في الكورس', FALSE),
('certificate_template_url', '', 'string', 'رابط قالب الشهادة', FALSE);

-- إنشاء المدير الافتراضي
INSERT INTO users (name, email, phone, role, is_active) VALUES
('علاء عبد الحميد', '<EMAIL>', '0506747770', 'admin', TRUE);

-- إنشاء أسئلة شائعة أساسية
INSERT INTO faqs (question, answer, category, display_order, is_active) VALUES
('كيف يمكنني التسجيل في الكورسات؟', 'يمكنك التسجيل من خلال كود الطالب الذي يوفره لك المدير', 'التسجيل', 1, TRUE),
('هل يمكنني مشاهدة الكورسات أكثر من مرة؟', 'نعم، يمكنك مشاهدة الكورسات عدد غير محدود من المرات', 'المشاهدة', 2, TRUE),
('كيف أحصل على شهادة إتمام الكورس؟', 'ستحصل على الشهادة تلقائياً بعد إتمام جميع دروس الكورس', 'الشهادات', 3, TRUE),
('ماذا أفعل إذا نسيت كود الطالب؟', 'تواصل مع المدير للحصول على كود الطالب الخاص بك', 'الدعم', 4, TRUE),
('هل يمكنني تحميل الفيديوهات؟', 'لا، الفيديوهات متاحة للمشاهدة عبر الإنترنت فقط', 'المشاهدة', 5, TRUE);

-- إنشاء كورس تجريبي
INSERT INTO courses (title, description, instructor, duration, level, is_active, total_videos) VALUES
('مقدمة في التسويق الرقمي', 'تعلم أساسيات التسويق الرقمي والوصول للعملاء المستهدفين', 'علاء عبد الحميد', '4 ساعات', 'مبتدئ', TRUE, 3);

-- الحصول على معرف الكورس المُدرج
SET @course_id = (SELECT id FROM courses WHERE title = 'مقدمة في التسويق الرقمي' LIMIT 1);

-- إدراج فيديوهات الكورس التجريبي
INSERT INTO course_videos (course_id, title, description, duration_minutes, video_order, is_free) VALUES
(@course_id, 'مقدمة في التسويق الرقمي', 'نظرة عامة على التسويق الرقمي وأهميته', 15, 1, TRUE),
(@course_id, 'استراتيجيات التسويق', 'تعلم أهم استراتيجيات التسويق الرقمي', 23, 2, FALSE),
(@course_id, 'قياس النتائج', 'كيفية قياس نجاح حملاتك التسويقية', 18, 3, FALSE);
