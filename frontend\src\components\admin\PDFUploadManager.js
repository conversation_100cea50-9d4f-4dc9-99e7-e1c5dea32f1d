/**
 * مدير رفع ملفات PDF - SKILLS WORLD ACADEMY
 * PDF Upload Manager
 */

import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  CloudUpload,
  PictureAsPdf,
  Cancel,
  CheckCircle,
  Error,
  Delete,
  Download,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import fileUploadService, { SUPPORTED_FILE_TYPES } from '../../services/fileUploadService';

const PDFUploadManager = ({ 
  open, 
  onClose, 
  courseId, 
  onPDFUploaded,
  existingPDFs = [] 
}) => {
  const [uploadQueue, setUploadQueue] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [isUploading, setIsUploading] = useState(false);

  /**
   * معالج إسقاط الملفات
   */
  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // معالجة الملفات المرفوضة
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach(error => {
        if (error.code === 'file-too-large') {
          toast.error(`الملف ${file.name} كبير جداً. الحد الأقصى 50MB`);
        } else if (error.code === 'file-invalid-type') {
          toast.error(`نوع الملف ${file.name} غير مدعوم. يُسمح فقط بملفات PDF`);
        }
      });
    });

    // إضافة الملفات المقبولة إلى قائمة الانتظار
    const newFiles = acceptedFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      status: 'pending', // pending, uploading, completed, error
      progress: 0,
      metadata: {
        title: file.name.replace(/\.[^/.]+$/, ""), // إزالة الامتداد
        description: '',
        isPublic: true,
        isDownloadable: true,
        category: 'material', // material, assignment, reference
        order: existingPDFs.length + uploadQueue.length + 1
      }
    }));

    setUploadQueue(prev => [...prev, ...newFiles]);
  }, [existingPDFs.length, uploadQueue.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxSize: SUPPORTED_FILE_TYPES.PDF.maxSize,
    multiple: true
  });

  /**
   * تحديث معلومات PDF
   */
  const updatePDFMetadata = (fileId, field, value) => {
    setUploadQueue(prev => 
      prev.map(item => 
        item.id === fileId 
          ? { ...item, metadata: { ...item.metadata, [field]: value } }
          : item
      )
    );
  };

  /**
   * إزالة PDF من القائمة
   */
  const removeFromQueue = (fileId) => {
    setUploadQueue(prev => prev.filter(item => item.id !== fileId));
    
    // إلغاء الرفع إذا كان جارياً
    const item = uploadQueue.find(item => item.id === fileId);
    if (item && item.status === 'uploading') {
      fileUploadService.cancelUpload(item.uploadPath);
    }
  };

  /**
   * رفع PDF واحد
   */
  const uploadSinglePDF = async (queueItem) => {
    try {
      // تحديث حالة الرفع
      setUploadQueue(prev => 
        prev.map(item => 
          item.id === queueItem.id 
            ? { ...item, status: 'uploading' }
            : item
        )
      );

      // رفع PDF
      const result = await fileUploadService.uploadCoursePDF(
        queueItem.file,
        courseId,
        (progress) => {
          setUploadProgress(prev => ({
            ...prev,
            [queueItem.id]: progress
          }));
        }
      );

      // تحديث حالة النجاح
      setUploadQueue(prev => 
        prev.map(item => 
          item.id === queueItem.id 
            ? { 
                ...item, 
                status: 'completed',
                uploadResult: result,
                uploadPath: result.path
              }
            : item
        )
      );

      // إشعار المكون الأب
      if (onPDFUploaded) {
        onPDFUploaded({
          ...queueItem.metadata,
          pdfUrl: result.url,
          backupUrl: result.backupUrl,
          fileName: result.fileName,
          fileSize: result.size,
          uploadPath: result.path
        });
      }

      toast.success(`تم رفع الملف: ${queueItem.metadata.title}`);

    } catch (error) {
      console.error('خطأ في رفع PDF:', error);
      
      setUploadQueue(prev => 
        prev.map(item => 
          item.id === queueItem.id 
            ? { ...item, status: 'error', error: error.message }
            : item
        )
      );

      toast.error(`فشل في رفع الملف: ${error.message}`);
    }
  };

  /**
   * رفع جميع ملفات PDF
   */
  const uploadAllPDFs = async () => {
    setIsUploading(true);
    
    const pendingPDFs = uploadQueue.filter(item => item.status === 'pending');
    
    if (pendingPDFs.length === 0) {
      toast.error('لا توجد ملفات للرفع');
      setIsUploading(false);
      return;
    }

    // التحقق من وجود عناوين للملفات
    const pdfsWithoutTitles = pendingPDFs.filter(item => !item.metadata.title.trim());
    if (pdfsWithoutTitles.length > 0) {
      toast.error('يرجى إدخال عناوين لجميع الملفات');
      setIsUploading(false);
      return;
    }

    try {
      // رفع الملفات بشكل متتالي
      for (const pdf of pendingPDFs) {
        await uploadSinglePDF(pdf);
      }
      
      toast.success('تم رفع جميع الملفات بنجاح!');
    } catch (error) {
      console.error('خطأ في رفع الملفات:', error);
    } finally {
      setIsUploading(false);
    }
  };

  /**
   * تنسيق حجم الملف
   */
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * الحصول على لون الحالة
   */
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'default';
      case 'uploading': return 'primary';
      case 'completed': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  /**
   * الحصول على أيقونة الحالة
   */
  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <PictureAsPdf />;
      case 'uploading': return <CloudUpload />;
      case 'completed': return <CheckCircle />;
      case 'error': return <Error />;
      default: return <PictureAsPdf />;
    }
  };

  /**
   * الحصول على تسمية الفئة
   */
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'material': return 'مادة تعليمية';
      case 'assignment': return 'واجب';
      case 'reference': return 'مرجع';
      default: return 'مادة تعليمية';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle sx={{ 
        background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <PictureAsPdf />
        مدير رفع ملفات PDF
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* منطقة إسقاط الملفات */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box
              {...getRootProps()}
              sx={{
                border: '2px dashed',
                borderColor: isDragActive ? 'error.main' : 'grey.300',
                borderRadius: 2,
                p: 4,
                textAlign: 'center',
                cursor: 'pointer',
                bgcolor: isDragActive ? 'action.hover' : 'transparent',
                transition: 'all 0.3s ease'
              }}
            >
              <input {...getInputProps()} />
              <PictureAsPdf sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {isDragActive ? 'أفلت ملفات PDF هنا...' : 'اسحب وأفلت ملفات PDF هنا'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                أو انقر لاختيار الملفات
              </Typography>
              <Typography variant="caption" color="text.secondary">
                نوع الملف المدعوم: PDF فقط
                <br />
                الحد الأقصى: 50MB لكل ملف
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* قائمة ملفات PDF */}
        {uploadQueue.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                قائمة ملفات PDF ({uploadQueue.length})
              </Typography>
              
              <List>
                {uploadQueue.map((item, index) => (
                  <React.Fragment key={item.id}>
                    <ListItem sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
                      {/* معلومات الملف الأساسية */}
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                          {getStatusIcon(item.status)}
                          <Box sx={{ ml: 2, flex: 1 }}>
                            <Typography variant="subtitle1">
                              {item.file.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatFileSize(item.file.size)}
                            </Typography>
                          </Box>
                        </Box>
                        
                        <Chip 
                          label={item.status === 'pending' ? 'في الانتظار' : 
                                item.status === 'uploading' ? 'جاري الرفع' :
                                item.status === 'completed' ? 'مكتمل' : 'خطأ'}
                          color={getStatusColor(item.status)}
                          size="small"
                          sx={{ mr: 1 }}
                        />
                        
                        <IconButton 
                          onClick={() => removeFromQueue(item.id)}
                          disabled={item.status === 'uploading'}
                          size="small"
                        >
                          <Delete />
                        </IconButton>
                      </Box>

                      {/* شريط التقدم */}
                      {item.status === 'uploading' && (
                        <Box sx={{ width: '100%', mb: 2 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={uploadProgress[item.id] || 0}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {Math.round(uploadProgress[item.id] || 0)}%
                          </Typography>
                        </Box>
                      )}

                      {/* معلومات الملف */}
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="عنوان الملف *"
                            value={item.metadata.title}
                            onChange={(e) => updatePDFMetadata(item.id, 'title', e.target.value)}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <TextField
                            fullWidth
                            select
                            label="فئة الملف"
                            value={item.metadata.category}
                            onChange={(e) => updatePDFMetadata(item.id, 'category', e.target.value)}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                            SelectProps={{ native: true }}
                          >
                            <option value="material">مادة تعليمية</option>
                            <option value="assignment">واجب</option>
                            <option value="reference">مرجع</option>
                          </TextField>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <TextField
                            fullWidth
                            type="number"
                            label="ترتيب الملف"
                            value={item.metadata.order}
                            onChange={(e) => updatePDFMetadata(item.id, 'order', parseInt(e.target.value))}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            multiline
                            rows={2}
                            label="وصف الملف"
                            value={item.metadata.description}
                            onChange={(e) => updatePDFMetadata(item.id, 'description', e.target.value)}
                            disabled={item.status === 'uploading' || item.status === 'completed'}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={item.metadata.isPublic}
                                  onChange={(e) => updatePDFMetadata(item.id, 'isPublic', e.target.checked)}
                                  disabled={item.status === 'uploading' || item.status === 'completed'}
                                />
                              }
                              label="متاح للطلاب"
                            />
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={item.metadata.isDownloadable}
                                  onChange={(e) => updatePDFMetadata(item.id, 'isDownloadable', e.target.checked)}
                                  disabled={item.status === 'uploading' || item.status === 'completed'}
                                />
                              }
                              label="قابل للتحميل"
                            />
                          </Box>
                        </Grid>
                      </Grid>

                      {/* رسالة الخطأ */}
                      {item.status === 'error' && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                          {item.error}
                        </Alert>
                      )}
                    </ListItem>
                    
                    {index < uploadQueue.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 2 }}>
        <Button onClick={onClose} disabled={isUploading}>
          إلغاء
        </Button>
        <Button
          variant="contained"
          onClick={uploadAllPDFs}
          disabled={uploadQueue.length === 0 || isUploading}
          startIcon={<CloudUpload />}
          sx={{
            background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #c0392b 0%, #a93226 100%)'
            }
          }}
        >
          {isUploading ? 'جاري الرفع...' : 'رفع جميع الملفات'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PDFUploadManager;
