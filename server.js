const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');
const studentRoutes = require('./routes/student');

// Import models
const User = require('./models/User');
const Course = require('./models/Course');
const Category = require('./models/Category');
const Certificate = require('./models/Certificate');
const Progress = require('./models/Progress');

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'"]
    }
  }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
  }
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? process.env.FRONTEND_URL 
    : ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Database connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/alaa-courses';
    
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // Initialize database with default data if empty
    await initializeDatabase();
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    process.exit(1);
  }
};

// Initialize database with default data
const initializeDatabase = async () => {
  try {
    // Check if admin user exists
    const adminExists = await User.findOne({ role: 'admin' });

    if (!adminExists) {
      console.log('🔧 تهيئة قاعدة البيانات بالبيانات الافتراضية...');

      // Create admin user
      const adminUser = new User({
        name: 'علاء عبد الحميد',
        email: '<EMAIL>',
        password: 'Admin123!',
        role: 'admin',
        isActive: true
      });
      await adminUser.save();
      console.log('👨‍💼 تم إنشاء حساب المدير الافتراضي');

      // Create default categories
      const categories = [
        {
          name: 'التسويق الرقمي',
          description: 'تعلم أساسيات وتقنيات التسويق الرقمي الحديثة',
          icon: 'digital-marketing',
          color: '#2196F3',
          order: 1,
          isActive: true,
          createdBy: adminUser._id
        },
        {
          name: 'وسائل التواصل الاجتماعي',
          description: 'استراتيجيات التسويق عبر منصات التواصل الاجتماعي',
          icon: 'social-media',
          color: '#4CAF50',
          order: 2,
          isActive: true,
          createdBy: adminUser._id
        },
        {
          name: 'التسويق بالمحتوى',
          description: 'إنشاء وتسويق المحتوى الفعال',
          icon: 'content-marketing',
          color: '#FF9800',
          order: 3,
          isActive: true,
          createdBy: adminUser._id
        }
      ];

      await Category.insertMany(categories);
      console.log('📂 تم إنشاء الأقسام الافتراضية');

      // Create sample students
      const sampleStudents = [
        {
          name: 'أحمد محمد علي',
          studentCode: '123456',
          role: 'student',
          isActive: true,
          createdBy: adminUser._id
        },
        {
          name: 'فاطمة علي حسن',
          studentCode: '789012',
          role: 'student',
          isActive: true,
          createdBy: adminUser._id
        }
      ];

      await User.insertMany(sampleStudents);
      console.log('👥 تم إنشاء الطلاب التجريبيين');

      console.log('✅ تم تهيئة قاعدة البيانات بنجاح!');
    } else {
      console.log('ℹ️ قاعدة البيانات مهيأة بالفعل');
    }
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error.message);
  }
};

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/student', studentRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'الخادم يعمل بنجاح',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Serve frontend in production
if (process.env.NODE_ENV === 'production') {
  // Serve static files from React build
  app.use(express.static(path.join(__dirname, 'frontend/build')));
  
  // Handle React routing, return all requests to React app
  app.get('*', (req, res) => {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(path.join(__dirname, 'frontend/build', 'index.html'));
  });
} else {
  // Development welcome page
  app.get('/', (req, res) => {
    res.json({
      message: 'مرحباً بك في منصة كورسات علاء عبد الحميد',
      version: '1.0.0',
      environment: 'development',
      endpoints: {
        health: '/api/health',
        auth: '/api/auth',
        admin: '/api/admin',
        student: '/api/student'
      },
      defaultCredentials: {
        admin: {
          email: '<EMAIL>',
          password: 'Admin123!'
        },
        student: {
          codes: ['123456', '789012']
        }
      }
    });
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err.stack);
  
  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'حدث خطأ في الخادم' 
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    path: req.originalUrl
  });
});

// Start server
const startServer = async () => {
  await connectDB();
  
  app.listen(PORT, () => {
    console.log('🚀 ═══════════════════════════════════════════════════════════');
    console.log(`🎓 منصة كورسات علاء عبد الحميد`);
    console.log(`🌐 الخادم يعمل على المنفذ: ${PORT}`);
    console.log(`📱 البيئة: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 الرابط: http://localhost:${PORT}`);
    console.log(`📊 API: http://localhost:${PORT}/api/health`);
    console.log('👨‍💼 بيانات المدير: <EMAIL> / Admin123!');
    console.log('👨‍🎓 أكواد الطلاب التجريبية: 123456, 789012');
    console.log('🚀 ═══════════════════════════════════════════════════════════');
  });
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.error('Unhandled Promise Rejection:', err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err.message);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  mongoose.connection.close(() => {
    console.log('Database connection closed.');
    process.exit(0);
  });
});

startServer();
