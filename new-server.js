const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

// بيانات تجريبية
const admin = {
  _id: 'admin1',
  email: '<EMAIL>',
  password: 'Admin123!',
  name: 'علاء عبد الحميد',
  role: 'admin'
};

let students = [
  {
    _id: 'student1',
    studentCode: '123456',
    name: 'أحمد محمد',
    email: '<EMAIL>',
    isActive: true,
    enrolledCourses: ['course1'],
    certificates: [],
    progress: {
      'course1': {
        completedVideos: 2,
        totalVideos: 5,
        progress: 40
      }
    },
    createdAt: new Date().toISOString()
  }
];

let courses = [
  {
    _id: 'course1',
    title: 'أساسيات التسويق الرقمي',
    description: 'تعلم أساسيات التسويق الرقمي من الصفر',
    instructor: 'علاء عبد الحميد',
    isActive: true,
    enrolledStudents: ['student1'],
    videos: [
      { id: 1, title: 'مقدمة في التسويق الرقمي', duration: '10:30' },
      { id: 2, title: 'استراتيجيات التسويق', duration: '15:45' },
      { id: 3, title: 'وسائل التواصل الاجتماعي', duration: '12:20' },
      { id: 4, title: 'التسويق عبر البريد الإلكتروني', duration: '18:10' },
      { id: 5, title: 'تحليل النتائج', duration: '14:55' }
    ],
    createdAt: new Date().toISOString()
  },
  {
    _id: 'course2',
    title: 'إدارة وسائل التواصل الاجتماعي',
    description: 'تعلم كيفية إدارة حسابات وسائل التواصل الاجتماعي بفعالية',
    instructor: 'علاء عبد الحميد',
    isActive: true,
    enrolledStudents: [],
    videos: [
      { id: 1, title: 'مقدمة في وسائل التواصل', duration: '8:30' },
      { id: 2, title: 'استراتيجية المحتوى', duration: '16:20' },
      { id: 3, title: 'التفاعل مع الجمهور', duration: '11:45' }
    ],
    createdAt: new Date().toISOString()
  }
];

// دالة لتوليد كود طالب فريد
function generateStudentCode() {
  let code;
  do {
    code = Math.floor(100000 + Math.random() * 900000).toString();
  } while (students.find(s => s.studentCode === code));
  return code;
}

// دالة لتوليد ID فريد
function generateId() {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

// Routes
app.get('/api/test', (req, res) => {
  res.json({ message: 'الخادم يعمل بنجاح!' });
});

// تسجيل دخول المدير
app.post('/api/admin/login', (req, res) => {
  const { email, password } = req.body;
  console.log('🔐 طلب تسجيل دخول مدير:', { email });

  if (email === admin.email && password === admin.password) {
    res.json({
      token: 'fake-admin-token',
      user: {
        _id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role
      }
    });
  } else {
    res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
  }
});

// تسجيل دخول الطالب
app.post('/api/student/login', (req, res) => {
  const { code } = req.body;
  console.log('🎓 طلب تسجيل دخول طالب:', { code });

  const student = students.find(s => s.studentCode === code && s.isActive);
  if (student) {
    res.json({
      token: 'fake-student-token',
      user: {
        _id: student._id,
        name: student.name,
        studentCode: student.studentCode,
        role: 'student'
      }
    });
  } else {
    res.status(401).json({ message: 'كود الطالب غير صحيح أو الحساب غير مفعل' });
  }
});

// التحقق من المصادقة
app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (token === 'fake-admin-token') {
    res.json({ user: { ...admin, role: 'admin' } });
  } else if (token === 'fake-student-token') {
    const student = students[0]; // الطالب التجريبي
    res.json({ 
      user: { 
        _id: student._id, 
        name: student.name, 
        studentCode: student.studentCode, 
        role: 'student' 
      } 
    });
  } else {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// جلب الكورسات (للمدير)
app.get('/api/admin/courses', (req, res) => {
  console.log('📚 طلب جلب الكورسات من المدير');
  res.json(courses);
});

// إضافة كورس جديد
app.post('/api/admin/courses', (req, res) => {
  console.log('➕ إضافة كورس جديد:', req.body);
  
  const newCourse = {
    _id: generateId(),
    ...req.body,
    enrolledStudents: [],
    videos: [],
    createdAt: new Date().toISOString()
  };
  
  courses.push(newCourse);
  res.json(newCourse);
});

// تحديث كورس
app.put('/api/admin/courses/:id', (req, res) => {
  const courseId = req.params.id;
  console.log('✏️ تحديث كورس:', courseId, req.body);
  
  const courseIndex = courses.findIndex(c => c._id === courseId);
  if (courseIndex !== -1) {
    courses[courseIndex] = { ...courses[courseIndex], ...req.body };
    res.json(courses[courseIndex]);
  } else {
    res.status(404).json({ message: 'الكورس غير موجود' });
  }
});

// حذف كورس
app.delete('/api/admin/courses/:id', (req, res) => {
  const courseId = req.params.id;
  console.log('🗑️ حذف كورس:', courseId);
  
  const courseIndex = courses.findIndex(c => c._id === courseId);
  if (courseIndex !== -1) {
    courses.splice(courseIndex, 1);
    res.json({ message: 'تم حذف الكورس بنجاح' });
  } else {
    res.status(404).json({ message: 'الكورس غير موجود' });
  }
});

// جلب الطلاب (للمدير)
app.get('/api/admin/students', (req, res) => {
  console.log('👥 طلب جلب الطلاب من المدير');
  
  const studentsWithCourseNames = students.map(student => ({
    ...student,
    enrolledCoursesNames: student.enrolledCourses.map(courseId => {
      const course = courses.find(c => c._id === courseId);
      return course ? course.title : 'كورس محذوف';
    })
  }));
  
  res.json(studentsWithCourseNames);
});

// إضافة طالب جديد
app.post('/api/admin/students', (req, res) => {
  console.log('➕ إضافة طالب جديد:', req.body);
  
  const studentCode = generateStudentCode();
  const newStudent = {
    _id: generateId(),
    studentCode,
    name: req.body.name,
    email: req.body.email || '',
    isActive: true,
    enrolledCourses: [],
    certificates: [],
    progress: {},
    createdAt: new Date().toISOString()
  };
  
  students.push(newStudent);
  res.json({ ...newStudent, enrolledCoursesNames: [] });
});

// تحديث طالب
app.put('/api/admin/students/:id', (req, res) => {
  const studentId = req.params.id;
  console.log('✏️ تحديث طالب:', studentId, req.body);
  
  const studentIndex = students.findIndex(s => s._id === studentId);
  if (studentIndex !== -1) {
    students[studentIndex] = { ...students[studentIndex], ...req.body };
    
    const updatedStudent = {
      ...students[studentIndex],
      enrolledCoursesNames: students[studentIndex].enrolledCourses.map(courseId => {
        const course = courses.find(c => c._id === courseId);
        return course ? course.title : 'كورس محذوف';
      })
    };
    
    res.json(updatedStudent);
  } else {
    res.status(404).json({ message: 'الطالب غير موجود' });
  }
});

// حذف طالب
app.delete('/api/admin/students/:id', (req, res) => {
  const studentId = req.params.id;
  console.log('🗑️ حذف طالب:', studentId);
  
  const studentIndex = students.findIndex(s => s._id === studentId);
  if (studentIndex !== -1) {
    students.splice(studentIndex, 1);
    res.json({ message: 'تم حذف الطالب بنجاح' });
  } else {
    res.status(404).json({ message: 'الطالب غير موجود' });
  }
});

// إحصائيات لوحة التحكم
app.get('/api/admin/dashboard-stats', (req, res) => {
  console.log('📊 طلب إحصائيات لوحة التحكم');

  const totalVideos = courses.reduce((total, course) => total + course.videos.length, 0);
  const totalEnrollments = students.reduce((total, student) => total + student.enrolledCourses.length, 0);

  res.json({
    totalStudents: students.length,
    activeStudents: students.filter(s => s.isActive).length,
    totalCourses: courses.length,
    activeCourses: courses.filter(c => c.isActive).length,
    totalVideos,
    totalCertificates: totalEnrollments
  });
});

// جلب كورسات الطالب
app.get('/api/student/courses', (req, res) => {
  console.log('📚 طلب جلب كورسات الطالب');

  const studentCourses = courses.filter(course =>
    course.enrolledStudents.includes('student1')
  ).map(course => ({
    ...course,
    progress: { completedVideos: 2, totalVideos: course.videos.length, progress: 40 }
  }));

  res.json(studentCourses);
});

// تسجيل طالب في كورس
app.post('/api/admin/courses/:courseId/enroll/:studentId', (req, res) => {
  const { courseId, studentId } = req.params;
  console.log('📝 تسجيل طالب في كورس:', { courseId, studentId });

  const course = courses.find(c => c._id === courseId);
  const student = students.find(s => s._id === studentId);

  if (!course || !student) {
    return res.status(404).json({ message: 'الكورس أو الطالب غير موجود' });
  }

  if (!course.enrolledStudents.includes(studentId)) {
    course.enrolledStudents.push(studentId);
  }

  if (!student.enrolledCourses.includes(courseId)) {
    student.enrolledCourses.push(courseId);
    student.progress[courseId] = {
      completedVideos: 0,
      totalVideos: course.videos.length,
      progress: 0
    };
  }

  res.json({ message: 'تم تسجيل الطالب في الكورس بنجاح' });
});

// إلغاء تسجيل طالب من كورس
app.delete('/api/admin/courses/:courseId/unenroll/:studentId', (req, res) => {
  const { courseId, studentId } = req.params;
  console.log('❌ إلغاء تسجيل طالب من كورس:', { courseId, studentId });

  const course = courses.find(c => c._id === courseId);
  const student = students.find(s => s._id === studentId);

  if (!course || !student) {
    return res.status(404).json({ message: 'الكورس أو الطالب غير موجود' });
  }

  course.enrolledStudents = course.enrolledStudents.filter(id => id !== studentId);
  student.enrolledCourses = student.enrolledCourses.filter(id => id !== courseId);
  delete student.progress[courseId];

  res.json({ message: 'تم إلغاء تسجيل الطالب من الكورس بنجاح' });
});

// إضافة فيديو لكورس
app.post('/api/admin/courses/:courseId/videos', (req, res) => {
  const courseId = req.params.courseId;
  console.log('🎥 إضافة فيديو لكورس:', courseId, req.body);

  const course = courses.find(c => c._id === courseId);
  if (!course) {
    return res.status(404).json({ message: 'الكورس غير موجود' });
  }

  const newVideo = {
    id: course.videos.length + 1,
    ...req.body
  };

  course.videos.push(newVideo);

  // تحديث progress للطلاب المسجلين
  course.enrolledStudents.forEach(studentId => {
    const student = students.find(s => s._id === studentId);
    if (student && student.progress[courseId]) {
      student.progress[courseId].totalVideos = course.videos.length;
      student.progress[courseId].progress = Math.round(
        (student.progress[courseId].completedVideos / course.videos.length) * 100
      );
    }
  });

  res.json(newVideo);
});

// رفع شهادة للطالب
app.post('/api/admin/students/:studentId/certificate', (req, res) => {
  const studentId = req.params.studentId;
  const { courseId, certificateName, description } = req.body;

  console.log('🏆 رفع شهادة للطالب:', { studentId, courseId, certificateName });

  const student = students.find(s => s._id === studentId);
  const course = courses.find(c => c._id === courseId);

  if (!student || !course) {
    return res.status(404).json({ message: 'الطالب أو الكورس غير موجود' });
  }

  const certificate = {
    id: generateId(),
    courseId,
    courseName: course.title,
    certificateName: certificateName || `شهادة إتمام ${course.title}`,
    description: description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`,
    issuedDate: new Date().toISOString(),
    issuedBy: admin.name
  };

  if (!student.certificates) {
    student.certificates = [];
  }

  student.certificates.push(certificate);

  res.json({ message: 'تم رفع الشهادة بنجاح', certificate });
});

// جلب شهادات الطالب
app.get('/api/student/certificates', (req, res) => {
  console.log('🏆 طلب جلب شهادات الطالب');

  const student = students[0]; // الطالب التجريبي
  res.json(student.certificates || []);
});

app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الموقع متاح على: http://localhost:${PORT}`);
  console.log(`📱 API متاح على: http://localhost:${PORT}/api/test`);
  console.log(`👨‍💼 بيانات المدير: <EMAIL> / Admin123!`);
  console.log(`👨‍🎓 كود طالب تجريبي: 123456`);
});
