/* تحسينات شاملة للأجهزة اللوحية */

/* تحسينات الشريط العلوي للشاشات الكبيرة */
@media (min-width: 1200px) {
  .admin-dashboard .MuiAppBar-root {
    height: 64px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 255, 0.15) !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 64px !important;
    padding: 0 32px !important;
    justify-content: space-between !important;
  }

  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.4rem !important;
    font-weight: 600 !important;
  }

  .admin-dashboard .MuiIconButton-root {
    padding: 12px !important;
  }

  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.4rem !important;
  }
}

/* نقاط الكسر للأجهزة اللوحية */
@media (min-width: 900px) and (max-width: 1199px) {

  /* تحسينات عامة للوحة التحكم الإدارية */
  .admin-dashboard-tablet,
  .admin-dashboard {
    /* تحسين الخطوط */
    font-size: 16px !important;
    line-height: 1.5 !important;

    /* تحسين المسافات */
    padding: 16px !important;
    margin: 8px !important;

    /* تحسين التفاعل */
    touch-action: manipulation !important;
    -webkit-overflow-scrolling: touch !important;
  }
  
  /* تحسين الأزرار للأجهزة اللوحية */
  .admin-dashboard-tablet .MuiButton-root {
    min-height: 48px !important;
    min-width: 120px !important;
    padding: 12px 24px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    margin: 4px !important;
    
    /* تحسين اللمس */
    touch-action: manipulation !important;
    user-select: none !important;
    
    /* تأثيرات التفاعل */
    transition: all 0.2s ease !important;
  }
  
  .admin-dashboard-tablet .MuiButton-root:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 255, 0.2) !important;
  }
  
  .admin-dashboard-tablet .MuiButton-root:active {
    transform: translateY(0) scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
  
  /* تحسين عناصر القائمة */
  .admin-dashboard-tablet .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
    margin: 2px 8px !important;
    border-radius: 8px !important;
    
    /* تحسين اللمس */
    touch-action: manipulation !important;
    cursor: pointer !important;
  }
  
  .admin-dashboard-tablet .MuiListItem-root:hover {
    background-color: rgba(0, 0, 255, 0.08) !important;
    transform: translateX(-4px) !important;
  }
  
  .admin-dashboard-tablet .MuiListItem-root:active {
    background-color: rgba(0, 0, 255, 0.12) !important;
    transform: scale(0.98) !important;
  }
  
  /* تحسين الأيقونات */
  .admin-dashboard-tablet .MuiSvgIcon-root {
    font-size: 1.5rem !important;
    margin: 4px !important;
  }
  
  .admin-dashboard-tablet .MuiListItemIcon-root {
    min-width: 48px !important;
    margin-right: 12px !important;
  }
  
  /* تحسين النصوص */
  .admin-dashboard-tablet .MuiTypography-h6 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin: 8px 0 !important;
  }
  
  .admin-dashboard-tablet .MuiTypography-body1 {
    font-size: 1rem !important;
    line-height: 1.6 !important;
  }
  
  .admin-dashboard-tablet .MuiTypography-body2 {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
  }
  
  /* تحسين الجداول */
  .admin-dashboard-tablet .MuiTable-root {
    font-size: 0.95rem !important;
  }
  
  .admin-dashboard-tablet .MuiTableCell-root {
    padding: 16px 12px !important;
    font-size: 0.95rem !important;
    border-bottom: 1px solid rgba(224, 224, 224, 0.5) !important;
  }
  
  .admin-dashboard-tablet .MuiTableHead .MuiTableCell-root {
    font-weight: 600 !important;
    background-color: rgba(0, 0, 255, 0.05) !important;
  }
  
  /* تحسين الحقول والمدخلات */
  .admin-dashboard-tablet .MuiTextField-root {
    margin: 8px 0 16px 0 !important;
    width: 100% !important;
  }
  
  .admin-dashboard-tablet .MuiInputBase-root {
    font-size: 1rem !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    min-height: 48px !important;
  }
  
  .admin-dashboard-tablet .MuiInputLabel-root {
    font-size: 1rem !important;
    font-weight: 500 !important;
  }
  
  /* تحسين البطاقات */
  .admin-dashboard-tablet .MuiCard-root {
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    margin: 12px 0 !important;
    padding: 16px !important;
  }
  
  .admin-dashboard-tablet .MuiCardContent-root {
    padding: 20px !important;
  }
  
  /* تحسين الحوارات والنوافذ المنبثقة */
  .admin-dashboard-tablet .MuiDialog-paper {
    margin: 16px !important;
    max-width: calc(100% - 32px) !important;
    border-radius: 12px !important;
  }
  
  .admin-dashboard-tablet .MuiDialogTitle-root {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    padding: 20px 24px 16px !important;
  }
  
  .admin-dashboard-tablet .MuiDialogContent-root {
    padding: 16px 24px !important;
  }
  
  .admin-dashboard-tablet .MuiDialogActions-root {
    padding: 16px 24px 20px !important;
    gap: 12px !important;
  }
  
  /* تحسين الشرائح والتبويبات */
  .admin-dashboard-tablet .MuiTabs-root {
    min-height: 56px !important;
  }
  
  .admin-dashboard-tablet .MuiTab-root {
    min-height: 56px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    min-width: 120px !important;
  }
  
  /* تحسين الشرائح المنزلقة */
  .admin-dashboard-tablet .MuiSlider-root {
    height: 8px !important;
  }
  
  .admin-dashboard-tablet .MuiSlider-thumb {
    width: 24px !important;
    height: 24px !important;
  }
  
  /* تحسين القوائم المنسدلة */
  .admin-dashboard-tablet .MuiSelect-root {
    min-height: 48px !important;
    font-size: 1rem !important;
  }
  
  .admin-dashboard-tablet .MuiMenuItem-root {
    min-height: 48px !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
  }
  
  /* تحسين الرقائق والعلامات */
  .admin-dashboard-tablet .MuiChip-root {
    height: 36px !important;
    font-size: 0.9rem !important;
    padding: 8px 12px !important;
    margin: 4px !important;
  }
  
  /* تحسين شريط التقدم */
  .admin-dashboard-tablet .MuiLinearProgress-root {
    height: 8px !important;
    border-radius: 4px !important;
  }
  
  .admin-dashboard-tablet .MuiCircularProgress-root {
    margin: 16px !important;
  }
  
  /* تحسين التنبيهات */
  .admin-dashboard-tablet .MuiAlert-root {
    font-size: 1rem !important;
    padding: 16px !important;
    margin: 12px 0 !important;
    border-radius: 8px !important;
  }
  
  /* تحسين الشبكة والتخطيط */
  .admin-dashboard-tablet .MuiGrid-container {
    margin: 0 !important;
    width: 100% !important;
  }
  
  .admin-dashboard-tablet .MuiGrid-item {
    padding: 8px !important;
  }
  
  /* تحسين التمرير */
  .admin-dashboard-tablet {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
    overscroll-behavior: contain !important;
  }
  
  /* تحسين الأداء */
  .admin-dashboard-tablet * {
    will-change: auto !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
  }
  
  /* تحسين الانتقالات */
  .admin-dashboard-tablet .MuiCollapse-root,
  .admin-dashboard-tablet .MuiFade-root,
  .admin-dashboard-tablet .MuiSlide-root {
    transition-duration: 0.3s !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

/* تحسينات خاصة للأجهزة اللوحية الكبيرة */
@media (min-width: 1024px) and (max-width: 1199px) {
  .admin-dashboard-tablet .MuiContainer-root {
    max-width: 100% !important;
    padding: 0 24px !important;
  }
  
  .admin-dashboard-tablet .MuiDrawer-paper {
    width: 300px !important;
  }
}

/* تحسينات خاصة للأجهزة اللوحية الصغيرة */
@media (min-width: 768px) and (max-width: 1023px) {
  .admin-dashboard-tablet .MuiContainer-root {
    padding: 0 16px !important;
  }
  
  .admin-dashboard-tablet .MuiDrawer-paper {
    width: 280px !important;
  }
  
  .admin-dashboard-tablet .MuiButton-root {
    font-size: 0.95rem !important;
    padding: 10px 20px !important;
  }
}

/* تحسينات RTL للوحة التحكم الإدارية */
.rtl .admin-dashboard {
  direction: rtl !important;
}

.rtl .admin-dashboard .MuiAppBar-root {
  right: 0 !important;
  left: auto !important;
}

.rtl .admin-dashboard .MuiDrawer-paper {
  right: 0 !important;
  left: auto !important;
}

.rtl .admin-dashboard .MuiToolbar-root {
  direction: rtl !important;
  text-align: right !important;
}

.rtl .admin-dashboard .MuiTypography-root {
  text-align: right !important;
}

.rtl .admin-dashboard .MuiListItem-root {
  direction: rtl !important;
  text-align: right !important;
}

.rtl .admin-dashboard .MuiListItemIcon-root {
  margin-right: 0 !important;
  margin-left: 16px !important;
}

.rtl .admin-dashboard .MuiListItemText-root {
  text-align: right !important;
}

/* تحسينات LTR للوحة التحكم الإدارية */
.ltr .admin-dashboard {
  direction: ltr !important;
}

.ltr .admin-dashboard .MuiAppBar-root {
  left: 0 !important;
  right: auto !important;
}

.ltr .admin-dashboard .MuiDrawer-paper {
  left: 0 !important;
  right: auto !important;
}

.ltr .admin-dashboard .MuiToolbar-root {
  direction: ltr !important;
  text-align: left !important;
}

.ltr .admin-dashboard .MuiTypography-root {
  text-align: left !important;
}

.ltr .admin-dashboard .MuiListItem-root {
  direction: ltr !important;
  text-align: left !important;
}

.ltr .admin-dashboard .MuiListItemIcon-root {
  margin-left: 0 !important;
  margin-right: 16px !important;
}

.ltr .admin-dashboard .MuiListItemText-root {
  text-align: left !important;
}
