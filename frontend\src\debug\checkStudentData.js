import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * فحص بيانات الطلاب في قاعدة البيانات
 */
export const checkStudentData = async () => {
  try {
    console.log('🔍 فحص بيانات الطلاب...');

    // جلب جميع المستخدمين
    const usersQuery = query(collection(db, 'users'));
    const usersSnapshot = await getDocs(usersQuery);
    
    console.log(`📊 إجمالي المستخدمين: ${usersSnapshot.size}`);
    
    // فلترة الطلاب
    const students = [];
    const admins = [];
    
    usersSnapshot.docs.forEach(doc => {
      const userData = { id: doc.id, ...doc.data() };
      if (userData.role === 'student') {
        students.push(userData);
      } else if (userData.role === 'admin') {
        admins.push(userData);
      }
    });
    
    console.log(`👨‍🎓 عدد الطلاب: ${students.length}`);
    console.log(`👨‍💼 عدد المدراء: ${admins.length}`);
    
    // عرض بيانات الطلاب
    if (students.length > 0) {
      console.log('📋 بيانات الطلاب:');
      students.forEach((student, index) => {
        console.log(`${index + 1}. الاسم: ${student.name}`);
        console.log(`   الكود: ${student.studentCode}`);
        console.log(`   نشط: ${student.isActive ? 'نعم' : 'لا'}`);
        console.log(`   تاريخ الإنشاء: ${student.createdAt?.toDate?.() || 'غير محدد'}`);
        console.log('   ---');
      });
    } else {
      console.log('⚠️ لا يوجد طلاب في قاعدة البيانات');
    }
    
    // عرض بيانات المدراء
    if (admins.length > 0) {
      console.log('📋 بيانات المدراء:');
      admins.forEach((admin, index) => {
        console.log(`${index + 1}. الاسم: ${admin.name}`);
        console.log(`   البريد: ${admin.email}`);
        console.log(`   نشط: ${admin.isActive ? 'نعم' : 'لا'}`);
        console.log('   ---');
      });
    }
    
    return { students, admins };
    
  } catch (error) {
    console.error('❌ خطأ في فحص بيانات الطلاب:', error);
    throw error;
  }
};

/**
 * فحص طالب محدد بالكود
 */
export const checkStudentByCode = async (studentCode) => {
  try {
    console.log(`🔍 البحث عن الطالب بالكود: ${studentCode}`);
    
    const studentQuery = query(
      collection(db, 'users'),
      where('studentCode', '==', studentCode),
      where('role', '==', 'student')
    );
    
    const snapshot = await getDocs(studentQuery);
    
    if (snapshot.empty) {
      console.log('❌ لم يتم العثور على طالب بهذا الكود');
      return null;
    }
    
    const studentDoc = snapshot.docs[0];
    const studentData = { id: studentDoc.id, ...studentDoc.data() };
    
    console.log('✅ تم العثور على الطالب:');
    console.log('الاسم:', studentData.name);
    console.log('الكود:', studentData.studentCode);
    console.log('نشط:', studentData.isActive ? 'نعم' : 'لا');
    console.log('الدور:', studentData.role);
    console.log('تاريخ الإنشاء:', studentData.createdAt?.toDate?.() || 'غير محدد');
    
    return studentData;
    
  } catch (error) {
    console.error('❌ خطأ في البحث عن الطالب:', error);
    throw error;
  }
};

/**
 * إنشاء طالب تجريبي للاختبار
 */
export const createTestStudent = async () => {
  try {
    const { createStudent } = await import('../firebase/studentService');
    
    const testStudentData = {
      name: 'طالب تجريبي',
      email: '<EMAIL>',
      phone: '1234567890'
    };
    
    const newStudent = await createStudent(testStudentData, 'admin');
    
    console.log('✅ تم إنشاء طالب تجريبي:');
    console.log('الاسم:', newStudent.name);
    console.log('الكود:', newStudent.studentCode);
    
    return newStudent;
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء طالب تجريبي:', error);
    throw error;
  }
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.checkStudentData = checkStudentData;
window.checkStudentByCode = checkStudentByCode;
window.createTestStudent = createTestStudent;

export default {
  checkStudentData,
  checkStudentByCode,
  createTestStudent
};
